#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频轨道写入器 - 正确创建第三视频轨道
完全按照原项目 VideoTrackWriter.java 的方式实现
基于参考实现，创建真正的视频轨道而不是文本轨道
"""

import os
import re
from datetime import datetime
from typing import List

class MatchResult:
    """匹配结果数据结构 - 按照原项目格式"""
    def __init__(self, main_segment_name: str, main_start_time: float, main_end_time: float,
                 second_segment_name: str, second_start_time: float, second_end_time: float,
                 confidence: float, method: str):
        self.main_segment_name = main_segment_name
        self.main_start_time = main_start_time
        self.main_end_time = main_end_time
        self.second_segment_name = second_segment_name
        self.second_start_time = second_start_time
        self.second_end_time = second_end_time
        self.confidence = confidence
        self.method = method
    
    def __str__(self):
        return f"主轨道[{self.main_start_time:.2f}s-{self.main_end_time:.2f}s] → 第二轨道[{self.second_start_time:.2f}s-{self.second_end_time:.2f}s] ({self.confidence*100:.1f}%, {self.method})"

class VideoTrackWriter:
    """视频轨道写入器 - 按照原项目实现"""
    
    @staticmethod
    def write_matches_to_jianying_project(project_path: str, matches: List[MatchResult]) -> bool:
        """将匹配结果写入剪映草稿的第三视频轨道 - 按照原项目逻辑"""
        print("📝 开始将匹配结果写入剪映项目（视频轨道）")
        print(f"📂 项目路径: {project_path}")
        print(f"📊 匹配结果数量: {len(matches)}")
        
        try:
            # 读取草稿文件
            draft_file = os.path.join(project_path, "draft_content.json")
            if not os.path.exists(draft_file):
                print("❌ 未找到draft_content.json文件")
                return False
            
            # 备份原始文件
            VideoTrackWriter._backup_draft_file(draft_file)
            
            # 读取JSON内容
            json_content = VideoTrackWriter._read_file_content(draft_file)
            
            # 修改JSON，添加第三视频轨道
            modified_json = VideoTrackWriter._add_third_video_track_with_matches(json_content, matches)
            
            # 写入修改后的内容
            VideoTrackWriter._write_file_content(draft_file, modified_json)
            
            print("✅ 第三视频轨道创建成功")
            print("📄 备份文件已保存")
            print("🎬 请在剪映中重新打开项目查看第三轨道")
            
            return True
            
        except Exception as e:
            print(f"❌ 写入视频轨道失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    @staticmethod
    def _backup_draft_file(original_file: str):
        """备份原始文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(
            os.path.dirname(original_file),
            f"draft_content_backup_{timestamp}.json"
        )
        
        # 复制文件
        with open(original_file, 'rb') as src, open(backup_file, 'wb') as dst:
            dst.write(src.read())
        
        print(f"💾 备份文件: {os.path.basename(backup_file)}")
    
    @staticmethod
    def _read_file_content(file_path: str) -> str:
        """读取文件内容"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    @staticmethod
    def _write_file_content(file_path: str, content: str):
        """写入文件内容"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    @staticmethod
    def _add_third_video_track_with_matches(json_content: str, matches: List[MatchResult]) -> str:
        """添加第三视频轨道和匹配结果 - 按照原项目逻辑"""
        print("🔧 开始修改JSON结构...")
        
        # 获取第二轨道的材料ID（原始素材）
        second_track_material_id = VideoTrackWriter._get_second_track_material_id(json_content)
        if not second_track_material_id:
            print("❌ 无法找到第二轨道的材料ID")
            return json_content
        
        print(f"✅ 找到第二轨道材料ID: {second_track_material_id}")
        
        # 添加第三视频轨道（使用第二轨道的材料ID，但时间戳基于匹配结果）
        json_with_third_track = VideoTrackWriter._add_third_video_track(json_content, matches, second_track_material_id)
        
        print("✅ JSON结构修改完成")
        return json_with_third_track
    
    @staticmethod
    def _get_second_track_material_id(json_content: str) -> str:
        """获取第二轨道的材料ID（原始素材）- 按照原项目逻辑"""
        try:
            print("🔍 开始查找第二轨道的材料ID...")
            
            # 查找tracks数组
            tracks_start = json_content.find('"tracks":')
            if tracks_start == -1:
                print("❌ 未找到tracks数组")
                return None
            
            # 找到tracks数组的开始
            array_start = json_content.find('[', tracks_start)
            if array_start == -1:
                print("❌ tracks数组格式错误")
                return None
            
            # 查找视频轨道
            video_track_count = 0
            pos = array_start + 1
            
            while pos < len(json_content):
                # 查找下一个轨道对象
                track_start = json_content.find('{', pos)
                if track_start == -1:
                    break
                
                # 查找轨道类型
                type_pos = json_content.find('"type":', track_start)
                if type_pos == -1:
                    break
                
                # 检查是否是视频轨道
                type_value_start = json_content.find('"', type_pos + 7)
                type_value_end = json_content.find('"', type_value_start + 1)
                track_type = json_content[type_value_start + 1:type_value_end]
                
                if track_type == "video":
                    video_track_count += 1
                    
                    # 如果是第二个视频轨道，查找其材料ID
                    if video_track_count == 2:
                        print("🎯 找到第二视频轨道，查找材料ID...")
                        
                        # 查找segments数组
                        segments_pos = json_content.find('"segments":', track_start)
                        if segments_pos != -1:
                            segments_array_start = json_content.find('[', segments_pos)
                            if segments_array_start != -1:
                                # 查找第一个segment的material_id
                                first_segment_start = json_content.find('{', segments_array_start)
                                if first_segment_start != -1:
                                    material_id_pos = json_content.find('"material_id":', first_segment_start)
                                    if material_id_pos != -1:
                                        material_id_start = json_content.find('"', material_id_pos + 14)
                                        material_id_end = json_content.find('"', material_id_start + 1)
                                        material_id = json_content[material_id_start + 1:material_id_end]
                                        print(f"✅ 找到第二轨道材料ID: {material_id}")
                                        return material_id
                        break
                
                # 移动到下一个轨道
                track_end = VideoTrackWriter._find_matching_brace(json_content, track_start)
                if track_end == -1:
                    break
                pos = track_end + 1
            
            print("❌ 未找到第二视频轨道或其材料ID")
            return None
            
        except Exception as e:
            print(f"❌ 查找第二轨道材料ID失败: {e}")
            return None
    
    @staticmethod
    def _find_matching_brace(content: str, start_pos: int) -> int:
        """找到匹配的大括号位置"""
        count = 1
        pos = start_pos + 1
        
        while pos < len(content) and count > 0:
            c = content[pos]
            if c == '{':
                count += 1
            elif c == '}':
                count -= 1
            pos += 1
        
        return pos - 1 if count == 0 else -1

    @staticmethod
    def _add_third_video_track(json_content: str, matches: List[MatchResult], material_id: str) -> str:
        """添加第三视频轨道 - 按照原项目逻辑"""
        print("🎬 添加第三视频轨道...")

        # 查找tracks数组的位置
        tracks_pos = json_content.find('"tracks":')
        if tracks_pos == -1:
            print("❌ 未找到tracks结构")
            return json_content

        # 找到tracks数组的开始和结束
        array_start = json_content.find('[', tracks_pos)
        if array_start == -1:
            print("❌ tracks数组格式错误")
            return json_content

        # 找到tracks数组的结束位置
        array_end = VideoTrackWriter._find_matching_bracket(json_content, array_start)
        if array_end == -1:
            print("❌ 无法找到tracks数组结束位置")
            return json_content

        # 生成第三视频轨道的JSON
        third_track_json = VideoTrackWriter._generate_third_video_track_json(matches, material_id)

        # 在tracks数组末尾添加第三视频轨道
        before_tracks = json_content[:array_end]
        after_tracks = json_content[array_end:]

        # 如果tracks数组不为空，需要添加逗号
        separator = ""
        tracks_content = json_content[array_start + 1:array_end].strip()
        if tracks_content and tracks_content != "":
            separator = ","

        result = before_tracks + separator + third_track_json + after_tracks
        print("✅ 第三视频轨道添加完成")
        return result

    @staticmethod
    def _find_matching_bracket(content: str, start_pos: int) -> int:
        """找到匹配的方括号位置"""
        count = 1
        pos = start_pos + 1

        while pos < len(content) and count > 0:
            c = content[pos]
            if c == '[':
                count += 1
            elif c == ']':
                count -= 1
            pos += 1

        return pos - 1 if count == 0 else -1

    @staticmethod
    def _generate_third_video_track_json(matches: List[MatchResult], material_id: str) -> str:
        """生成第三视频轨道的JSON结构 - 按照原项目逻辑"""
        print("🎬 生成第三视频轨道JSON...")

        track_json = f"""
    {{
      "attribute": 0,
      "flag": 0,
      "id": "{VideoTrackWriter._generate_uuid()}",
      "is_default_name": true,
      "name": "第三轨道",
      "segments": ["""

        # 生成匹配结果的视频片段 - 按照原项目逻辑
        segments = []
        current_timeline_position = 0.0  # 连续的时间轴位置

        for i, match in enumerate(matches):
            # 计算时间（微秒） - 按照原项目逻辑
            source_start_us = int(match.second_start_time * 1000000)
            source_duration_us = int((match.second_end_time - match.second_start_time) * 1000000)
            target_start_us = int(current_timeline_position * 1000000)  # 使用连续的时间轴位置
            target_duration_us = source_duration_us  # 目标时长等于源时长

            segment_json = f"""
        {{
          "cartoon": false,
          "clip": {{
            "alpha": 1.0,
            "flip": {{
              "horizontal": false,
              "vertical": false
            }},
            "rotation": 0.0,
            "scale": {{
              "x": 1.0,
              "y": 1.0
            }},
            "transform": {{
              "x": 0.0,
              "y": 0.0
            }}
          }},
          "common_keyframes": [],
          "enable_adjust": true,
          "enable_color_curves": true,
          "enable_color_match_adjust": false,
          "enable_color_wheels": true,
          "enable_lut": true,
          "enable_smart_color_adjust": false,
          "extra_material_refs": [],
          "group_id": "",
          "hdr_settings": {{
            "intensity": 1.0,
            "mode": 1,
            "nits": 1000
          }},
          "id": "{VideoTrackWriter._generate_uuid()}",
          "intensifies_audio": false,
          "is_placeholder": false,
          "is_tone_modify": false,
          "keyframe_refs": [],
          "last_nonzero_volume": 1.0,
          "material_id": "{material_id}",
          "render_index": 0,
          "reverse": false,
          "source_timerange": {{
            "duration": {source_duration_us},
            "start": {source_start_us}
          }},
          "speed": 1.0,
          "target_timerange": {{
            "duration": {target_duration_us},
            "start": {target_start_us}
          }},
          "template_id": "",
          "template_scene": "default",
          "track_attribute": 0,
          "track_render_index": 0,
          "uniform_scale": {{
            "on": true,
            "value": 1.0
          }},
          "visible": true,
          "volume": 1.0
        }}"""
            segments.append(segment_json)

            # 更新时间轴位置 - 按照原项目逻辑
            duration = match.second_end_time - match.second_start_time
            current_timeline_position += duration

            if i < 5:  # 只显示前5个片段的详细信息
                print(f"   📦 片段 {i+1:03d}: 第二轨道 {match.second_start_time:.3f}s-{match.second_end_time:.3f}s (时长{duration:.3f}s)")
                print(f"        → 时间轴位置: {target_start_us/1000000:.3f}s-{(target_start_us+target_duration_us)/1000000:.3f}s")

        track_json += ",".join(segments)
        track_json += """
      ],
      "type": "video"
    }"""

        print(f"✅ 生成了 {len(matches)} 个视频片段")
        return track_json

    @staticmethod
    def _generate_uuid() -> str:
        """生成UUID - 按照原项目逻辑（去掉连字符）"""
        import uuid
        return str(uuid.uuid4()).replace("-", "")
