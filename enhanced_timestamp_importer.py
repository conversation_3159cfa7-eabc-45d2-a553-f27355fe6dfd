#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版时间戳导入器 - 支持原始拼接模式
"""

import json
import os
import re
import logging
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TimestampEntry:
    """时间戳条目"""
    sequence_number: int
    start_time: float
    end_time: float
    material_name: Optional[str] = None  # 新增：对应的素材文件名
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time
    
    def __str__(self) -> str:
        material_info = f" [{self.material_name}]" if self.material_name else ""
        return f"{self.sequence_number:03d} {self.start_time:.3f}-{self.end_time:.3f} ({self.duration:.3f}s){material_info}"

class EnhancedTimestampImporter:
    """增强版时间戳导入器"""
    
    def __init__(self):
        self.srt_sequence_counter = 1
        self.mode = "auto"  # auto, segmented, raw_concat
    
    def detect_project_mode(self, project_path: str) -> str:
        """检测项目模式：分镜模式 vs 原始拼接模式"""
        try:
            with open(project_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            tracks = project_data.get('tracks', [])
            if len(tracks) < 2:
                return "segmented"  # 默认分镜模式
            
            second_track = tracks[1]
            segments = second_track.get('segments', [])
            
            if len(segments) == 0:
                return "segmented"
            
            # 分析素材使用模式
            materials = project_data.get('materials', {})
            videos = materials.get('videos', [])
            
            material_usage = {}
            for segment in segments:
                material_id = segment.get('material_id', '')
                if material_id:
                    for video in videos:
                        if video.get('id') == material_id:
                            material_name = video.get('material_name', 'Unknown')
                            material_usage[material_name] = material_usage.get(material_name, 0) + 1
                            break
            
            # 判断逻辑
            total_materials = len(material_usage)
            total_segments = len(segments)
            
            # 如果每个素材只用一次，且素材数量接近片段数量，则是原始拼接模式
            single_use_materials = sum(1 for count in material_usage.values() if count == 1)
            
            if single_use_materials / total_materials > 0.8 and total_materials / total_segments > 0.8:
                logger.info(f"🔍 检测到原始拼接模式: {total_materials}个素材, {total_segments}个片段")
                return "raw_concat"
            else:
                logger.info(f"🔍 检测到分镜模式: {total_materials}个素材, {total_segments}个片段")
                return "segmented"
                
        except Exception as e:
            logger.error(f"检测项目模式失败: {e}")
            return "segmented"  # 默认分镜模式
    
    def extract_material_sequence(self, project_path: str) -> List[str]:
        """从项目中提取素材序列"""
        try:
            with open(project_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            tracks = project_data.get('tracks', [])
            if len(tracks) < 2:
                return []
            
            second_track = tracks[1]
            segments = second_track.get('segments', [])
            
            materials = project_data.get('materials', {})
            videos = materials.get('videos', [])
            
            material_sequence = []
            for segment in segments:
                material_id = segment.get('material_id', '')
                if material_id:
                    for video in videos:
                        if video.get('id') == material_id:
                            material_name = video.get('material_name', 'Unknown')
                            material_sequence.append(material_name)
                            break
                    else:
                        material_sequence.append('Unknown')
                else:
                    material_sequence.append('Unknown')
            
            return material_sequence
            
        except Exception as e:
            logger.error(f"提取素材序列失败: {e}")
            return []
    
    def parse_timestamps_with_project(self, timestamp_text: str, project_path: str) -> List[TimestampEntry]:
        """解析时间戳并结合项目信息"""
        # 检测项目模式
        mode = self.detect_project_mode(project_path)
        
        # 解析基础时间戳
        entries = self.parse_timestamps(timestamp_text)
        
        if mode == "raw_concat":
            # 原始拼接模式：添加素材信息
            material_sequence = self.extract_material_sequence(project_path)
            
            for i, entry in enumerate(entries):
                if i < len(material_sequence):
                    entry.material_name = material_sequence[i]
                    logger.debug(f"时间戳{entry.sequence_number} -> 素材{entry.material_name}")
        
        return entries
    
    def parse_timestamps(self, timestamp_text: str) -> List[TimestampEntry]:
        """解析时间戳文本（基础版本）"""
        entries = []
        
        logger.info("📝 解析时间戳文本...")
        
        lines = timestamp_text.strip().split('\n')
        valid_count = 0
        error_count = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            try:
                entry = self._parse_timestamp_line(line)
                if entry:
                    entries.append(entry)
                    valid_count += 1
                else:
                    error_count += 1
            except Exception as e:
                logger.error(f"解析失败: {line} - {e}")
                error_count += 1
        
        logger.info(f"✅ 时间戳解析完成: {valid_count}个有效, {error_count}个错误")
        return entries
    
    def _parse_timestamp_line(self, line: str) -> Optional[TimestampEntry]:
        """解析单行时间戳"""
        try:
            line = line.strip()
            if not line:
                return None
            
            # 检测格式类型 - 优先检测带序号的格式
            if re.match(r'^\d+\.\s+.*', line):
                # 简化格式: "1. 33:33.530-33:42.670" 或 "1. 00:33:33,531 --> 00:33:46,567"
                return self._parse_simple_format(line)
            elif '-->' in line:
                # SRT格式: "00:30:38,400 --> 00:30:43,566"
                return self._parse_srt_format(line)
            else:
                # 标准格式: "001 33:32.130-33:33.530"
                return self._parse_standard_format(line)
        
        except Exception as e:
            logger.error(f"解析失败: {line} - {e}")
            return None
    
    def _parse_standard_format(self, line: str) -> Optional[TimestampEntry]:
        """解析标准格式: "001 33:32.130-33:33.530" """
        parts = line.split(None, 1)
        if len(parts) != 2:
            logger.error(f"标准格式错误: {line} (期望: 序号 时间-时间)")
            return None
        
        sequence_number = int(parts[0])
        time_range = parts[1]
        
        time_parts = time_range.split('-')
        if len(time_parts) != 2:
            logger.error(f"时间格式错误: {time_range} (期望: 开始时间-结束时间)")
            return None
        
        start_time = self._parse_time_string(time_parts[0].strip())
        end_time = self._parse_time_string(time_parts[1].strip())
        
        if end_time <= start_time:
            logger.error(f"时间逻辑错误: {line} (结束时间必须大于开始时间)")
            return None
        
        return TimestampEntry(sequence_number, start_time, end_time)
    
    def _parse_simple_format(self, line: str) -> Optional[TimestampEntry]:
        """解析简化格式: "1. 33:33.530-33:42.670" """
        # 提取序号
        match = re.match(r'^(\d+)\.\s+(.+)', line)
        if not match:
            logger.error(f"简化格式错误: {line}")
            return None
        
        sequence_number = int(match.group(1))
        time_part = match.group(2).strip()
        
        # 检查是否是SRT格式的时间
        if '-->' in time_part:
            time_parts = re.split(r'\s*-->\s*', time_part)
            if len(time_parts) != 2:
                logger.error(f"SRT时间格式错误: {time_part}")
                return None
            
            start_time = self._parse_srt_time(time_parts[0].strip())
            end_time = self._parse_srt_time(time_parts[1].strip())
            
            if start_time < 0 or end_time < 0:
                logger.error(f"SRT时间解析失败: {time_part}")
                return None
        else:
            # 标准格式的时间
            time_parts = time_part.split('-')
            if len(time_parts) != 2:
                logger.error(f"时间格式错误: {time_part}")
                return None
            
            start_time = self._parse_time_string(time_parts[0].strip())
            end_time = self._parse_time_string(time_parts[1].strip())
        
        if end_time <= start_time:
            logger.error(f"时间逻辑错误: {line} (结束时间必须大于开始时间)")
            return None

        return TimestampEntry(sequence_number, start_time, end_time)
    
    def _parse_srt_format(self, line: str) -> Optional[TimestampEntry]:
        """解析SRT格式: "00:30:38,400 --> 00:30:43,566" """
        time_parts = re.split(r'\s*-->\s*', line)
        if len(time_parts) != 2:
            logger.error(f"SRT时间格式错误: {line} (期望: HH:MM:SS,mmm --> HH:MM:SS,mmm)")
            return None
        
        start_time = self._parse_srt_time(time_parts[0].strip())
        end_time = self._parse_srt_time(time_parts[1].strip())
        
        if start_time < 0 or end_time < 0:
            logger.error(f"SRT时间解析失败: {line}")
            return None
        
        # 使用自动递增的序号
        entry = TimestampEntry(self.srt_sequence_counter, start_time, end_time)
        self.srt_sequence_counter += 1
        return entry
    
    def _parse_srt_time(self, time_str: str) -> float:
        """解析SRT时间格式: "00:30:38,400" -> 秒数"""
        try:
            # SRT格式: HH:MM:SS,mmm
            parts = time_str.split(':')
            if len(parts) != 3:
                return -1
            
            hours = int(parts[0])
            minutes = int(parts[1])
            
            # 处理秒和毫秒部分
            sec_parts = parts[2].split(',')
            if len(sec_parts) != 2:
                return -1
            
            seconds = int(sec_parts[0])
            milliseconds = int(sec_parts[1])
            
            return hours * 3600.0 + minutes * 60.0 + seconds + milliseconds / 1000.0
        except ValueError:
            return -1
    
    def _parse_time_string(self, time_str: str) -> float:
        """解析时间字符串，支持格式: "33:32.130", "1:23:45.678", "45.678" """
        try:
            parts = time_str.split(':')
            
            if len(parts) == 1:
                # 格式: "45.678" (秒)
                return float(parts[0])
            elif len(parts) == 2:
                # 格式: "33:32.130" (分:秒)
                minutes = int(parts[0])
                seconds = float(parts[1])
                return minutes * 60.0 + seconds
            elif len(parts) == 3:
                # 格式: "1:23:45.678" (时:分:秒)
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = float(parts[2])
                return hours * 3600.0 + minutes * 60.0 + seconds
            else:
                raise ValueError(f"不支持的时间格式: {time_str}")
        except ValueError as e:
            raise ValueError(f"时间格式错误: {time_str}") from e

def main():
    """测试函数"""
    importer = EnhancedTimestampImporter()
    
    # 测试项目模式检测
    project_path = "D:/JianyingPro Drafts/7月20日/draft_content.json"
    mode = importer.detect_project_mode(project_path)
    print(f"检测到的模式: {mode}")
    
    # 测试素材序列提取
    material_sequence = importer.extract_material_sequence(project_path)
    print(f"素材序列: {material_sequence[:10]}...")  # 只显示前10个

if __name__ == "__main__":
    main()
