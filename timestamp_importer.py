#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时间戳导入器 - 通过提供的时间戳直接写入草稿

功能：
1. 解析用户提供的时间戳格式
2. 将时间戳转换为匹配结果
3. 直接写入剪映草稿项目
4. 支持多种时间戳格式
"""

import re
import logging
from typing import List, Optional
from dataclasses import dataclass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TimestampEntry:
    """时间戳条目"""
    sequence_number: int
    start_time: float
    end_time: float
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time
    
    def __str__(self) -> str:
        return f"{self.sequence_number:03d} {self.start_time:.3f}-{self.end_time:.3f} ({self.duration:.3f}s)"

class TimestampImporter:
    """时间戳导入器"""
    
    def __init__(self):
        self.srt_sequence_counter = 1
    
    def parse_timestamps(self, timestamp_text: str) -> List[TimestampEntry]:
        """解析时间戳文本"""
        entries = []

        logger.info("📝 解析时间戳文本...")

        # 检测是否是完整的SRT文件格式
        if self._is_full_srt_format(timestamp_text):
            logger.info("检测到完整SRT文件格式，使用SRT解析器")
            return self._parse_full_srt(timestamp_text)

        # 原有的逐行解析逻辑
        lines = timestamp_text.strip().split('\n')
        valid_count = 0
        error_count = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            try:
                entry = self._parse_timestamp_line(line)
                if entry:
                    entries.append(entry)
                    valid_count += 1
                else:
                    error_count += 1
            except Exception as e:
                logger.error(f"解析失败: {line} - {e}")
                error_count += 1

        logger.info(f"✅ 时间戳解析完成: {valid_count}个有效, {error_count}个错误")
        return entries
    
    def _parse_timestamp_line(self, line: str) -> Optional[TimestampEntry]:
        """解析单行时间戳"""
        try:
            line = line.strip()
            if not line:
                return None
            
            # 检测格式类型 - 优先检测带序号的格式
            if re.match(r'^\d+\.\s+.*', line):
                # 简化格式: "1. 33:33.530-33:42.670" 或 "1. 00:33:33,531 --> 00:33:46,567"
                return self._parse_simple_format(line)
            elif '-->' in line:
                # SRT格式: "00:30:38,400 --> 00:30:43,566"
                return self._parse_srt_format(line)
            else:
                # 标准格式: "001 33:32.130-33:33.530"
                return self._parse_standard_format(line)
        
        except Exception as e:
            logger.error(f"解析失败: {line} - {e}")
            return None
    
    def _parse_standard_format(self, line: str) -> Optional[TimestampEntry]:
        """解析标准格式: "001 33:32.130-33:33.530" """
        parts = line.split(None, 1)
        if len(parts) != 2:
            logger.error(f"标准格式错误: {line} (期望: 序号 时间-时间)")
            return None
        
        sequence_number = int(parts[0])
        time_range = parts[1]
        
        time_parts = time_range.split('-')
        if len(time_parts) != 2:
            logger.error(f"时间格式错误: {time_range} (期望: 开始时间-结束时间)")
            return None
        
        start_time = self._parse_time_string(time_parts[0].strip())
        end_time = self._parse_time_string(time_parts[1].strip())
        
        if end_time <= start_time:
            logger.error(f"时间逻辑错误: {line} (结束时间必须大于开始时间)")
            return None
        
        return TimestampEntry(sequence_number, start_time, end_time)
    
    def _parse_srt_format(self, line: str) -> Optional[TimestampEntry]:
        """解析SRT格式: "00:30:38,400 --> 00:30:43,566" """
        time_parts = re.split(r'\s*-->\s*', line)
        if len(time_parts) != 2:
            logger.error(f"SRT时间格式错误: {line} (期望: HH:MM:SS,mmm --> HH:MM:SS,mmm)")
            return None
        
        start_time = self._parse_srt_time(time_parts[0].strip())
        end_time = self._parse_srt_time(time_parts[1].strip())
        
        if start_time < 0 or end_time < 0:
            logger.error(f"SRT时间解析失败: {line}")
            return None
        
        # 使用自动递增的序号
        entry = TimestampEntry(self.srt_sequence_counter, start_time, end_time)
        self.srt_sequence_counter += 1
        return entry
    
    def _parse_srt_time(self, time_str: str) -> float:
        """解析SRT时间格式: "00:30:38,400" -> 秒数"""
        try:
            # SRT格式: HH:MM:SS,mmm
            parts = time_str.split(':')
            if len(parts) != 3:
                return -1
            
            hours = int(parts[0])
            minutes = int(parts[1])
            
            # 处理秒和毫秒部分
            sec_parts = parts[2].split(',')
            if len(sec_parts) != 2:
                return -1
            
            seconds = int(sec_parts[0])
            milliseconds = int(sec_parts[1])
            
            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
        except Exception as e:
            logger.error(f"SRT时间解析错误: {time_str} - {e}")
            return -1
    
    def _parse_simple_format(self, line: str) -> Optional[TimestampEntry]:
        """解析简化格式: "1. 33:33.530-33:42.670" 或 "1. 00:33:33,531 --> 00:33:46,567" """
        parts = line.split('.', 1)
        if len(parts) != 2:
            logger.error(f"简化格式错误: {line} (期望: 序号. 时间-时间)")
            return None

        sequence_number = int(parts[0].strip())
        time_range = parts[1].strip()

        # 检查是否包含 SRT 格式的 -->
        if '-->' in time_range:
            # SRT格式: "00:33:33,531 --> 00:33:46,567"
            time_parts = re.split(r'\s*-->\s*', time_range)
            if len(time_parts) != 2:
                logger.error(f"SRT时间格式错误: {time_range} (期望: HH:MM:SS,mmm --> HH:MM:SS,mmm)")
                return None

            # 在简化格式中，time_parts已经是纯时间了
            # 例如: time_range = "00:33:33,531 --> 00:33:46,567"
            start_time_str = time_parts[0].strip()
            end_time_str = time_parts[1].strip()

            start_time = self._parse_srt_time(start_time_str)
            end_time = self._parse_srt_time(end_time_str)

            if start_time < 0 or end_time < 0:
                logger.error(f"SRT时间解析失败: {time_range}")
                return None
        else:
            # 标准格式: "33:33.530-33:42.670"
            time_parts = time_range.split('-')
            if len(time_parts) != 2:
                logger.error(f"时间格式错误: {time_range} (期望: 开始时间-结束时间)")
                return None

            start_time = self._parse_time_string(time_parts[0].strip())
            end_time = self._parse_time_string(time_parts[1].strip())

        if end_time <= start_time:
            logger.error(f"时间逻辑错误: {line} (结束时间必须大于开始时间)")
            return None

        return TimestampEntry(sequence_number, start_time, end_time)
    
    def _parse_time_string(self, time_str: str) -> float:
        """解析时间字符串，支持格式: "33:32.130", "1:23:45.678", "45.678" """
        try:
            parts = time_str.split(':')
            
            if len(parts) == 1:
                # 格式: "45.678" (秒)
                return float(parts[0])
            elif len(parts) == 2:
                # 格式: "33:32.130" (分:秒)
                minutes = int(parts[0])
                seconds = float(parts[1])
                return minutes * 60.0 + seconds
            elif len(parts) == 3:
                # 格式: "1:23:45.678" (时:分:秒)
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = float(parts[2])
                return hours * 3600.0 + minutes * 60.0 + seconds
            else:
                raise ValueError(f"不支持的时间格式: {time_str}")
        except ValueError as e:
            raise ValueError(f"时间格式错误: {time_str}") from e
    
    def validate_timestamps(self, timestamps: List[TimestampEntry]) -> None:
        """验证时间戳的合理性"""
        logger.info("🔍 验证时间戳合理性...")
        
        warning_count = 0
        
        # 检查序号连续性
        sequence_numbers = set()
        for entry in timestamps:
            if entry.sequence_number in sequence_numbers:
                logger.warning(f"⚠️ 重复序号: {entry.sequence_number}")
                warning_count += 1
            sequence_numbers.add(entry.sequence_number)
        
        # 检查时间重叠
        sorted_by_time = sorted(timestamps, key=lambda x: x.start_time)
        
        for i in range(len(sorted_by_time) - 1):
            current = sorted_by_time[i]
            next_entry = sorted_by_time[i + 1]
            
            if current.end_time > next_entry.start_time:
                logger.warning(f"⚠️ 时间重叠: {current} 与 {next_entry}")
                warning_count += 1
        
        # 检查异常时长
        for entry in timestamps:
            if entry.duration < 0.1:
                logger.warning(f"⚠️ 时长过短: {entry}")
                warning_count += 1
            elif entry.duration > 60.0:
                logger.warning(f"⚠️ 时长过长: {entry}")
                warning_count += 1
        
        if warning_count == 0:
            logger.info("✅ 时间戳验证通过，无异常")
        else:
            logger.warning(f"⚠️ 发现 {warning_count} 个潜在问题")
    
    def display_statistics(self, timestamps: List[TimestampEntry]) -> str:
        """显示时间戳统计信息"""
        if not timestamps:
            return "📊 无时间戳数据"
        
        # 基本统计
        total_duration = sum(t.duration for t in timestamps)
        avg_duration = total_duration / len(timestamps)
        min_duration = min(t.duration for t in timestamps)
        max_duration = max(t.duration for t in timestamps)
        
        min_start_time = min(t.start_time for t in timestamps)
        max_end_time = max(t.end_time for t in timestamps)
        
        min_seq = min(t.sequence_number for t in timestamps)
        max_seq = max(t.sequence_number for t in timestamps)
        
        stats = []
        stats.append("📊 时间戳统计信息")
        stats.append("-" * 40)
        stats.append(f"片段数量: {len(timestamps)}个")
        stats.append(f"总时长: {total_duration:.2f}秒 ({total_duration/60:.2f}分钟)")
        stats.append(f"平均时长: {avg_duration:.2f}秒")
        stats.append(f"时长范围: {min_duration:.2f}秒 - {max_duration:.2f}秒")
        stats.append(f"时间范围: {self._format_time(min_start_time)} - {self._format_time(max_end_time)}")
        stats.append(f"序号范围: {min_seq} - {max_seq}")
        
        return '\n'.join(stats)
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间为可读格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        remaining_seconds = seconds % 60
        
        if hours > 0:
            return f"{hours}:{minutes:02d}:{remaining_seconds:06.3f}"
        else:
            return f"{minutes}:{remaining_seconds:06.3f}"
    
    def convert_to_srt_format(self, timestamps: List[TimestampEntry]) -> str:
        """转换为SRT格式输出"""
        srt_lines = []
        
        for i, entry in enumerate(timestamps, 1):
            # 序号
            srt_lines.append(str(i))
            
            # 时间戳
            start_time = self._seconds_to_srt_time(entry.start_time)
            end_time = self._seconds_to_srt_time(entry.end_time)
            srt_lines.append(f"{start_time} --> {end_time}")
            
            # 空白字幕内容
            srt_lines.append("")
            
            # 空行
            srt_lines.append("")
        
        return '\n'.join(srt_lines)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def _is_full_srt_format(self, text: str) -> bool:
        """检测是否是完整的SRT文件格式"""
        lines = text.strip().split('\n')

        # SRT文件的特征：
        # 1. 包含序号行（纯数字）
        # 2. 包含时间戳行（包含 -->）
        # 3. 包含文本内容行

        has_sequence = False
        has_timestamp = False
        has_text_content = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否是序号行
            if line.isdigit():
                has_sequence = True
            # 检查是否是时间戳行
            elif '-->' in line and ':' in line:
                has_timestamp = True
            # 检查是否有非数字、非时间戳的文本内容
            elif not line.isdigit() and '-->' not in line and len(line) > 3:
                has_text_content = True

        # 如果同时包含这三种元素，认为是完整SRT格式
        return has_sequence and has_timestamp and has_text_content

    def _parse_full_srt(self, srt_text: str) -> List[TimestampEntry]:
        """解析完整的SRT文件"""
        entries = []

        # 按空行分割SRT块
        blocks = re.split(r'\n\s*\n', srt_text.strip())

        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) < 3:  # SRT块至少需要3行：序号、时间戳、文本
                continue

            try:
                # 第一行：序号
                sequence_line = lines[0].strip()
                if not sequence_line.isdigit():
                    continue

                sequence_number = int(sequence_line)

                # 第二行：时间戳
                timestamp_line = lines[1].strip()
                if '-->' not in timestamp_line:
                    continue

                # 解析时间戳
                time_parts = re.split(r'\s*-->\s*', timestamp_line)
                if len(time_parts) != 2:
                    continue

                start_time = self._parse_srt_time(time_parts[0].strip())
                end_time = self._parse_srt_time(time_parts[1].strip())

                if start_time < 0 or end_time < 0:
                    continue

                # 创建时间戳条目
                entry = TimestampEntry(sequence_number, start_time, end_time)
                entries.append(entry)

            except Exception as e:
                logger.error(f"解析SRT块失败: {e}")
                continue

        logger.info(f"✅ SRT文件解析完成: {len(entries)}个字幕条目")
        return entries

    def convert_to_standard_format(self, timestamps: List[TimestampEntry]) -> str:
        """转换为标准格式输出"""
        lines = []

        for entry in timestamps:
            start_str = self._format_time_string(entry.start_time)
            end_str = self._format_time_string(entry.end_time)
            lines.append(f"{entry.sequence_number:03d} {start_str}-{end_str}")

        return '\n'.join(lines)
