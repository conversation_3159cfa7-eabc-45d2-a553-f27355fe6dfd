#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的剪映写入器功能
"""

from jianying_writer import JianyingTrackWriter, MatchResult
import os

def test_jianying_writer():
    """测试剪映写入器功能"""
    print("=" * 60)
    print("测试剪映写入器功能（按照原项目逻辑）")
    print("=" * 60)
    
    # 创建测试匹配结果
    test_matches = [
        MatchResult(
            main_segment_name="segment_001",
            main_start_time=2013.531,
            main_end_time=2026.567,
            second_segment_name="",
            second_start_time=0.0,
            second_end_time=0.0,
            confidence=1.0,
            method="测试导入"
        ),
        MatchResult(
            main_segment_name="segment_002", 
            main_start_time=2035.133,
            main_end_time=2066.132,
            second_segment_name="",
            second_start_time=0.0,
            second_end_time=0.0,
            confidence=1.0,
            method="测试导入"
        ),
        MatchResult(
            main_segment_name="segment_003",
            main_start_time=140.900,
            main_end_time=171.000,
            second_segment_name="",
            second_start_time=0.0,
            second_end_time=0.0,
            confidence=1.0,
            method="测试导入"
        )
    ]
    
    print(f"📊 测试匹配结果: {len(test_matches)} 个")
    for i, match in enumerate(test_matches, 1):
        print(f"  {i}. {match}")
    
    # 测试项目路径
    test_project_path = "测试项目文件夹/测试项目1"
    
    print(f"\n📁 测试项目路径: {test_project_path}")
    
    if os.path.exists(test_project_path):
        print("✅ 测试项目存在，开始写入测试...")
        
        # 创建写入器
        writer = JianyingTrackWriter()
        
        # 执行写入
        success = writer.write_matches_to_jianying_project(test_project_path, test_matches)
        
        if success:
            print("✅ 写入测试成功！")
            print("📄 请检查测试项目的草稿文件是否已更新")
        else:
            print("❌ 写入测试失败")
    else:
        print("❌ 测试项目不存在")
        print("💡 请先创建测试项目或使用真实的剪映项目进行测试")

def test_json_modification():
    """测试JSON修改功能"""
    print("\n" + "=" * 60)
    print("测试JSON修改功能")
    print("=" * 60)
    
    # 创建简单的测试JSON
    test_json = """{
  "materials": {
    "texts": []
  },
  "tracks": []
}"""
    
    print("📋 原始JSON:")
    print(test_json)
    
    # 创建测试匹配结果
    test_matches = [
        MatchResult(
            main_segment_name="test_001",
            main_start_time=10.0,
            main_end_time=20.0,
            second_segment_name="",
            second_start_time=0.0,
            second_end_time=0.0,
            confidence=1.0,
            method="测试"
        )
    ]
    
    # 创建写入器并测试修改
    writer = JianyingTrackWriter()
    
    try:
        # 测试添加文本材料
        json_with_materials = writer._add_text_materials(test_json, test_matches)
        print("\n📝 添加文本材料后:")
        print(json_with_materials[:200] + "..." if len(json_with_materials) > 200 else json_with_materials)
        
        # 测试添加第三轨道
        final_json = writer._add_third_track(json_with_materials, test_matches)
        print("\n🎬 添加第三轨道后:")
        print(final_json[:300] + "..." if len(final_json) > 300 else final_json)
        
        print("\n✅ JSON修改功能测试完成")
        
    except Exception as e:
        print(f"\n❌ JSON修改测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_helper_functions():
    """测试辅助函数"""
    print("\n" + "=" * 60)
    print("测试辅助函数")
    print("=" * 60)
    
    writer = JianyingTrackWriter()
    
    # 测试UUID生成
    uuid1 = writer._generate_uuid()
    uuid2 = writer._generate_uuid()
    print(f"📋 UUID生成测试:")
    print(f"  UUID1: {uuid1}")
    print(f"  UUID2: {uuid2}")
    print(f"  是否不同: {uuid1 != uuid2}")
    
    # 测试文本材料ID生成
    text_id1 = writer._generate_text_material_id(0)
    text_id2 = writer._generate_text_material_id(1)
    print(f"\n📋 文本材料ID生成测试:")
    print(f"  ID1: {text_id1}")
    print(f"  ID2: {text_id2}")
    
    # 测试括号匹配
    test_content = '{"array": [1, 2, [3, 4]], "other": "value"}'
    array_start = test_content.find('[')
    array_end = writer._find_matching_bracket(test_content, array_start)
    print(f"\n📋 括号匹配测试:")
    print(f"  测试内容: {test_content}")
    print(f"  数组开始位置: {array_start}")
    print(f"  数组结束位置: {array_end}")
    print(f"  提取的数组: {test_content[array_start:array_end+1]}")

def main():
    """主测试函数"""
    print("🧪 剪映写入器功能测试")
    print("=" * 60)
    
    # 测试辅助函数
    test_helper_functions()
    
    # 测试JSON修改
    test_json_modification()
    
    # 测试完整写入功能
    test_jianying_writer()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("1. 新的剪映写入器已按照原项目逻辑实现")
    print("2. 支持直接操作JSON字符串，避免解析问题")
    print("3. 完全兼容原项目的文件结构和格式")
    print("4. 可以正确添加文本材料和第三轨道")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
