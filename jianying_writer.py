#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
剪映轨道写入器 - 将匹配结果写入剪映草稿的第三条轨道
完全按照原项目 JianyingTrackWriter.java 的方式实现
"""

import os
import re
from datetime import datetime
from typing import List

class MatchResult:
    """匹配结果信息 - 按照原项目格式"""
    def __init__(self, main_segment_name: str, main_start_time: float, main_end_time: float,
                 second_segment_name: str, second_start_time: float, second_end_time: float,
                 confidence: float, method: str):
        self.main_segment_name = main_segment_name
        self.main_start_time = main_start_time
        self.main_end_time = main_end_time
        self.second_segment_name = second_segment_name
        self.second_start_time = second_start_time
        self.second_end_time = second_end_time
        self.confidence = confidence
        self.method = method

    def __str__(self):
        return f"主轨道[{self.main_start_time:.2f}s-{self.main_end_time:.2f}s] → 第二轨道[{self.second_start_time:.2f}s-{self.second_end_time:.2f}s] ({self.confidence*100:.1f}%, {self.method})"

class JianyingTrackWriter:
    """剪映轨道写入器 - 按照原项目实现"""

    def write_matches_to_jianying_project(self, project_path: str, matches: List[MatchResult]) -> bool:
        """将匹配结果写入剪映草稿的第三条轨道 - 按照原项目逻辑"""
        print("📝 开始将匹配结果写入剪映项目")
        print(f"📂 项目路径: {project_path}")
        print(f"📊 匹配结果数量: {len(matches)}")

        try:
            # 读取原始项目文件
            draft_file = os.path.join(project_path, "draft_content.json")
            if not os.path.exists(draft_file):
                print("❌ 未找到draft_content.json文件")
                return False

            # 备份原始文件
            self._backup_original_file(draft_file)

            # 读取JSON内容
            json_content = self._read_file_content(draft_file)

            # 修改JSON，添加第三条轨道和文本材料
            modified_json = self._add_third_track_with_matches(json_content, matches)

            # 写入修改后的内容
            self._write_file_content(draft_file, modified_json)

            print("✅ 匹配结果已成功写入剪映项目")
            print("📄 备份文件已保存")

            return True

        except Exception as e:
            print(f"❌ 写入匹配结果失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _backup_original_file(self, original_file: str):
        """备份原始文件 - 按照原项目逻辑"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(
            os.path.dirname(original_file),
            f"draft_content_backup_{timestamp}.json"
        )

        # 复制文件
        with open(original_file, 'rb') as src, open(backup_file, 'wb') as dst:
            dst.write(src.read())

        print(f"💾 备份文件: {os.path.basename(backup_file)}")

    def _read_file_content(self, file_path: str) -> str:
        """读取文件内容 - 按照原项目逻辑"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()

    def _write_file_content(self, file_path: str, content: str):
        """写入文件内容 - 按照原项目逻辑"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def _add_third_track_with_matches(self, json_content: str, matches: List[MatchResult]) -> str:
        """添加第三条轨道和匹配结果 - 按照原项目逻辑"""
        print("🔧 开始修改JSON结构...")

        # 首先添加文本材料到materials.texts
        json_with_materials = self._add_text_materials(json_content, matches)

        # 然后添加第三条轨道
        final_json = self._add_third_track(json_with_materials, matches)

        print("✅ JSON结构修改完成")
        return final_json

    def _add_text_materials(self, json_content: str, matches: List[MatchResult]) -> str:
        """添加文本材料到materials.texts - 按照原项目逻辑"""
        print("📝 添加文本材料...")

        # 查找materials结构
        materials_pos = json_content.find('"materials":')
        if materials_pos == -1:
            print("❌ 未找到materials结构")
            return json_content

        # 查找texts数组
        texts_pos = json_content.find('"texts":', materials_pos)
        if texts_pos == -1:
            print("❌ 未找到texts数组")
            return json_content

        texts_array_start = json_content.find('[', texts_pos)
        if texts_array_start == -1:
            print("❌ texts数组格式错误")
            return json_content

        texts_array_end = self._find_matching_bracket(json_content, texts_array_start)
        if texts_array_end == -1:
            print("❌ 无法找到texts数组结束位置")
            return json_content

        # 生成文本材料JSON
        text_materials = []
        for i, match in enumerate(matches):
            material_id = self._generate_text_material_id(i)
            match_text = f"主轨道[{match.main_start_time:.2f}s-{match.main_end_time:.2f}s] → 第二轨道[{match.second_start_time:.2f}s-{match.second_end_time:.2f}s] ({match.confidence*100:.1f}%, {match.method})"

            material_json = f"""        {{
          "id": "{material_id}",
          "path": "",
          "type": "text",
          "content": "{match_text}",
          "font_id": "",
          "font_path": "",
          "font_title": "默认",
          "font_category_id": "",
          "font_category_name": "",
          "font_resource_id": "",
          "font_size": 12.0,
          "font_url": "",
          "alignment": 1,
          "bold": false,
          "italic": false,
          "underline": false,
          "color": [1.0, 1.0, 1.0, 1.0],
          "stroke_color": [0.0, 0.0, 0.0, 1.0],
          "stroke_width": 0.0,
          "shadow_color": [0.0, 0.0, 0.0, 0.5],
          "shadow_offset": [2.0, 2.0],
          "shadow_blur": 4.0,
          "line_spacing": 0.0,
          "letter_spacing": 0.0
        }}"""
            text_materials.append(material_json)

        # 插入文本材料
        before_texts = json_content[:texts_array_end]
        after_texts = json_content[texts_array_end:]

        # 检查是否需要添加逗号
        separator = ""
        texts_content = json_content[texts_array_start + 1:texts_array_end].strip()
        if texts_content and texts_content != "":
            separator = ",\n"

        result = before_texts + separator + ",\n".join(text_materials) + after_texts
        print("✅ 文本材料添加完成")
        return result

    def _add_third_track(self, json_content: str, matches: List[MatchResult]) -> str:
        """添加第三条轨道 - 按照原项目逻辑"""
        print("🎬 添加第三条轨道...")

        # 查找tracks数组的位置
        tracks_pos = json_content.find('"tracks":')
        if tracks_pos == -1:
            print("❌ 未找到tracks结构")
            return json_content

        # 找到tracks数组的开始和结束
        array_start = json_content.find('[', tracks_pos)
        if array_start == -1:
            print("❌ tracks数组格式错误")
            return json_content

        # 找到tracks数组的结束位置
        array_end = self._find_matching_bracket(json_content, array_start)
        if array_end == -1:
            print("❌ 无法找到tracks数组结束位置")
            return json_content

        # 生成第三条轨道的JSON
        third_track_json = self._generate_third_track_json(matches)

        # 在tracks数组末尾添加第三条轨道
        before_tracks = json_content[:array_end]
        after_tracks = json_content[array_end:]

        # 如果tracks数组不为空，需要添加逗号
        separator = ""
        tracks_content = json_content[array_start + 1:array_end].strip()
        if tracks_content and tracks_content != "":
            separator = ","

        result = before_tracks + separator + third_track_json + after_tracks
        print("✅ 第三条轨道添加完成")
        return result

    def _find_matching_bracket(self, content: str, start_pos: int) -> int:
        """找到匹配的括号位置 - 按照原项目逻辑"""
        count = 1
        pos = start_pos + 1

        while pos < len(content) and count > 0:
            c = content[pos]
            if c == '[':
                count += 1
            elif c == ']':
                count -= 1
            pos += 1

        return pos - 1 if count == 0 else -1

    def _generate_text_material_id(self, index: int) -> str:
        """生成文本素材ID（带索引）- 按照原项目逻辑"""
        import time
        import random
        return f"text_match_{int(time.time() * 1000)}_{index}"

    def _generate_uuid(self) -> str:
        """生成UUID - 按照原项目逻辑（去掉连字符）"""
        import uuid
        return str(uuid.uuid4()).replace("-", "")

    def _generate_third_track_json(self, matches: List[MatchResult]) -> str:
        """生成第三条轨道的JSON结构 - 完全按照原项目逻辑"""
        print("🎬 生成第三条文本轨道JSON...")

        track_json = f"""
    {{
      "attribute": 0,
      "flag": 0,
      "id": "{self._generate_uuid()}",
      "is_default_name": true,
      "name": "第三轨道",
      "segments": ["""

        # 生成匹配结果的文本片段 - 按照原项目逻辑
        segments = []
        current_timeline_position = 0.0  # 时间轴位置

        for i, match in enumerate(matches):
            # 计算时间（微秒） - 按照原项目逻辑
            source_duration_us = int((match.main_end_time - match.main_start_time) * 1000000)
            target_start_us = int(match.main_start_time * 1000000)  # 使用原始时间戳位置

            segment_json = f"""
        {{
          "cartoon": false,
          "clip": {{
            "alpha": 1.0,
            "flip": {{
              "horizontal": false,
              "vertical": false
            }},
            "rotation": 0.0,
            "scale": {{
              "x": 1.0,
              "y": 1.0
            }},
            "transform": {{
              "x": 0.0,
              "y": 0.0
            }}
          }},
          "common_keyframes": [],
          "enable_adjust": true,
          "enable_color_curves": true,
          "enable_color_match_adjust": false,
          "enable_color_wheels": true,
          "enable_lut": true,
          "enable_smart_color_adjust": false,
          "extra_material_refs": [],
          "group_id": "",
          "hdr_settings": {{
            "intensity": 1.0,
            "mode": 1,
            "nits": 1000
          }},
          "id": "{self._generate_uuid()}",
          "intensifies_audio": false,
          "is_placeholder": false,
          "is_tone_modify": false,
          "keyframe_refs": [],
          "last_nonzero_volume": 1.0,
          "material_id": "{self._generate_text_material_id(i)}",
          "render_index": 0,
          "reverse": false,
          "source_timerange": {{
            "duration": {source_duration_us},
            "start": 0
          }},
          "speed": 1.0,
          "target_timerange": {{
            "duration": {source_duration_us},
            "start": {target_start_us}
          }},
          "template_id": "",
          "template_scene": "default",
          "track_attribute": 0,
          "track_render_index": 0,
          "uniform_scale": {{
            "on": true,
            "value": 1.0
          }},
          "visible": true,
          "volume": 1.0
        }}"""
            segments.append(segment_json)

            # 更新时间轴位置（用于下一个片段）
            current_timeline_position += (match.main_end_time - match.main_start_time)

        track_json += ",".join(segments)
        track_json += """
      ],
      "type": "text"
    }"""

        print(f"✅ 生成了 {len(matches)} 个文本片段")
        return track_json
