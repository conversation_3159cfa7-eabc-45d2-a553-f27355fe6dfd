#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文件夹记忆功能
"""

import json
import os

def test_memory_files():
    """测试记忆文件的创建和读取"""
    print("=" * 60)
    print("测试文件夹记忆功能")
    print("=" * 60)
    
    # 检查记忆文件
    memory_files = [
        "folder_memory.json",
        "project_file_memory.json"
    ]
    
    for memory_file in memory_files:
        print(f"\n📁 检查记忆文件: {memory_file}")
        
        if os.path.exists(memory_file):
            try:
                with open(memory_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"✅ 文件存在，包含 {len(data)} 个记忆项")
                
                if data:
                    print("📋 记忆内容:")
                    for key, value in data.items():
                        print(f"  - {key}: {value}")
                else:
                    print("📋 记忆文件为空")
                    
            except Exception as e:
                print(f"❌ 读取失败: {e}")
        else:
            print("⚠️ 文件不存在（首次运行时正常）")
    
    print("\n" + "=" * 60)
    print("记忆功能说明:")
    print("1. folder_memory.json - 保存各个文件夹选择按钮的记忆")
    print("2. project_file_memory.json - 保存每个项目选择的时间戳文件")
    print("3. 每次选择文件/文件夹时会自动保存记忆")
    print("4. 下次打开时会自动恢复到上次选择的位置")
    print("=" * 60)

if __name__ == "__main__":
    test_memory_files()
    input("\n按回车键退出...")
