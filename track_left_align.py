#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剪映轨道左对齐工具 - 将轨道中的片段间隙消除，实现左对齐吸附
"""

import json
import os
import shutil
from typing import List, Dict, Any, Tuple
from datetime import datetime

class TrackLeftAlign:
    """轨道左对齐处理器"""
    
    def __init__(self):
        self.draft_data = None
        self.draft_path = None
        
    def load_draft(self, draft_path: str) -> bool:
        """加载剪映草稿文件"""
        try:
            self.draft_path = draft_path
            draft_file = os.path.join(draft_path, "draft_content.json")
            
            if not os.path.exists(draft_file):
                print(f"❌ 草稿文件不存在: {draft_file}")
                return False
                
            with open(draft_file, 'r', encoding='utf-8') as f:
                self.draft_data = json.load(f)
                
            print(f"✅ 成功加载草稿: {draft_path}")
            return True
            
        except Exception as e:
            print(f"❌ 加载草稿失败: {e}")
            return False
    
    def backup_draft(self) -> bool:
        """备份原始草稿文件"""
        try:
            draft_file = os.path.join(self.draft_path, "draft_content.json")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(self.draft_path, f"draft_content_backup_{timestamp}.json")
            
            shutil.copy2(draft_file, backup_file)
            print(f"✅ 备份文件已保存: {backup_file}")
            return True
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def analyze_track_gaps(self, track_index: int) -> Dict[str, Any]:
        """分析指定轨道的间隙"""
        try:
            tracks = self.draft_data.get('tracks', [])
            video_tracks = [track for track in tracks if track.get('type') == 'video']
            
            if track_index >= len(video_tracks):
                print(f"❌ 轨道索引超出范围: {track_index}")
                return {}
                
            track = video_tracks[track_index]
            segments = track.get('segments', [])
            
            # 提取片段信息
            segment_info = []
            for i, segment in enumerate(segments):
                target_timerange = segment.get('target_timerange', {})
                start_time = target_timerange.get('start', 0)  # 保持微秒单位
                duration = target_timerange.get('duration', 0)
                end_time = start_time + duration
                
                segment_info.append({
                    'index': i,
                    'segment': segment,
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration
                })
            
            # 按开始时间排序
            segment_info.sort(key=lambda x: x['start_time'])
            
            # 计算间隙
            gaps = []
            for i in range(len(segment_info) - 1):
                current_end = segment_info[i]['end_time']
                next_start = segment_info[i + 1]['start_time']
                
                if next_start > current_end:
                    gap_duration = next_start - current_end
                    gaps.append({
                        'after_segment': i,
                        'before_segment': i + 1,
                        'gap_start': current_end,
                        'gap_end': next_start,
                        'gap_duration': gap_duration
                    })
            
            return {
                'track_index': track_index,
                'segments': segment_info,
                'gaps': gaps,
                'has_gaps': len(gaps) > 0
            }
            
        except Exception as e:
            print(f"❌ 分析轨道间隙失败: {e}")
            return {}
    
    def left_align_track(self, track_index: int) -> bool:
        """对指定轨道执行左对齐操作"""
        try:
            print(f"\n🔧 开始对轨道 {track_index + 1} 执行左对齐...")
            
            # 分析轨道间隙
            analysis = self.analyze_track_gaps(track_index)
            if not analysis or not analysis['has_gaps']:
                print(f"✅ 轨道 {track_index + 1} 已经对齐，无需处理")
                return True
            
            segments = analysis['segments']
            gaps = analysis['gaps']
            
            print(f"📊 发现 {len(gaps)} 个间隙，开始左对齐处理...")
            
            # 计算累积偏移量
            cumulative_offset = 0
            
            for i, segment_info in enumerate(segments):
                segment = segment_info['segment']
                
                # 计算当前片段需要向左移动的距离
                offset_for_this_segment = cumulative_offset
                
                # 更新片段的时间范围
                if offset_for_this_segment > 0:
                    target_timerange = segment.get('target_timerange', {})
                    original_start = target_timerange.get('start', 0)
                    new_start = original_start - offset_for_this_segment
                    
                    # 确保不会移动到负数时间
                    if new_start < 0:
                        new_start = 0
                    
                    target_timerange['start'] = new_start
                    
                    print(f"   片段{i+1}: {original_start/1000000:.3f}s -> {new_start/1000000:.3f}s "
                          f"(向左移动 {offset_for_this_segment/1000000:.3f}s)")
                
                # 检查是否有间隙在这个片段之后
                for gap in gaps:
                    if gap['after_segment'] == i:
                        cumulative_offset += gap['gap_duration']
                        break
            
            print(f"✅ 轨道 {track_index + 1} 左对齐完成")
            return True
            
        except Exception as e:
            print(f"❌ 左对齐失败: {e}")
            return False
    
    def save_draft(self) -> bool:
        """保存修改后的草稿文件"""
        try:
            draft_file = os.path.join(self.draft_path, "draft_content.json")
            
            with open(draft_file, 'w', encoding='utf-8') as f:
                json.dump(self.draft_data, f, ensure_ascii=False, separators=(',', ':'))
            
            print(f"✅ 草稿文件已保存: {draft_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存草稿失败: {e}")
            return False
    
    def process_all_tracks(self) -> bool:
        """处理所有轨道的左对齐"""
        try:
            tracks = self.draft_data.get('tracks', [])
            video_tracks = [track for track in tracks if track.get('type') == 'video']
            
            print(f"\n🎬 开始处理 {len(video_tracks)} 个视频轨道...")
            
            success_count = 0
            for i in range(len(video_tracks)):
                if self.left_align_track(i):
                    success_count += 1
            
            print(f"\n📊 处理完成: {success_count}/{len(video_tracks)} 个轨道成功左对齐")
            return success_count == len(video_tracks)
            
        except Exception as e:
            print(f"❌ 处理所有轨道失败: {e}")
            return False

def main():
    """主函数"""
    draft_path = r"D:\JianyingPro Drafts\8月1日"
    
    aligner = TrackLeftAlign()
    
    print("🚀 剪映轨道左对齐工具")
    print("=" * 50)
    
    # 加载草稿
    if not aligner.load_draft(draft_path):
        return
    
    # 备份原始文件
    if not aligner.backup_draft():
        print("⚠️ 备份失败，但继续处理...")
    
    # 分析当前状态
    print("\n📊 分析当前轨道状态...")
    for i in range(2):  # 分析两个轨道
        analysis = aligner.analyze_track_gaps(i)
        if analysis:
            gaps = analysis['gaps']
            print(f"   轨道 {i+1}: {len(analysis['segments'])} 个片段, "
                  f"{len(gaps)} 个间隙 {'(需要对齐)' if gaps else '(已对齐)'}")
    
    # 执行左对齐
    if aligner.process_all_tracks():
        # 保存结果
        if aligner.save_draft():
            print("\n🎉 左对齐处理完成！")
            print("💡 请在剪映中重新打开项目查看效果")
        else:
            print("\n❌ 保存失败")
    else:
        print("\n❌ 左对齐处理失败")

if __name__ == "__main__":
    main()
