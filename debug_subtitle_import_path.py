#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试字幕导入路径问题
"""

import os

def debug_subtitle_import_path():
    """调试字幕导入路径问题"""
    print("🔍 调试字幕导入路径问题")
    print("=" * 60)
    
    # 模拟GUI中的路径构建
    project_folder = "D:/JianyingPro Drafts"
    project_name = "7月20日"
    draft_name = "draft_content.json"
    
    project_path = os.path.join(project_folder, project_name)
    draft_path = os.path.join(project_path, draft_name)
    
    print(f"📁 项目文件夹: {project_folder}")
    print(f"📁 项目名称: {project_name}")
    print(f"📄 草稿名称: {draft_name}")
    print(f"📁 项目路径: {project_path}")
    print(f"📄 草稿路径: {draft_path}")
    
    # 检查文件是否存在
    print(f"\n🔍 文件存在性检查:")
    print(f"  项目文件夹存在: {os.path.exists(project_folder)}")
    print(f"  项目路径存在: {os.path.exists(project_path)}")
    print(f"  草稿文件存在: {os.path.exists(draft_path)}")
    
    # 列出项目目录中的文件
    if os.path.exists(project_path):
        print(f"\n📂 项目目录内容:")
        files = os.listdir(project_path)
        for file in files:
            file_path = os.path.join(project_path, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  📄 {file} ({size} bytes)")
            else:
                print(f"  📁 {file}/")
    
    # 分析备份文件名问题
    print(f"\n🔍 备份文件名分析:")
    
    # 从日志看到的实际备份文件
    actual_backup = "D:/JianyingPro Drafts/7月20日/draft_agency_config.json.backup_20250724_194539"
    print(f"  实际备份路径: {actual_backup}")
    print(f"  实际备份存在: {os.path.exists(actual_backup)}")
    
    # 分析问题
    print(f"\n❌ 问题分析:")
    print(f"  预期操作文件: draft_content.json")
    print(f"  实际操作文件: draft_agency_config.json")
    print(f"  这说明字幕导入器接收到了错误的文件路径！")
    
    # 检查可能的原因
    print(f"\n🔍 可能的原因:")
    print(f"  1. GUI传递了错误的draft_path")
    print(f"  2. 字幕轨道生成器内部有路径处理错误")
    print(f"  3. 某个地方硬编码了错误的文件名")

def check_subtitle_generator():
    """检查字幕轨道生成器"""
    print(f"\n🔍 检查字幕轨道生成器...")
    
    try:
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        from subtitle_track_generator import SubtitleTrackGenerator
        
        # 创建生成器
        generator = SubtitleTrackGenerator()
        
        # 测试路径处理
        test_path = "D:/JianyingPro Drafts/7月20日/draft_content.json"
        print(f"  测试路径: {test_path}")
        
        # 模拟备份路径生成
        from datetime import datetime
        backup_path = f"{test_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"  预期备份路径: {backup_path}")
        
        # 检查方法是否存在
        if hasattr(generator, 'add_subtitle_to_project'):
            print(f"  ✅ add_subtitle_to_project 方法存在")
        else:
            print(f"  ❌ add_subtitle_to_project 方法不存在")
            
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🧪 字幕导入路径问题调试")
    print("=" * 80)
    
    debug_subtitle_import_path()
    check_subtitle_generator()
    
    print("=" * 80)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
