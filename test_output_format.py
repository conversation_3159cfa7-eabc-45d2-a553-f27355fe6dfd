#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修改后的输出格式和导入功能
"""

from timestamp_importer import TimestampImporter

def test_simple_output_format():
    """测试简洁的输出格式"""
    print("=" * 60)
    print("测试简洁输出格式")
    print("=" * 60)
    
    # 模拟智能字幕匹配的简洁输出格式
    test_content = """1. 00:33:33,531 --> 00:33:46,567
2. 00:33:55,133 --> 00:34:26,132
3. 00:02:20,900 --> 00:02:51,000
4. 00:03:07,599 --> 00:03:44,366
5. 00:04:13,233 --> 00:04:32,567
6. 00:09:51,767 --> 00:10:07,432
7. 00:10:26,232 --> 00:10:49,133
8. 00:15:42,600 --> 00:16:29,232
9. 00:26:11,833 --> 00:26:57,232
10. 00:27:02,633 --> 00:27:09,166
11. 00:27:19,467 --> 00:27:30,967
12. 00:27:42,267 --> 00:28:39,032
13. 00:31:34,200 --> 00:31:48,133
14. 00:31:54,032 --> 00:32:32,200
15. 00:32:40,232 --> 00:32:46,231
16. 00:32:59,599 --> 00:33:01,232"""
    
    # 保存测试文件
    with open("测试简洁格式.txt", 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ 已生成测试文件: 测试简洁格式.txt")
    print("📋 内容预览:")
    print(test_content[:200] + "...")
    
    return test_content

def test_timestamp_import():
    """测试时间戳导入功能"""
    print("\n" + "=" * 60)
    print("测试时间戳导入功能")
    print("=" * 60)
    
    # 测试简洁格式解析
    test_content = """1. 00:33:33,531 --> 00:33:46,567
2. 00:33:55,133 --> 00:34:26,132
3. 00:02:20,900 --> 00:02:51,000"""
    
    importer = TimestampImporter()
    entries = importer.parse_timestamps(test_content)
    
    print(f"📊 解析结果: {len(entries)} 个时间戳条目")
    
    if entries:
        print("📋 解析详情:")
        for i, entry in enumerate(entries[:3]):  # 只显示前3个
            print(f"  {i+1}. 序号{entry.sequence}: {entry.start_time:.3f}s - {entry.end_time:.3f}s")
        
        # 转换为匹配结果格式
        match_results = []
        for entry in entries:
            match_result = {
                'sequence': entry.sequence,
                'start_time': entry.start_time,
                'end_time': entry.end_time,
                'duration': entry.end_time - entry.start_time
            }
            match_results.append(match_result)
        
        print(f"\n✅ 成功转换为 {len(match_results)} 个匹配结果")
        return True
    else:
        print("❌ 解析失败")
        return False

def test_old_vs_new_format():
    """对比旧格式和新格式"""
    print("\n" + "=" * 60)
    print("对比旧格式和新格式")
    print("=" * 60)
    
    # 旧格式（带标题）
    old_format = """字幕匹配结果
========================================

1. 00:33:33,531 --> 00:33:46,567
2. 00:33:55,133 --> 00:34:26,132
3. 00:02:20,900 --> 00:02:51,000"""
    
    # 新格式（简洁）
    new_format = """1. 00:33:33,531 --> 00:33:46,567
2. 00:33:55,133 --> 00:34:26,132
3. 00:02:20,900 --> 00:02:51,000"""
    
    print("📋 旧格式（带标题）:")
    print(old_format)
    print("\n📋 新格式（简洁）:")
    print(new_format)
    
    # 测试两种格式的解析
    importer = TimestampImporter()
    
    print("\n🔍 测试旧格式解析:")
    old_entries = importer.parse_timestamps(old_format)
    print(f"  解析结果: {len(old_entries)} 个条目")
    
    print("\n🔍 测试新格式解析:")
    new_entries = importer.parse_timestamps(new_format)
    print(f"  解析结果: {len(new_entries)} 个条目")
    
    if len(old_entries) == len(new_entries) == 3:
        print("✅ 两种格式都能正确解析")
        return True
    else:
        print("❌ 格式解析有问题")
        return False

def test_gui_output_simulation():
    """模拟GUI输出测试"""
    print("\n" + "=" * 60)
    print("模拟GUI输出测试")
    print("=" * 60)
    
    # 模拟智能字幕匹配的时间戳列表
    timestamps = [
        "00:33:33,531 --> 00:33:46,567",
        "00:33:55,133 --> 00:34:26,132", 
        "00:02:20,900 --> 00:02:51,000",
        "00:03:07,599 --> 00:03:44,366",
        "00:04:13,233 --> 00:04:32,567"
    ]
    
    # 模拟新的GUI保存逻辑（简洁格式）
    filename = "模拟GUI输出.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        for i, timestamp in enumerate(timestamps, 1):
            f.write(f"{i}. {timestamp}\n")
    
    print(f"✅ 已生成模拟GUI输出文件: {filename}")
    
    # 读取并显示内容
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("📋 文件内容:")
    print(content)
    
    # 测试导入
    importer = TimestampImporter()
    entries = importer.parse_timestamps(content)
    
    print(f"🔍 导入测试结果: {len(entries)} 个条目")
    
    if len(entries) == len(timestamps):
        print("✅ GUI输出格式完全兼容导入功能")
        return True
    else:
        print("❌ GUI输出格式与导入功能不兼容")
        return False

def main():
    """主测试函数"""
    print("🧪 输出格式和导入功能测试")
    print("=" * 60)
    
    # 测试简洁输出格式
    test_simple_output_format()
    
    # 测试时间戳导入
    import_success = test_timestamp_import()
    
    # 对比旧格式和新格式
    format_success = test_old_vs_new_format()
    
    # 模拟GUI输出测试
    gui_success = test_gui_output_simulation()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 时间戳导入功能: {'通过' if import_success else '失败'}")
    print(f"✅ 格式兼容性测试: {'通过' if format_success else '失败'}")
    print(f"✅ GUI输出兼容性: {'通过' if gui_success else '失败'}")
    
    if import_success and format_success and gui_success:
        print("\n🎉 所有测试通过！")
        print("📋 功能说明:")
        print("1. 智能字幕匹配现在输出简洁格式（无标题）")
        print("2. 导入时间戳功能支持简洁格式的SRT时间戳")
        print("3. 格式: '1. 00:33:33,531 --> 00:33:46,567'")
        print("4. 完全兼容导入时间戳功能")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
