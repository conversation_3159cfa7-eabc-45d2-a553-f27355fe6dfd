#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试参数恢复功能
"""

import tkinter as tk
import json
import os

def test_parameter_restore():
    """测试参数恢复功能"""
    print("🧪 测试场景检测参数恢复功能")
    print("=" * 50)
    
    # 创建测试参数
    test_params = {
        "scene_threshold": 75.0,
        "min_scene_length": 2.5
    }
    
    # 保存测试参数
    with open("parameter_memory.json", 'r', encoding='utf-8') as f:
        current_params = json.load(f)
    
    current_params.update(test_params)
    
    with open("parameter_memory.json", 'w', encoding='utf-8') as f:
        json.dump(current_params, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 设置测试参数: 敏感度={test_params['scene_threshold']}, 最小长度={test_params['min_scene_length']}")
    
    # 创建简单的GUI来测试参数恢复
    root = tk.Tk()
    root.title("参数恢复测试")
    root.geometry("400x200")
    
    # 创建变量
    scene_threshold = tk.DoubleVar(value=30.0)  # 默认值
    min_scene_length = tk.DoubleVar(value=1.0)  # 默认值
    
    print(f"📊 初始值: 敏感度={scene_threshold.get()}, 最小长度={min_scene_length.get()}")
    
    # 模拟参数加载
    try:
        with open("parameter_memory.json", 'r', encoding='utf-8') as f:
            parameter_memory = json.load(f)
        
        if 'scene_threshold' in parameter_memory:
            scene_threshold.set(parameter_memory['scene_threshold'])
            print(f"✅ 恢复敏感度: {parameter_memory['scene_threshold']}")
        
        if 'min_scene_length' in parameter_memory:
            min_scene_length.set(parameter_memory['min_scene_length'])
            print(f"✅ 恢复最小长度: {parameter_memory['min_scene_length']}")
            
    except Exception as e:
        print(f"❌ 参数加载失败: {e}")
    
    print(f"📊 恢复后值: 敏感度={scene_threshold.get()}, 最小长度={min_scene_length.get()}")
    
    # 创建界面显示
    tk.Label(root, text="参数恢复测试", font=("Arial", 16)).pack(pady=10)
    
    tk.Label(root, text=f"敏感度: {scene_threshold.get():.1f}").pack(pady=5)
    tk.Scale(root, from_=1.0, to=100.0, variable=scene_threshold, 
             orient="horizontal", length=300).pack(pady=5)
    
    tk.Label(root, text=f"最小长度: {min_scene_length.get():.1f}").pack(pady=5)
    tk.Scale(root, from_=0.1, to=10.0, variable=min_scene_length, 
             orient="horizontal", length=300, resolution=0.1).pack(pady=5)
    
    def close_test():
        print(f"📊 最终值: 敏感度={scene_threshold.get()}, 最小长度={min_scene_length.get()}")
        root.destroy()
    
    tk.Button(root, text="关闭测试", command=close_test).pack(pady=10)
    
    # 检查参数是否正确恢复
    if (abs(scene_threshold.get() - test_params['scene_threshold']) < 0.1 and 
        abs(min_scene_length.get() - test_params['min_scene_length']) < 0.1):
        print("✅ 参数恢复成功！")
    else:
        print("❌ 参数恢复失败！")
    
    print("\n💡 请检查界面上的参数值是否正确")
    print("关闭窗口后将显示最终结果")
    
    root.mainloop()

if __name__ == "__main__":
    test_parameter_restore()
