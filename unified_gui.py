#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能字幕处理工具集 - 统一GUI界面
集成字幕匹配和字幕合并功能在一个窗口中
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import logging
import datetime
import sys
from typing import List, Dict

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from subtitle_matcher import SubtitleMatcher
    from subtitle_timestamp_merger import SubtitleTimestampMerger
    TRADITIONAL_AVAILABLE = True
except ImportError as e:
    print(f"导入传统模块失败: {e}")
    print("请确保所有必要的文件都在同一目录下")
    TRADITIONAL_AVAILABLE = False

try:
    from ai_subtitle_matcher import AISubtitleMatcher, SENTENCE_TRANSFORMERS_AVAILABLE
    AI_AVAILABLE = SENTENCE_TRANSFORMERS_AVAILABLE
except ImportError as e:
    print(f"导入AI模块失败: {e}")
    AI_AVAILABLE = False


class UnifiedGUI:
    """统一的字幕处理工具GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能字幕处理工具集")
        self.root.geometry("900x800")
        
        # 设置中文字体
        self.default_font = ("Microsoft YaHei", 10)
        self.root.option_add("*Font", self.default_font)
        
        # 初始化变量
        self.setup_variables()
        
        # 设置日志
        self.setup_logger()
        
        # 创建界面
        self.create_widgets()
        
        # 设置默认路径
        self.set_default_paths()

        # 检查算法可用性
        self.check_algorithm_availability()
    
    def setup_variables(self):
        """初始化变量"""
        # 字幕匹配相关变量
        self.matcher_file_a = tk.StringVar()
        self.matcher_file_b = tk.StringVar()
        self.max_window = tk.IntVar(value=5)
        self.max_time_gap = tk.DoubleVar(value=8.0)

        # 新增算法选择和参数
        self.matching_algorithm = tk.StringVar(value="enhanced")
        self.similarity_threshold = tk.DoubleVar(value=0.3)
        self.context_weight = tk.DoubleVar(value=0.3)
        
        # 字幕合并相关变量（两组模式）
        # 二次剪辑组
        self.edited_subtitle_file = tk.StringVar()
        self.edited_timestamp_file = tk.StringVar()
        self.edited_output_file = tk.StringVar(value="二次剪辑合并结果.txt")

        # 原素材组
        self.original_subtitle_file = tk.StringVar()
        self.original_timestamp_file = tk.StringVar()
        self.original_output_file = tk.StringVar(value="原素材合并结果.txt")

        # 导入时间戳相关变量
        self.importer_project_folder = tk.StringVar()
        self.importer_selected_project = tk.StringVar()
        self.importer_timestamp_file = tk.StringVar()

        # 字幕文件变量，设置默认值
        default_subtitle_path = os.path.join(os.getcwd(), "叙事性字幕.srt")
        self.importer_subtitle_file = tk.StringVar(value=default_subtitle_path)

        self.project_list = []
        self.draft_list = []

        # 提取时间戳相关变量
        self.extractor_project_folder = tk.StringVar()
        self.extractor_selected_project = tk.StringVar()
        self.extractor_selected_draft = tk.StringVar()
        self.extractor_project_list = []
        self.extractor_draft_list = []

        # 场景检测相关变量
        self.scene_video_file = tk.StringVar()
        self.scene_output_file = tk.StringVar()  # 保留兼容性
        self.scene_output_folder = tk.StringVar()
        self.scene_threshold = tk.DoubleVar(value=50.0)
        self.min_scene_length = tk.DoubleVar(value=1.0)
        self.max_scene_length = tk.DoubleVar(value=30.0)
        self.long_scene_sensitivity = tk.DoubleVar(value=0.7)  # 长场景分割敏感度系数
        self.segment_length = tk.DoubleVar(value=7.0)

        # 文件记忆功能
        self.project_file_memory = {}  # {project_folder: selected_file}
        self.folder_memory = {}  # 各种文件夹选择的记忆
        self.parameter_memory = {}  # 参数设置的记忆
        
        # 工具实例
        self.matcher = None
        self.merger = None
    
    def setup_logger(self):
        """设置日志记录器"""
        log_filename = "统一工具日志.log"
        
        self.logger = logging.getLogger('UnifiedGUI')
        self.logger.setLevel(logging.DEBUG)
        
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        self.logger.info("=" * 60)
        self.logger.info("智能字幕处理工具集启动")
        self.logger.info(f"日志文件: {log_filename}")
        self.logger.info("=" * 60)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="智能字幕处理工具集", 
            font=("Microsoft YaHei", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True)
        
        # 创建提取时间戳选项卡（第一个）
        self.create_timestamp_extractor_tab()

        # 创建字幕合并选项卡（第二个）
        self.create_merger_tab()

        # 创建智能字幕匹配选项卡（第三个）
        self.create_matcher_tab()

        # 创建导入时间戳选项卡（第四个）
        self.create_timestamp_importer_tab()

        # 创建场景检测选项卡（第五个）
        self.create_scene_detector_tab()

        # 创建分段合并选项卡（第六个）
        self.create_segment_merge_tab()

        # 状态栏
        self.create_status_bar()

        # 加载项目文件记忆、文件夹记忆和参数记忆
        self.load_project_memory()
        self.load_folder_memory()
        # 延迟加载参数记忆，确保GUI组件已创建
        self.root.after(100, self.load_parameter_memory)

        # 恢复上次的项目文件夹选择
        self.restore_folder_selections()

        # 恢复字幕文件选择
        self.restore_subtitle_file_selection()

        # 初始化字幕文件列表
        self.root.after(200, self.refresh_subtitle_files)
    
    def create_matcher_tab(self):
        """创建字幕匹配选项卡"""
        # 创建选项卡框架
        matcher_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(matcher_frame, text="🎯 智能字幕匹配")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(matcher_frame, text="文件选择", padding="10")
        file_frame.pack(fill="x", pady=(0, 10))
        
        # 文件A选择
        ttk.Label(file_frame, text="二次剪辑字幕文件:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=5
        )
        ttk.Entry(file_frame, textvariable=self.matcher_file_a, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(file_frame, text="浏览", command=self.browse_matcher_file_a).grid(
            row=0, column=2, pady=5
        )
        
        # 文件B选择
        ttk.Label(file_frame, text="原始素材时间戳文件:", font=self.default_font).grid(
            row=1, column=0, sticky="w", pady=5
        )
        ttk.Entry(file_frame, textvariable=self.matcher_file_b, width=50).grid(
            row=1, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(file_frame, text="浏览", command=self.browse_matcher_file_b).grid(
            row=1, column=2, pady=5
        )
        
        file_frame.columnconfigure(1, weight=1)
        
        # 算法选择区域
        algo_frame = ttk.LabelFrame(matcher_frame, text="算法选择", padding="10")
        algo_frame.pack(fill="x", pady=(0, 10))

        # 算法选择单选按钮
        ttk.Radiobutton(algo_frame, text="增强传统算法", variable=self.matching_algorithm, value="enhanced",
                       command=self.on_algorithm_changed).grid(
            row=0, column=0, sticky="w", pady=2
        )
        ttk.Label(algo_frame, text="(子字符串匹配 + 相似度匹配 + 上下文感知)",
                 font=("Arial", 8), foreground="gray").grid(
            row=0, column=1, sticky="w", padx=(10, 0), pady=2
        )

        ttk.Radiobutton(algo_frame, text="AI语义匹配", variable=self.matching_algorithm, value="ai",
                       command=self.on_algorithm_changed).grid(
            row=1, column=0, sticky="w", pady=2
        )
        ttk.Label(algo_frame, text="(基于预训练语言模型的语义理解)",
                 font=("Arial", 8), foreground="gray").grid(
            row=1, column=1, sticky="w", padx=(10, 0), pady=2
        )

        # 参数设置区域
        param_frame = ttk.LabelFrame(matcher_frame, text="参数设置", padding="10")
        param_frame.pack(fill="x", pady=(0, 10))

        # 相似度阈值
        ttk.Label(param_frame, text="相似度阈值:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=2
        )
        similarity_scale = ttk.Scale(param_frame, from_=0.1, to=0.8, variable=self.similarity_threshold,
                                   orient=tk.HORIZONTAL, length=150)
        similarity_scale.grid(row=0, column=1, sticky="w", padx=5, pady=2)
        self.similarity_label = ttk.Label(param_frame, text="0.3")
        self.similarity_label.grid(row=0, column=2, sticky="w", padx=5, pady=2)
        similarity_scale.configure(command=self.update_similarity_label)

        # 上下文权重
        ttk.Label(param_frame, text="上下文权重:", font=self.default_font).grid(
            row=1, column=0, sticky="w", pady=2
        )
        context_scale = ttk.Scale(param_frame, from_=0.1, to=0.7, variable=self.context_weight,
                                orient=tk.HORIZONTAL, length=150)
        context_scale.grid(row=1, column=1, sticky="w", padx=5, pady=2)
        self.context_label = ttk.Label(param_frame, text="0.3")
        self.context_label.grid(row=1, column=2, sticky="w", padx=5, pady=2)
        context_scale.configure(command=self.update_context_label)

        # 最大时间间隔设置
        ttk.Label(param_frame, text="最大时间间隔:", font=self.default_font).grid(
            row=2, column=0, sticky="w", pady=2
        )
        time_gap_spinbox = ttk.Spinbox(param_frame, from_=5.0, to=120.0, increment=5.0, textvariable=self.max_time_gap, width=10, command=self.save_parameter_memory)
        time_gap_spinbox.grid(row=2, column=1, sticky="w", padx=5, pady=2)
        # 绑定变量变化事件
        self.max_time_gap.trace('w', lambda *args: self.save_parameter_memory())
        ttk.Label(param_frame, text="秒", font=self.default_font).grid(
            row=2, column=2, sticky="w", pady=2
        )

        # 说明标签
        help_label = ttk.Label(param_frame, text="超过此时间间隔的镜头将被分离到不同区块",
                              font=("Arial", 8), foreground="gray")
        help_label.grid(row=3, column=0, columnspan=3, sticky="w", pady=(5, 0))
        
        # 操作按钮
        button_frame = ttk.Frame(matcher_frame)
        button_frame.pack(fill="x", pady=(0, 10))
        
        self.matcher_button = ttk.Button(
            button_frame, 
            text="开始匹配", 
            command=self.start_matching,
            style="Accent.TButton"
        )
        self.matcher_button.pack(side="left", padx=(0, 10))
        
        ttk.Button(button_frame, text="清空结果", command=self.clear_matcher_results).pack(side="left")
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(matcher_frame, text="匹配结果", padding="10")
        result_frame.pack(fill="both", expand=True)
        
        self.matcher_result_text = scrolledtext.ScrolledText(
            result_frame, 
            height=12, 
            font=("Consolas", 9),
            wrap=tk.WORD
        )
        self.matcher_result_text.pack(fill="both", expand=True)

    def update_similarity_label(self, value):
        """更新相似度阈值标签"""
        self.similarity_label.config(text=f"{float(value):.2f}")
        # 保存参数到记忆
        self.save_parameter_memory()

    def update_context_label(self, value):
        """更新上下文权重标签"""
        self.context_label.config(text=f"{float(value):.2f}")
        # 保存参数到记忆
        self.save_parameter_memory()

    def on_algorithm_changed(self):
        """算法选择改变时的回调"""
        # 保存参数到记忆
        self.save_parameter_memory()

    def create_merger_tab(self):
        """创建字幕合并选项卡"""
        # 创建选项卡框架
        merger_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(merger_frame, text="🔗 字幕与时间戳合并")

        # 二次剪辑组区域
        edited_frame = ttk.LabelFrame(merger_frame, text="📝 二次剪辑组", padding="10")
        edited_frame.pack(fill="x", pady=(0, 10))

        # 二次剪辑字幕文件
        ttk.Label(edited_frame, text="二次剪辑字幕文件:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=5
        )
        ttk.Entry(edited_frame, textvariable=self.edited_subtitle_file, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(edited_frame, text="浏览", command=self.browse_edited_subtitle_file).grid(
            row=0, column=2, pady=5
        )

        # 二次剪辑时间戳文件
        ttk.Label(edited_frame, text="二次剪辑时间戳文件:", font=self.default_font).grid(
            row=1, column=0, sticky="w", pady=5
        )
        ttk.Entry(edited_frame, textvariable=self.edited_timestamp_file, width=50).grid(
            row=1, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(edited_frame, text="浏览", command=self.browse_edited_timestamp_file).grid(
            row=1, column=2, pady=5
        )

        # 二次剪辑输出文件
        ttk.Label(edited_frame, text="二次剪辑输出文件:", font=self.default_font).grid(
            row=2, column=0, sticky="w", pady=5
        )
        ttk.Entry(edited_frame, textvariable=self.edited_output_file, width=50).grid(
            row=2, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(edited_frame, text="浏览", command=self.browse_edited_output_file).grid(
            row=2, column=2, pady=5
        )

        edited_frame.columnconfigure(1, weight=1)

        # 原素材组区域
        original_frame = ttk.LabelFrame(merger_frame, text="🎬 原素材组", padding="10")
        original_frame.pack(fill="x", pady=(0, 10))

        # 原素材字幕文件
        ttk.Label(original_frame, text="原素材字幕文件:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=5
        )
        ttk.Entry(original_frame, textvariable=self.original_subtitle_file, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(original_frame, text="浏览", command=self.browse_original_subtitle_file).grid(
            row=0, column=2, pady=5
        )

        # 原素材时间戳文件
        ttk.Label(original_frame, text="原素材时间戳文件:", font=self.default_font).grid(
            row=1, column=0, sticky="w", pady=5
        )
        ttk.Entry(original_frame, textvariable=self.original_timestamp_file, width=50).grid(
            row=1, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(original_frame, text="浏览", command=self.browse_original_timestamp_file).grid(
            row=1, column=2, pady=5
        )

        # 原素材输出文件
        ttk.Label(original_frame, text="原素材输出文件:", font=self.default_font).grid(
            row=2, column=0, sticky="w", pady=5
        )
        ttk.Entry(original_frame, textvariable=self.original_output_file, width=50).grid(
            row=2, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(original_frame, text="浏览", command=self.browse_original_output_file).grid(
            row=2, column=2, pady=5
        )

        original_frame.columnconfigure(1, weight=1)

        # 功能说明区域
        desc_frame = ttk.LabelFrame(merger_frame, text="功能说明", padding="10")
        desc_frame.pack(fill="x", pady=(0, 10))

        description = """双组合并功能：
• 分别处理二次剪辑和原素材的字幕与时间戳合并
• 二次剪辑组：处理经过编辑的字幕内容
• 原素材组：处理原始视频的字幕内容
• 两组独立输出，便于后续分别使用
• 自动从"提取时间戳"功能的输出中选择文件"""

        ttk.Label(desc_frame, text=description, font=("Microsoft YaHei", 9), justify="left").pack(anchor="w")

        # 操作按钮
        button_frame = ttk.Frame(merger_frame)
        button_frame.pack(fill="x", pady=(0, 10))

        self.merger_button = ttk.Button(
            button_frame,
            text="开始双组合并",
            command=self.start_merging,
            style="Accent.TButton"
        )
        self.merger_button.pack(side="left", padx=(0, 10))

        ttk.Button(button_frame, text="清空结果", command=self.clear_merger_results).pack(side="left")

        # 智能填充按钮
        ttk.Button(
            button_frame,
            text="智能填充",
            command=self.auto_fill_merger_files
        ).pack(side="left", padx=(10, 0))

        # 结果显示区域
        result_frame = ttk.LabelFrame(merger_frame, text="合并结果", padding="10")
        result_frame.pack(fill="both", expand=True)

        self.merger_result_text = scrolledtext.ScrolledText(
            result_frame,
            height=12,
            font=("Consolas", 9),
            wrap=tk.WORD
        )
        self.merger_result_text.pack(fill="both", expand=True)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(side="bottom", fill="x")

    def check_algorithm_availability(self):
        """检查算法可用性并更新UI"""
        if not AI_AVAILABLE:
            # 如果AI不可用，确保默认选择传统算法
            if self.matching_algorithm.get() == "ai":
                self.matching_algorithm.set("enhanced")

            # 在日志中记录
            self.logger.warning("AI算法不可用 - 需要安装 sentence-transformers")

        if not TRADITIONAL_AVAILABLE:
            self.logger.error("传统算法不可用 - 核心模块缺失")

        # 记录可用算法
        available_algos = []
        if TRADITIONAL_AVAILABLE:
            available_algos.append("增强传统算法")
        if AI_AVAILABLE:
            available_algos.append("AI语义匹配")

        if available_algos:
            self.logger.info(f"可用算法: {', '.join(available_algos)}")
        else:
            self.logger.error("没有可用的匹配算法")

    def set_default_paths(self):
        """设置默认路径"""
        # 字幕匹配默认路径 - 优先使用字幕合并的输出结果
        self.auto_fill_matcher_from_merger_output()

        # 如果没有合并输出，则使用原始文件
        if not self.matcher_file_a.get():
            if os.path.exists("二次剪辑素材/二次剪辑OCR字幕.txt"):
                self.matcher_file_a.set("二次剪辑素材/二次剪辑OCR字幕.txt")

        if not self.matcher_file_b.get():
            if os.path.exists("原视频匹配素材/原素材分镜时间戳.txt"):
                self.matcher_file_b.set("原视频匹配素材/原素材分镜时间戳.txt")

        # 字幕合并默认路径（双组模式）
        merger_dir = "字幕与时间戳合并"

        # 自动填充双组文件路径
        self.auto_fill_merger_files_on_startup()

        # 设置默认输出文件
        self.edited_output_file.set(os.path.join(merger_dir, "二次剪辑合并结果.txt"))
        self.original_output_file.set(os.path.join(merger_dir, "原素材合并结果.txt"))

    def auto_fill_matcher_from_merger_output(self):
        """从字幕合并输出自动填充智能字幕匹配的输入"""
        merger_dir = "字幕与时间戳合并"

        # 检查合并输出文件
        edited_output = os.path.join(merger_dir, "二次剪辑合并结果.txt")
        original_output = os.path.join(merger_dir, "原素材合并结果.txt")

        # 优先使用最新的合并结果
        merger_outputs = []
        if os.path.exists(edited_output):
            merger_outputs.append((edited_output, os.path.getmtime(edited_output), "edited"))
        if os.path.exists(original_output):
            merger_outputs.append((original_output, os.path.getmtime(original_output), "original"))

        if merger_outputs:
            # 按修改时间排序，选择最新的
            merger_outputs.sort(key=lambda x: x[1], reverse=True)

            # 智能分配文件
            for file_path, _, file_type in merger_outputs:
                if file_type == "edited" and not self.matcher_file_a.get():
                    # 二次剪辑合并结果 → 智能匹配的文件A（二次剪辑字幕文件）
                    self.matcher_file_a.set(file_path)
                elif file_type == "original" and not self.matcher_file_b.get():
                    # 原素材合并结果 → 智能匹配的文件B（原始素材时间戳文件）
                    self.matcher_file_b.set(file_path)

            # 如果还有空字段，用剩余文件填充
            if not self.matcher_file_a.get() and merger_outputs:
                self.matcher_file_a.set(merger_outputs[0][0])
            elif not self.matcher_file_b.get() and len(merger_outputs) > 1:
                # 找一个还没被使用的文件
                used_file = self.matcher_file_a.get()
                for file_path, _, _ in merger_outputs:
                    if file_path != used_file:
                        self.matcher_file_b.set(file_path)
                        break

    def auto_fill_merger_files_on_startup(self):
        """启动时自动填充双组合并文件路径"""
        # 二次剪辑组默认路径
        if os.path.exists("二次剪辑素材/二次剪辑OCR字幕.txt"):
            self.edited_subtitle_file.set("二次剪辑素材/二次剪辑OCR字幕.txt")
        if os.path.exists("二次剪辑素材/二次剪辑分镜时间戳.txt"):
            self.edited_timestamp_file.set("二次剪辑素材/二次剪辑分镜时间戳.txt")

        # 原素材组默认路径
        if os.path.exists("原视频匹配素材/原素材OCR字幕.txt"):
            self.original_subtitle_file.set("原视频匹配素材/原素材OCR字幕.txt")
        if os.path.exists("原视频匹配素材/原素材分镜时间戳.txt"):
            self.original_timestamp_file.set("原视频匹配素材/原素材分镜时间戳.txt")

        # 从提取时间戳功能的输出中自动选择文件
        self.auto_fill_from_timestamp_extraction()

    def auto_fill_from_timestamp_extraction(self):
        """从提取时间戳功能的输出中自动选择文件"""
        # 检查提取时间戳的输出目录
        timestamp_dir = "timestamp_reports"
        if os.path.exists(timestamp_dir):
            # 查找时间戳文件
            timestamp_files = []
            for file in os.listdir(timestamp_dir):
                if file.endswith(".txt"):
                    file_path = os.path.join(timestamp_dir, file)
                    timestamp_files.append((file_path, os.path.getmtime(file_path)))

            if timestamp_files:
                # 按修改时间排序，选择最新的
                timestamp_files.sort(key=lambda x: x[1], reverse=True)

                # 根据文件名智能分配
                for file_path, _ in timestamp_files:
                    filename = os.path.basename(file_path)

                    # 根据文件名判断是二次剪辑还是原素材
                    if "主轨道" in filename or "第一" in filename or "edited" in filename.lower():
                        if not self.edited_timestamp_file.get():
                            self.edited_timestamp_file.set(file_path)
                    elif "第二轨道" in filename or "第二" in filename or "original" in filename.lower():
                        if not self.original_timestamp_file.get():
                            self.original_timestamp_file.set(file_path)

                # 如果还有空字段，用剩余文件填充
                if not self.edited_timestamp_file.get() and timestamp_files:
                    self.edited_timestamp_file.set(timestamp_files[0][0])
                elif not self.original_timestamp_file.get() and len(timestamp_files) > 1:
                    # 找一个还没被使用的文件
                    used_file = self.edited_timestamp_file.get()
                    for file_path, _ in timestamp_files:
                        if file_path != used_file:
                            self.original_timestamp_file.set(file_path)
                            break

    # 文件浏览方法
    def browse_matcher_file_a(self):
        """浏览字幕匹配文件A"""
        # 从记忆中获取初始目录
        initial_dir = self.folder_memory.get('matcher_file_a', os.getcwd())

        filename = filedialog.askopenfilename(
            title="选择二次剪辑字幕文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialdir=initial_dir
        )
        if filename:
            self.matcher_file_a.set(filename)
            # 保存文件夹路径到记忆中
            file_dir = os.path.dirname(filename)
            self.folder_memory['matcher_file_a'] = file_dir
            self.save_folder_memory()
            self.logger.info(f"用户选择字幕匹配文件A: {filename}")

    def browse_matcher_file_b(self):
        """浏览字幕匹配文件B"""
        # 从记忆中获取初始目录
        initial_dir = self.folder_memory.get('matcher_file_b', os.getcwd())

        filename = filedialog.askopenfilename(
            title="选择原始素材时间戳文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialdir=initial_dir
        )
        if filename:
            self.matcher_file_b.set(filename)
            # 保存文件夹路径到记忆中
            file_dir = os.path.dirname(filename)
            self.folder_memory['matcher_file_b'] = file_dir
            self.save_folder_memory()
            self.logger.info(f"用户选择字幕匹配文件B: {filename}")

    # 二次剪辑组浏览方法
    def browse_edited_subtitle_file(self):
        """浏览二次剪辑字幕文件"""
        initial_dir = self.folder_memory.get('edited_subtitle_file', "二次剪辑素材")
        filename = filedialog.askopenfilename(
            title="选择二次剪辑字幕文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialdir=initial_dir
        )
        if filename:
            self.edited_subtitle_file.set(filename)
            file_dir = os.path.dirname(filename)
            self.folder_memory['edited_subtitle_file'] = file_dir
            self.save_folder_memory()

    def browse_edited_timestamp_file(self):
        """浏览二次剪辑时间戳文件"""
        initial_dir = self.folder_memory.get('edited_timestamp_file', "二次剪辑素材")
        filename = filedialog.askopenfilename(
            title="选择二次剪辑时间戳文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialdir=initial_dir
        )
        if filename:
            self.edited_timestamp_file.set(filename)
            file_dir = os.path.dirname(filename)
            self.folder_memory['edited_timestamp_file'] = file_dir
            self.save_folder_memory()

    def browse_edited_output_file(self):
        """浏览二次剪辑输出文件"""
        initial_dir = self.folder_memory.get('edited_output_file', "字幕与时间戳合并")
        filename = filedialog.asksaveasfilename(
            title="选择二次剪辑输出文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialdir=initial_dir
        )
        if filename:
            self.edited_output_file.set(filename)
            file_dir = os.path.dirname(filename)
            self.folder_memory['edited_output_file'] = file_dir
            self.save_folder_memory()

    # 原素材组浏览方法
    def browse_original_subtitle_file(self):
        """浏览原素材字幕文件"""
        initial_dir = self.folder_memory.get('original_subtitle_file', "原视频匹配素材")
        filename = filedialog.askopenfilename(
            title="选择原素材字幕文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialdir=initial_dir
        )
        if filename:
            self.original_subtitle_file.set(filename)
            file_dir = os.path.dirname(filename)
            self.folder_memory['original_subtitle_file'] = file_dir
            self.save_folder_memory()

    def browse_original_timestamp_file(self):
        """浏览原素材时间戳文件"""
        initial_dir = self.folder_memory.get('original_timestamp_file', "原视频匹配素材")
        filename = filedialog.askopenfilename(
            title="选择原素材时间戳文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialdir=initial_dir
        )
        if filename:
            self.original_timestamp_file.set(filename)
            file_dir = os.path.dirname(filename)
            self.folder_memory['original_timestamp_file'] = file_dir
            self.save_folder_memory()

    def browse_original_output_file(self):
        """浏览原素材输出文件"""
        initial_dir = self.folder_memory.get('original_output_file', "字幕与时间戳合并")
        filename = filedialog.asksaveasfilename(
            title="选择原素材输出文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialdir=initial_dir
        )
        if filename:
            self.original_output_file.set(filename)
            file_dir = os.path.dirname(filename)
            self.folder_memory['original_output_file'] = file_dir
            self.save_folder_memory()

    def auto_fill_merger_files(self):
        """智能填充双组文件路径"""
        filled_count = 0

        # 先尝试从提取时间戳功能的输出中自动选择文件
        self.auto_fill_from_timestamp_extraction()

        # 二次剪辑组智能填充
        edited_subtitle_candidates = [
            "二次剪辑素材/二次剪辑OCR字幕.txt",
            "二次剪辑素材/二次剪辑.txt",
            "提取时间戳/二次剪辑字幕.txt"
        ]

        edited_timestamp_candidates = [
            "二次剪辑素材/二次剪辑分镜时间戳.txt",
            "二次剪辑素材/二次剪辑时间戳.txt",
            "提取时间戳/二次剪辑时间戳.txt",
            "timestamp_reports/主轨道.txt"
        ]

        # 只有当字段为空时才填充
        if not self.edited_subtitle_file.get():
            for candidate in edited_subtitle_candidates:
                if os.path.exists(candidate):
                    self.edited_subtitle_file.set(candidate)
                    filled_count += 1
                    break

        if not self.edited_timestamp_file.get():
            for candidate in edited_timestamp_candidates:
                if os.path.exists(candidate):
                    self.edited_timestamp_file.set(candidate)
                    filled_count += 1
                    break

        # 原素材组智能填充
        original_subtitle_candidates = [
            "原视频匹配素材/原素材OCR字幕.txt",
            "原视频匹配素材/原素材.txt",
            "提取时间戳/原素材字幕.txt"
        ]

        original_timestamp_candidates = [
            "原视频匹配素材/原素材分镜时间戳.txt",
            "原视频匹配素材/原素材时间戳.txt",
            "提取时间戳/原素材时间戳.txt",
            "timestamp_reports/第二轨道.txt"
        ]

        # 只有当字段为空时才填充
        if not self.original_subtitle_file.get():
            for candidate in original_subtitle_candidates:
                if os.path.exists(candidate):
                    self.original_subtitle_file.set(candidate)
                    filled_count += 1
                    break

        if not self.original_timestamp_file.get():
            for candidate in original_timestamp_candidates:
                if os.path.exists(candidate):
                    self.original_timestamp_file.set(candidate)
                    filled_count += 1
                    break

        # 确保输出目录存在
        output_dir = "字幕与时间戳合并"
        os.makedirs(output_dir, exist_ok=True)

        if filled_count > 0:
            messagebox.showinfo("智能填充完成", f"已自动填充 {filled_count} 个文件路径")
        else:
            messagebox.showwarning("智能填充", "未找到可自动填充的文件，请手动选择文件")

    # 字幕匹配相关方法
    def validate_matcher_inputs(self) -> bool:
        """验证字幕匹配输入"""
        if not self.matcher_file_a.get():
            messagebox.showerror("错误", "请选择二次剪辑字幕文件")
            return False

        if not self.matcher_file_b.get():
            messagebox.showerror("错误", "请选择原始素材时间戳文件")
            return False

        if not os.path.exists(self.matcher_file_a.get()):
            messagebox.showerror("错误", "二次剪辑字幕文件不存在")
            return False

        if not os.path.exists(self.matcher_file_b.get()):
            messagebox.showerror("错误", "原始素材时间戳文件不存在")
            return False

        return True

    def start_matching(self):
        """开始字幕匹配"""
        if not self.validate_matcher_inputs():
            return

        # 检查选择的算法是否可用
        algorithm = self.matching_algorithm.get()
        if algorithm == "ai" and not AI_AVAILABLE:
            messagebox.showerror("错误", "AI算法不可用\n请安装 sentence-transformers:\npip install sentence-transformers")
            return

        if algorithm == "enhanced" and not TRADITIONAL_AVAILABLE:
            messagebox.showerror("错误", "传统算法不可用\n请检查核心模块是否正确安装")
            return

        algo_name = "增强传统算法" if algorithm == "enhanced" else "AI语义匹配"
        self.logger.info(f"用户开始字幕匹配处理 - 使用{algo_name}")
        self.logger.info(f"文件A: {self.matcher_file_a.get()}")
        self.logger.info(f"文件B: {self.matcher_file_b.get()}")
        self.logger.info(f"相似度阈值: {self.similarity_threshold.get()}")
        self.logger.info(f"上下文权重: {self.context_weight.get()}")
        self.logger.info(f"最大时间间隔: {self.max_time_gap.get()}秒")

        # 禁用按钮
        self.matcher_button.config(state="disabled")
        self.status_var.set("正在进行字幕匹配...")

        # 清空结果区域
        self.matcher_result_text.delete(1.0, tk.END)

        # 在新线程中执行处理
        thread = threading.Thread(target=self.matching_worker)
        thread.daemon = True
        thread.start()

    def matching_worker(self):
        """字幕匹配工作线程"""
        try:
            algorithm = self.matching_algorithm.get()

            if algorithm == "enhanced":
                # 使用增强传统算法
                self.matcher = SubtitleMatcher()

                # 设置相似度匹配参数
                self.matcher.similarity_threshold = self.similarity_threshold.get()
                self.matcher.context_weight = self.context_weight.get()

                # 处理文件
                success, timestamps, error = self.matcher.process_files(
                    self.matcher_file_a.get(),
                    self.matcher_file_b.get(),
                    auto_save=True,
                    max_time_gap=self.max_time_gap.get()
                )

            elif algorithm == "ai":
                # 使用AI算法
                try:
                    from ai_subtitle_matcher import AISubtitleMatcher, SENTENCE_TRANSFORMERS_AVAILABLE

                    if not SENTENCE_TRANSFORMERS_AVAILABLE:
                        error_msg = "AI算法不可用：请安装 sentence-transformers\n运行命令: pip install sentence-transformers"
                        self.root.after(0, self.matching_completed, False, [], error_msg)
                        return

                    self.ai_matcher = AISubtitleMatcher()
                    self.ai_matcher.semantic_threshold = self.similarity_threshold.get()

                    # 处理文件
                    success, timestamps, error = self.ai_matcher.process_files(
                        self.matcher_file_a.get(),
                        self.matcher_file_b.get(),
                        "AI匹配结果.txt"
                    )

                except ImportError:
                    error_msg = "AI算法不可用：ai_subtitle_matcher模块未找到"
                    self.root.after(0, self.matching_completed, False, [], error_msg)
                    return

            else:
                error_msg = f"未知的算法类型: {algorithm}"
                self.root.after(0, self.matching_completed, False, [], error_msg)
                return

            # 在主线程中更新UI
            self.root.after(0, self.matching_completed, success, timestamps, error, algorithm)

        except Exception as e:
            error_msg = f"字幕匹配过程中发生错误: {e}"
            self.logger.error(error_msg)
            self.root.after(0, self.matching_completed, False, [], error_msg, "unknown")

    def matching_completed(self, success: bool, timestamps: list, error: str, algorithm: str = "unknown"):
        """字幕匹配完成回调"""
        # 恢复按钮
        self.matcher_button.config(state="normal")

        if success:
            algo_name = "增强传统算法" if algorithm == "enhanced" else "AI语义匹配" if algorithm == "ai" else "未知算法"
            self.status_var.set("字幕匹配完成")
            self.matcher_result_text.insert(tk.END, f"✅ 字幕匹配成功！（{algo_name}）\n\n")
            self.matcher_result_text.insert(tk.END, f"生成了 {len(timestamps)} 个时间戳区块\n\n")

            # 显示算法特定的统计信息
            if algorithm == "enhanced" and hasattr(self, 'matcher'):
                # 显示增强传统算法的详细统计
                match_results = getattr(self.matcher, 'match_results', [])
                if match_results:
                    successful = sum(1 for r in match_results if r.matched_sequences)
                    total = len(match_results)
                    success_rate = (successful / total) * 100 if total > 0 else 0

                    self.matcher_result_text.insert(tk.END, f"匹配统计:\n")
                    self.matcher_result_text.insert(tk.END, f"  总台词数: {total}\n")
                    self.matcher_result_text.insert(tk.END, f"  成功匹配: {successful}\n")
                    self.matcher_result_text.insert(tk.END, f"  匹配成功率: {success_rate:.1f}%\n\n")

            elif algorithm == "ai" and hasattr(self, 'ai_matcher'):
                # 显示AI算法的详细统计
                match_results = getattr(self.ai_matcher, 'match_results', [])
                if match_results:
                    successful = sum(1 for r in match_results if r.matched_sequences)
                    total = len(match_results)
                    success_rate = (successful / total) * 100 if total > 0 else 0

                    # 计算平均置信度
                    confidences = [r.confidence_score for r in match_results if r.matched_sequences]
                    avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                    self.matcher_result_text.insert(tk.END, f"匹配统计:\n")
                    self.matcher_result_text.insert(tk.END, f"  总台词数: {total}\n")
                    self.matcher_result_text.insert(tk.END, f"  成功匹配: {successful}\n")
                    self.matcher_result_text.insert(tk.END, f"  匹配成功率: {success_rate:.1f}%\n")
                    self.matcher_result_text.insert(tk.END, f"  平均置信度: {avg_confidence:.3f}\n\n")

            self.matcher_result_text.insert(tk.END, "时间戳列表:\n")
            for i, timestamp in enumerate(timestamps, 1):
                self.matcher_result_text.insert(tk.END, f"{i}. {timestamp}\n")

            # 自动保存结果
            self.auto_save_matcher_results(timestamps)

            # 获取详细统计信息
            stats_info = self.get_matcher_statistics()

            # 弹出详细统计信息
            messagebox.showinfo("字幕匹配完成", f"""✅ 处理成功！

📊 匹配统计信息：
• 总台词数：{stats_info.get('total_subtitles', 0)}
• 成功匹配数：{stats_info.get('successful_matches', 0)}
• 规则1匹配（精确）：{stats_info.get('rule1_matches', 0)}
• 规则2匹配（多镜头）：{stats_info.get('rule2_matches', 0)}
• 匹配成功率：{stats_info.get('success_rate', 0):.1f}%
• 生成区块数：{len(timestamps)}

📁 结果已保存到：匹配结果.txt""")
        else:
            self.status_var.set("字幕匹配失败")
            self.matcher_result_text.insert(tk.END, "❌ 字幕匹配失败！\n\n")
            self.matcher_result_text.insert(tk.END, f"错误信息: {error}")
            messagebox.showerror("失败", f"字幕匹配失败:\n{error}")

    def get_matcher_statistics(self):
        """获取字幕匹配的统计信息"""
        if not self.matcher:
            return {}

        try:
            # 从匹配器获取统计信息
            total_subtitles = len(self.matcher.file_a_entries) if hasattr(self.matcher, 'file_a_entries') else 0
            successful_matches = len(self.matcher.matched_pairs) if hasattr(self.matcher, 'matched_pairs') else 0

            # 计算规则匹配数量
            rule1_matches = 0
            rule2_matches = 0

            if hasattr(self.matcher, 'matched_pairs'):
                for pair in self.matcher.matched_pairs:
                    if isinstance(pair[1], list):  # 多镜头匹配
                        rule2_matches += 1
                    else:  # 单镜头匹配
                        rule1_matches += 1

            success_rate = (successful_matches / total_subtitles * 100) if total_subtitles > 0 else 0

            return {
                'total_subtitles': total_subtitles,
                'successful_matches': successful_matches,
                'rule1_matches': rule1_matches,
                'rule2_matches': rule2_matches,
                'success_rate': success_rate
            }
        except Exception as e:
            self.logger.error(f"获取匹配统计信息失败: {e}")
            return {}

    def auto_save_matcher_results(self, timestamps):
        """自动保存字幕匹配结果 - 简洁格式"""
        try:
            filename = "GUI匹配结果.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                for i, timestamp in enumerate(timestamps, 1):
                    f.write(f"{i}. {timestamp}\n")
            self.logger.info(f"GUI匹配结果已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"GUI自动保存匹配结果失败: {e}")

    def clear_matcher_results(self):
        """清空字幕匹配结果"""
        self.matcher_result_text.delete(1.0, tk.END)
        self.status_var.set("就绪")
        self.logger.info("用户清空了字幕匹配结果显示")

    # 字幕合并相关方法
    def validate_merger_inputs(self) -> bool:
        """验证双组字幕合并输入"""
        # 二次剪辑组验证
        if not self.edited_subtitle_file.get():
            messagebox.showerror("错误", "请选择二次剪辑字幕文件")
            return False

        if not self.edited_timestamp_file.get():
            messagebox.showerror("错误", "请选择二次剪辑时间戳文件")
            return False

        if not self.edited_output_file.get():
            messagebox.showerror("错误", "请指定二次剪辑输出文件")
            return False

        # 原素材组验证
        if not self.original_subtitle_file.get():
            messagebox.showerror("错误", "请选择原素材字幕文件")
            return False

        if not self.original_timestamp_file.get():
            messagebox.showerror("错误", "请选择原素材时间戳文件")
            return False

        if not self.original_output_file.get():
            messagebox.showerror("错误", "请指定原素材输出文件")
            return False

        # 检查文件是否存在
        files_to_check = [
            (self.edited_subtitle_file.get(), "二次剪辑字幕文件"),
            (self.edited_timestamp_file.get(), "二次剪辑时间戳文件"),
            (self.original_subtitle_file.get(), "原素材字幕文件"),
            (self.original_timestamp_file.get(), "原素材时间戳文件")
        ]

        for file_path, file_desc in files_to_check:
            if not os.path.exists(file_path):
                messagebox.showerror("错误", f"{file_desc}不存在：{file_path}")
                return False

        return True

    def start_merging(self):
        """开始双组字幕合并"""
        if not self.validate_merger_inputs():
            return

        self.logger.info("用户开始双组字幕合并处理")
        self.logger.info(f"二次剪辑字幕文件: {self.edited_subtitle_file.get()}")
        self.logger.info(f"二次剪辑时间戳文件: {self.edited_timestamp_file.get()}")
        self.logger.info(f"二次剪辑输出文件: {self.edited_output_file.get()}")
        self.logger.info(f"原素材字幕文件: {self.original_subtitle_file.get()}")
        self.logger.info(f"原素材时间戳文件: {self.original_timestamp_file.get()}")
        self.logger.info(f"原素材输出文件: {self.original_output_file.get()}")

        # 禁用按钮
        self.merger_button.config(state="disabled")
        self.status_var.set("正在进行双组字幕合并...")

        # 清空结果区域
        self.merger_result_text.delete(1.0, tk.END)

        # 在新线程中执行处理
        thread = threading.Thread(target=self.merging_worker)
        thread.daemon = True
        thread.start()

    def merging_worker(self):
        """双组字幕合并工作线程"""
        try:
            from subtitle_timestamp_merger import SubtitleTimestampMerger

            results = []

            # 二次剪辑组合并
            self.logger.info("开始处理二次剪辑组...")
            merger_edited = SubtitleTimestampMerger()
            success_edited, result_edited = merger_edited.process_files(
                self.edited_subtitle_file.get(),
                self.edited_timestamp_file.get(),
                self.edited_output_file.get()
            )

            if success_edited:
                results.append(f"✅ 二次剪辑组合并成功: {result_edited}")
            else:
                results.append(f"❌ 二次剪辑组合并失败: {result_edited}")

            # 原素材组合并
            self.logger.info("开始处理原素材组...")
            merger_original = SubtitleTimestampMerger()
            success_original, result_original = merger_original.process_files(
                self.original_subtitle_file.get(),
                self.original_timestamp_file.get(),
                self.original_output_file.get()
            )

            if success_original:
                results.append(f"✅ 原素材组合并成功: {result_original}")
            else:
                results.append(f"❌ 原素材组合并失败: {result_original}")

            # 判断整体成功状态
            overall_success = success_edited and success_original
            overall_result = "\n".join(results)

            # 在主线程中更新UI
            self.root.after(0, self.merging_completed, overall_success, overall_result)

        except Exception as e:
            error_msg = f"双组字幕合并过程中发生错误: {e}"
            self.logger.error(error_msg)
            import traceback
            traceback.print_exc()
            self.root.after(0, self.merging_completed, False, error_msg)

    def merging_completed(self, success: bool, result: str):
        """双组字幕合并完成回调"""
        # 恢复按钮
        self.merger_button.config(state="normal")

        if success:
            self.status_var.set("双组字幕合并完成")
            self.merger_result_text.insert(tk.END, "✅ 双组字幕合并成功！\n\n")

            # 显示处理结果
            self.merger_result_text.insert(tk.END, "处理结果：\n")
            self.merger_result_text.insert(tk.END, result)

            # 显示输出文件信息
            self.merger_result_text.insert(tk.END, f"\n\n📁 输出文件：\n")
            self.merger_result_text.insert(tk.END, f"  二次剪辑组: {self.edited_output_file.get()}\n")
            self.merger_result_text.insert(tk.END, f"  原素材组: {self.original_output_file.get()}\n")

            # 显示统计信息
            try:
                edited_count = 0
                original_count = 0

                # 统计二次剪辑组字幕数量
                if os.path.exists(self.edited_output_file.get()):
                    with open(self.edited_output_file.get(), 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        edited_count = sum(1 for line in lines if line.strip() and not line.strip().isdigit() and ' --> ' not in line)

                # 统计原素材组字幕数量
                if os.path.exists(self.original_output_file.get()):
                    with open(self.original_output_file.get(), 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        original_count = sum(1 for line in lines if line.strip() and not line.strip().isdigit() and ' --> ' not in line)

                self.merger_result_text.insert(tk.END, f"\n📊 统计信息：\n")
                self.merger_result_text.insert(tk.END, f"  二次剪辑组: {edited_count} 条字幕\n")
                self.merger_result_text.insert(tk.END, f"  原素材组: {original_count} 条字幕\n")
                self.merger_result_text.insert(tk.END, f"  总计: {edited_count + original_count} 条字幕")

            except Exception as e:
                self.merger_result_text.insert(tk.END, f"\n统计信息获取失败：{e}")

            # 自动保存结果
            self.auto_save_merger_results(result)

            # 自动填充智能字幕匹配的输入文件
            self.auto_fill_matcher_from_merger_output()

            # 提示用户可以进行智能匹配
            self.merger_result_text.insert(tk.END, f"\n\n💡 提示：合并结果已自动填充到'智能字幕匹配'功能中，您可以切换到该选项卡进行下一步处理。")

        else:
            self.status_var.set("双组字幕合并失败")
            self.merger_result_text.insert(tk.END, "❌ 双组字幕合并失败！\n\n")
            self.merger_result_text.insert(tk.END, f"错误信息: {result}")
            messagebox.showerror("失败", f"双组字幕合并失败:\n{result}")



    def auto_save_merger_results(self, result_text):
        """自动保存字幕合并结果"""
        try:
            filename = "GUI合并结果.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(result_text)
            self.logger.info(f"GUI合并结果已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"GUI自动保存合并结果失败: {e}")

    def clear_merger_results(self):
        """清空字幕合并结果"""
        self.merger_result_text.delete(1.0, tk.END)
        self.status_var.set("就绪")
        self.logger.info("用户清空了字幕合并结果显示")

    def create_timestamp_importer_tab(self):
        """创建导入时间戳选项卡"""
        # 创建选项卡框架
        importer_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(importer_frame, text="📥 导入时间戳")

        # 项目选择区域
        project_frame = ttk.LabelFrame(importer_frame, text="剪映项目选择", padding="10")
        project_frame.pack(fill="x", pady=(0, 10))

        # 项目文件夹选择
        ttk.Label(project_frame, text="项目文件夹:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=5
        )
        ttk.Entry(project_frame, textvariable=self.importer_project_folder, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(project_frame, text="浏览",
                  command=self.browse_project_folder).grid(
            row=0, column=2, padx=(0, 5), pady=5
        )

        # 项目选择
        ttk.Label(project_frame, text="选择项目:", font=self.default_font).grid(
            row=1, column=0, sticky="w", pady=5
        )
        self.project_combobox = ttk.Combobox(project_frame, textvariable=self.importer_selected_project,
                                           width=47, state="readonly")
        self.project_combobox.grid(row=1, column=1, sticky="ew", padx=(10, 5), pady=5)
        self.project_combobox.bind('<<ComboboxSelected>>', self.on_project_selected)

        ttk.Button(project_frame, text="刷新",
                  command=self.refresh_project_list).grid(
            row=1, column=2, padx=(0, 5), pady=5
        )

        # 说明文字
        ttk.Label(project_frame, text="说明：导入时间戳和字幕都使用 draft_content.json",
                 font=self.default_font, foreground="gray").grid(
            row=2, column=0, columnspan=3, sticky="w", pady=5
        )

        project_frame.columnconfigure(1, weight=1)

        # 时间戳文件选择区域
        file_frame = ttk.LabelFrame(importer_frame, text="时间戳文件选择", padding="10")
        file_frame.pack(fill="x", pady=(0, 10))

        # 说明文字
        instruction_text = """支持的时间戳格式：
1. SRT格式：00:30:38,400 --> 00:30:43,566
2. 标准格式：001 33:32.130-33:33.530
3. 简化格式：1. 33:33.530-33:42.670"""

        ttk.Label(file_frame, text=instruction_text, font=self.default_font,
                 justify="left").pack(anchor="w", pady=(0, 10))

        # 时间戳文件选择
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill="x", pady=(10, 0))

        ttk.Label(file_select_frame, text="时间戳文件:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=5
        )
        ttk.Entry(file_select_frame, textvariable=self.importer_timestamp_file, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(file_select_frame, text="浏览",
                  command=self.browse_timestamp_file).grid(
            row=0, column=2, padx=(0, 5), pady=5
        )

        file_select_frame.columnconfigure(1, weight=1)

        # 字幕文件选择
        subtitle_select_frame = ttk.Frame(file_frame)
        subtitle_select_frame.pack(fill="x", pady=(10, 0))

        ttk.Label(subtitle_select_frame, text="字幕文件:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=5
        )
        self.importer_subtitle_file = tk.StringVar()
        ttk.Entry(subtitle_select_frame, textvariable=self.importer_subtitle_file, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(subtitle_select_frame, text="浏览",
                  command=self.browse_importer_subtitle_file).grid(
            row=0, column=2, padx=(0, 5), pady=5
        )

        subtitle_select_frame.columnconfigure(1, weight=1)

        # 操作按钮区域
        button_frame = ttk.Frame(importer_frame)
        button_frame.pack(fill="x", pady=(0, 10))

        # 格式转换按钮
        ttk.Button(button_frame, text="🔄 格式转换",
                  command=self.convert_timestamp_format,
                  style="Accent.TButton").pack(side="left")

        # 合并时间戳按钮
        ttk.Button(button_frame, text="🔗 合并时间戳",
                  command=self.merge_timestamps,
                  style="Accent.TButton").pack(side="left", padx=(10, 0))

        # 时间戳导入按钮
        ttk.Button(button_frame, text="🚀 导入时间戳到剪映",
                  command=self.import_timestamps_to_jianying,
                  style="Accent.TButton").pack(side="left", padx=(10, 0))

        # 字幕导入按钮
        ttk.Button(button_frame, text="📝 导入字幕到剪映",
                  command=self.import_subtitles_to_jianying,
                  style="Accent.TButton").pack(side="left", padx=(10, 0))

        # 预览按钮
        ttk.Button(button_frame, text="预览解析结果",
                  command=self.preview_timestamps).pack(side="left", padx=(10, 0))

        # 结果显示区域
        result_frame = ttk.LabelFrame(importer_frame, text="处理结果", padding="10")
        result_frame.pack(fill="both", expand=True)

        result_text_frame = ttk.Frame(result_frame)
        result_text_frame.pack(fill="both", expand=True)

        self.importer_result_text = tk.Text(result_text_frame, height=6, font=("Consolas", 9))
        result_scrollbar = ttk.Scrollbar(result_text_frame, orient="vertical",
                                       command=self.importer_result_text.yview)

        self.importer_result_text.configure(yscrollcommand=result_scrollbar.set)

        self.importer_result_text.pack(side="left", fill="both", expand=True)
        result_scrollbar.pack(side="right", fill="y")

    def browse_project_folder(self):
        """浏览项目文件夹"""
        # 从记忆中获取初始目录
        initial_dir = self.folder_memory.get('importer_project_folder', os.getcwd())

        folder = filedialog.askdirectory(
            title="选择剪映项目文件夹",
            initialdir=initial_dir
        )
        if folder:
            self.importer_project_folder.set(folder)
            # 保存到记忆中
            self.folder_memory['importer_project_folder'] = folder
            self.save_folder_memory()
            self.refresh_project_list()

    def refresh_project_list(self):
        """刷新项目列表"""
        project_folder = self.importer_project_folder.get().strip()
        if not project_folder or not os.path.exists(project_folder):
            self.project_list = []
            self.project_combobox['values'] = []
            return

        try:
            # 扫描项目文件夹
            projects = []
            for item in os.listdir(project_folder):
                item_path = os.path.join(project_folder, item)
                if os.path.isdir(item_path):
                    projects.append(item)

            self.project_list = sorted(projects)
            self.project_combobox['values'] = self.project_list

            if self.project_list:
                self.project_combobox.set(self.project_list[0])
                self.on_project_selected(None)

            self.importer_result_text.delete(1.0, tk.END)
            self.importer_result_text.insert(tk.END, f"✅ 找到 {len(self.project_list)} 个项目\n")

        except Exception as e:
            messagebox.showerror("错误", f"扫描项目文件夹失败：{e}")

    def on_project_selected(self, event):
        """项目选择事件"""
        selected_project = self.importer_selected_project.get()
        if not selected_project:
            return

        project_folder = self.importer_project_folder.get()
        project_path = os.path.join(project_folder, selected_project)

        try:
            # 扫描草稿文件
            drafts = []
            for item in os.listdir(project_path):
                if item.endswith('.json') and 'draft' in item.lower():
                    drafts.append(item)

            self.draft_list = sorted(drafts)
            # 不再需要设置草稿下拉菜单，使用默认的 draft_content.json

            # 恢复该项目的文件记忆
            project_key = os.path.join(project_folder, selected_project)
            if project_key in self.project_file_memory:
                remembered_file = self.project_file_memory[project_key]
                if os.path.exists(remembered_file):
                    self.importer_timestamp_file.set(remembered_file)
                    self.importer_result_text.delete(1.0, tk.END)
                    self.importer_result_text.insert(tk.END, f"✅ 项目: {selected_project}\n")
                    self.importer_result_text.insert(tk.END, f"📁 找到 {len(self.draft_list)} 个草稿文件\n")
                    self.importer_result_text.insert(tk.END, f"💾 已恢复记忆的时间戳文件: {os.path.basename(remembered_file)}\n")
                else:
                    # 文件不存在，清除记忆
                    del self.project_file_memory[project_key]
                    self.importer_timestamp_file.set("")
                    self.importer_result_text.delete(1.0, tk.END)
                    self.importer_result_text.insert(tk.END, f"✅ 项目: {selected_project}\n")
                    self.importer_result_text.insert(tk.END, f"📁 找到 {len(self.draft_list)} 个草稿文件\n")
                    self.importer_result_text.insert(tk.END, f"⚠️ 记忆的文件已不存在，已清除记忆\n")
            else:
                self.importer_timestamp_file.set("")
                self.importer_result_text.delete(1.0, tk.END)
                self.importer_result_text.insert(tk.END, f"✅ 项目: {selected_project}\n")
                self.importer_result_text.insert(tk.END, f"📁 找到 {len(self.draft_list)} 个草稿文件\n")

        except Exception as e:
            messagebox.showerror("错误", f"扫描草稿文件失败：{e}")

    def browse_timestamp_file(self):
        """浏览时间戳文件"""
        # 从记忆中获取初始目录
        initial_dir = self.folder_memory.get('importer_timestamp_file', os.getcwd())

        filename = filedialog.askopenfilename(
            title="选择时间戳文件",
            initialdir=initial_dir,
            filetypes=[
                ("文本文件", "*.txt"),
                ("SRT文件", "*.srt"),
                ("所有文件", "*.*")
            ]
        )

        if filename:
            self.importer_timestamp_file.set(filename)

            # 保存文件夹路径到记忆中
            file_dir = os.path.dirname(filename)
            self.folder_memory['importer_timestamp_file'] = file_dir
            self.save_folder_memory()

            # 保存到项目记忆中
            project_folder = self.importer_project_folder.get()
            selected_project = self.importer_selected_project.get()
            if project_folder and selected_project:
                project_key = os.path.join(project_folder, selected_project)
                self.project_file_memory[project_key] = filename
                self.save_project_memory()

    def refresh_subtitle_files(self):
        """刷新当前目录下的字幕文件列表"""
        try:
            current_dir = os.getcwd()
            subtitle_files = []

            # 查找SRT和TXT文件
            for file in os.listdir(current_dir):
                if file.lower().endswith(('.srt', '.txt')):
                    subtitle_files.append(file)

            # 按文件名排序
            subtitle_files.sort()

            # 更新下拉菜单
            if hasattr(self, 'subtitle_file_combobox'):
                self.subtitle_file_combobox['values'] = subtitle_files
            if hasattr(self, 'importer_subtitle_combobox'):
                self.importer_subtitle_combobox['values'] = subtitle_files

            if subtitle_files:
                self.logger.info(f"找到 {len(subtitle_files)} 个字幕文件")
            else:
                self.logger.info("当前目录下没有找到字幕文件")

        except Exception as e:
            self.logger.error(f"刷新字幕文件列表失败: {e}")
            messagebox.showerror("错误", f"刷新字幕文件列表失败：{e}")

    def on_subtitle_file_selected(self, event=None):
        """当选择字幕文件时的回调"""
        selected_file = None
        if hasattr(self, 'subtitle_file_combobox'):
            selected_file = self.subtitle_file_combobox.get()
        elif hasattr(self, 'importer_subtitle_combobox'):
            selected_file = self.importer_subtitle_combobox.get()

        if selected_file:
            # 构建完整路径
            full_path = os.path.join(os.getcwd(), selected_file)
            self.importer_timestamp_file.set(full_path)

            # 保存文件夹路径到记忆中
            file_dir = os.path.dirname(full_path)
            self.folder_memory['importer_timestamp_file'] = file_dir
            self.save_folder_memory()

            # 保存到项目记忆中
            project_folder = self.importer_project_folder.get()
            selected_project = self.importer_selected_project.get()
            if project_folder and selected_project:
                project_key = os.path.join(project_folder, selected_project)
                self.project_file_memory[project_key] = full_path
                self.save_project_memory()

            self.logger.info(f"选择字幕文件: {selected_file}")

            # 在结果区域显示选择信息
            if hasattr(self, 'importer_result_text'):
                self.importer_result_text.insert(tk.END, f"✅ 已选择字幕文件: {selected_file}\n")

    def extract_subtitle_entries_from_srt(self, srt_content: str, timestamps: List) -> List[Dict]:
        """从SRT内容中提取字幕条目"""
        import re

        subtitle_entries = []

        try:
            # 按空行分割SRT块
            blocks = re.split(r'\n\s*\n', srt_content.strip())

            subtitle_texts = []
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    # 第三行及以后是字幕文本
                    text = '\n'.join(lines[2:]).strip()
                    if text:
                        subtitle_texts.append(text)

            # 将字幕文本与时间戳匹配
            for i, entry in enumerate(timestamps):
                if i < len(subtitle_texts):
                    text = subtitle_texts[i]
                else:
                    text = f"字幕{entry.sequence_number}"

                subtitle_entries.append({
                    "text": text,
                    "start_time": entry.start_time,
                    "duration": entry.duration
                })

            self.logger.info(f"从SRT文件提取了 {len(subtitle_entries)} 个字幕条目")

        except Exception as e:
            self.logger.error(f"提取SRT字幕文本失败: {e}")
            # 如果提取失败，使用默认文本
            for i, entry in enumerate(timestamps):
                subtitle_entries.append({
                    "text": f"字幕{entry.sequence_number}",
                    "start_time": entry.start_time,
                    "duration": entry.duration
                })

        return subtitle_entries

    def on_import_type_selected(self):
        """导入类型选择回调"""
        # 这里可以根据选择的类型更新界面提示
        pass

    def browse_importer_subtitle_file(self):
        """浏览字幕文件"""
        # 从记忆中获取初始目录
        initial_dir = self.folder_memory.get('importer_subtitle_file', os.getcwd())

        filename = filedialog.askopenfilename(
            title="选择字幕文件",
            filetypes=[
                ("字幕文件", "*.srt *.txt"),
                ("SRT文件", "*.srt"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ],
            initialdir=initial_dir
        )
        if filename:
            self.importer_subtitle_file.set(filename)

            # 保存文件夹路径到记忆中
            file_dir = os.path.dirname(filename)
            self.folder_memory['importer_subtitle_file'] = file_dir
            self.save_folder_memory()

            # 保存字幕文件路径到记忆中
            self.folder_memory['last_subtitle_file'] = filename
            self.save_folder_memory()

            self.logger.info(f"选择字幕文件: {os.path.basename(filename)}")



    def import_subtitles_to_jianying(self):
        """导入字幕到剪映"""
        # 验证输入
        project_folder = self.importer_project_folder.get()
        project_name = self.importer_selected_project.get()
        subtitle_file = self.importer_subtitle_file.get()

        # 使用默认的草稿文件名
        draft_name = "draft_content.json"

        if not all([project_folder, project_name]):
            messagebox.showwarning("警告", "请先选择项目文件夹和项目")
            return

        if not subtitle_file:
            messagebox.showwarning("警告", "请选择字幕文件")
            return

        if not os.path.exists(subtitle_file):
            messagebox.showerror("错误", f"字幕文件不存在：{subtitle_file}")
            return

        # 构建项目路径
        project_path = os.path.join(project_folder, project_name)
        draft_path = os.path.join(project_path, draft_name)

        if not os.path.exists(draft_path):
            messagebox.showerror("错误", f"草稿文件不存在：{draft_path}")
            return

        try:
            self.importer_result_text.delete(1.0, tk.END)
            self.importer_result_text.insert(tk.END, f"🚀 开始导入字幕到剪映...\n")
            self.importer_result_text.insert(tk.END, f"📁 项目: {project_name}\n")
            self.importer_result_text.insert(tk.END, f"📄 草稿: {draft_name}\n")
            self.importer_result_text.insert(tk.END, f"📝 字幕文件: {os.path.basename(subtitle_file)}\n\n")

            # 读取字幕文件
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析字幕
            from timestamp_importer import TimestampImporter
            importer = TimestampImporter()
            entries = importer.parse_timestamps(content)

            if not entries:
                self.importer_result_text.insert(tk.END, "❌ 未能解析到有效的字幕条目\n")
                messagebox.showerror("错误", "字幕文件解析失败")
                return

            self.importer_result_text.insert(tk.END, f"✅ 解析成功！共 {len(entries)} 个字幕条目\n\n")

            # 提取字幕文本
            subtitle_entries = self.extract_subtitle_entries_from_srt(content, entries)

            # 创建字幕轨道
            from subtitle_track_generator import SubtitleTrackGenerator
            generator = SubtitleTrackGenerator()

            self.importer_result_text.insert(tk.END, f"📝 开始创建字幕轨道...\n")
            success = generator.add_subtitle_to_project(draft_path, subtitle_entries)

            if success:
                self.importer_result_text.insert(tk.END, f"✅ 字幕轨道创建成功！\n")
                self.importer_result_text.insert(tk.END, f"📝 已添加 {len(subtitle_entries)} 个字幕到字幕轨道\n")
                self.importer_result_text.insert(tk.END, f"🎬 请在剪映中重新打开项目查看字幕轨道\n")
                messagebox.showinfo("导入成功", f"字幕导入成功！\n\n共导入 {len(subtitle_entries)} 个字幕到剪映草稿\n\n项目：{project_name}\n草稿：{draft_name}\n\n请在剪映中重新打开项目查看字幕轨道")
            else:
                self.importer_result_text.insert(tk.END, f"❌ 字幕轨道创建失败\n")
                messagebox.showerror("错误", "字幕轨道创建失败")

        except Exception as e:
            self.importer_result_text.insert(tk.END, f"❌ 导入失败：{e}\n")
            self.logger.error(f"导入字幕失败: {e}")
            messagebox.showerror("错误", f"导入字幕失败：{e}")


    def create_timestamp_extractor_tab(self):
        """创建提取时间戳选项卡"""
        # 创建选项卡框架
        extractor_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(extractor_frame, text="📤 提取时间戳")

        # 项目选择区域
        project_frame = ttk.LabelFrame(extractor_frame, text="剪映项目选择", padding="10")
        project_frame.pack(fill="x", pady=(0, 10))

        # 项目文件夹选择
        ttk.Label(project_frame, text="项目文件夹:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=5
        )
        ttk.Entry(project_frame, textvariable=self.extractor_project_folder, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(project_frame, text="浏览",
                  command=self.browse_extractor_project_folder).grid(
            row=0, column=2, padx=(0, 5), pady=5
        )

        # 项目选择
        ttk.Label(project_frame, text="选择项目:", font=self.default_font).grid(
            row=1, column=0, sticky="w", pady=5
        )
        self.extractor_project_combobox = ttk.Combobox(project_frame, textvariable=self.extractor_selected_project,
                                                     width=47, state="readonly")
        self.extractor_project_combobox.grid(row=1, column=1, sticky="ew", padx=(10, 5), pady=5)
        self.extractor_project_combobox.bind('<<ComboboxSelected>>', self.on_extractor_project_selected)

        # 草稿选择
        ttk.Label(project_frame, text="选择草稿:", font=self.default_font).grid(
            row=2, column=0, sticky="w", pady=5
        )
        self.extractor_draft_combobox = ttk.Combobox(project_frame, textvariable=self.extractor_selected_draft,
                                                   width=47, state="readonly")
        self.extractor_draft_combobox.grid(row=2, column=1, sticky="ew", padx=(10, 5), pady=5)

        project_frame.columnconfigure(1, weight=1)

        # 提取选项区域
        options_frame = ttk.LabelFrame(extractor_frame, text="提取选项", padding="10")
        options_frame.pack(fill="x", pady=(0, 10))

        # 说明文字
        instruction_text = """功能说明：
1. 从特征缓存中提取片段时间戳信息
2. 从剪映项目中提取轨道片段信息
3. 生成详细的时间戳统计报告
4. 支持主轨道和第二轨道分析
5. 自动生成时间戳文件到 timestamp_reports 文件夹"""

        ttk.Label(options_frame, text=instruction_text, font=self.default_font,
                 justify="left").pack(anchor="w", pady=(0, 10))

        # 操作按钮区域
        button_frame = ttk.Frame(extractor_frame)
        button_frame.pack(fill="x", pady=(0, 10))

        ttk.Button(button_frame, text="🚀 提取时间戳",
                  command=self.extract_timestamps,
                  style="Accent.TButton").pack(side="left")
        ttk.Button(button_frame, text="📂 打开报告文件夹",
                  command=self.open_reports_folder).pack(side="left", padx=(10, 0))

        # 结果显示区域
        result_frame = ttk.LabelFrame(extractor_frame, text="提取结果", padding="10")
        result_frame.pack(fill="both", expand=True)

        result_text_frame = ttk.Frame(result_frame)
        result_text_frame.pack(fill="both", expand=True)

        self.extractor_result_text = tk.Text(result_text_frame, height=12, font=("Consolas", 9))
        extractor_scrollbar = ttk.Scrollbar(result_text_frame, orient="vertical",
                                          command=self.extractor_result_text.yview)
        self.extractor_result_text.config(yscrollcommand=extractor_scrollbar.set)

        self.extractor_result_text.pack(side="left", fill="both", expand=True)
        extractor_scrollbar.pack(side="right", fill="y")

    def browse_extractor_project_folder(self):
        """浏览提取器项目文件夹"""
        folder = filedialog.askdirectory(title="选择剪映项目文件夹")
        if folder:
            self.extractor_project_folder.set(folder)
            self.refresh_extractor_projects()




            self.logger.info(f"选择字幕文件: {selected_file}")

    def preview_subtitle_content(self):
        """预览字幕内容"""
        subtitle_file = self.subtitle_file_path.get()

        if not subtitle_file:
            messagebox.showwarning("警告", "请先选择字幕文件")
            return

        if not os.path.exists(subtitle_file):
            messagebox.showerror("错误", f"字幕文件不存在：{subtitle_file}")
            return

        try:
            self.subtitle_result_text.delete(1.0, tk.END)
            self.subtitle_result_text.insert(tk.END, f"📁 字幕文件: {os.path.basename(subtitle_file)}\n\n")

            # 读取并解析字幕文件
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()

            from timestamp_importer import TimestampImporter
            importer = TimestampImporter()
            entries = importer.parse_timestamps(content)

            if entries:
                self.subtitle_result_text.insert(tk.END, f"✅ 解析成功！共 {len(entries)} 个字幕条目\n\n")

                # 显示前10个条目
                for i, entry in enumerate(entries[:10]):
                    self.subtitle_result_text.insert(tk.END, f"{entry}\n")

                if len(entries) > 10:
                    self.subtitle_result_text.insert(tk.END, f"\n... 还有 {len(entries) - 10} 个条目\n")

                # 提取字幕文本
                subtitle_entries = self.extract_subtitle_entries_from_srt(content, entries)

                self.subtitle_result_text.insert(tk.END, f"\n📝 字幕文本预览:\n")
                for i, entry in enumerate(subtitle_entries[:5]):
                    text = entry['text'][:50] + "..." if len(entry['text']) > 50 else entry['text']
                    self.subtitle_result_text.insert(tk.END, f"  {i+1}. \"{text}\"\n")

            else:
                self.subtitle_result_text.insert(tk.END, "❌ 未能解析到有效的字幕条目\n")

        except Exception as e:
            self.subtitle_result_text.insert(tk.END, f"❌ 预览失败：{e}\n")
            self.logger.error(f"预览字幕失败: {e}")

    def import_subtitle_to_jianying(self):
        """导入字幕到剪映"""
        # 验证输入
        project_folder = self.subtitle_project_folder.get()
        project_name = self.subtitle_selected_project.get()
        draft_name = self.subtitle_selected_draft.get()
        subtitle_file = self.subtitle_file_path.get()

        if not all([project_folder, project_name, draft_name, subtitle_file]):
            messagebox.showwarning("警告", "请填写所有必要信息")
            return

        if not os.path.exists(subtitle_file):
            messagebox.showerror("错误", f"字幕文件不存在：{subtitle_file}")
            return

        # 构建项目路径
        project_path = os.path.join(project_folder, project_name)
        draft_path = os.path.join(project_path, draft_name)

        if not os.path.exists(draft_path):
            messagebox.showerror("错误", f"草稿文件不存在：{draft_path}")
            return

        try:
            self.subtitle_result_text.delete(1.0, tk.END)
            self.subtitle_result_text.insert(tk.END, f"🚀 开始导入字幕到剪映...\n")
            self.subtitle_result_text.insert(tk.END, f"📁 项目: {project_name}\n")
            self.subtitle_result_text.insert(tk.END, f"📄 草稿: {draft_name}\n")
            self.subtitle_result_text.insert(tk.END, f"📝 字幕文件: {os.path.basename(subtitle_file)}\n\n")

            # 读取字幕文件
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析字幕
            from timestamp_importer import TimestampImporter
            importer = TimestampImporter()
            entries = importer.parse_timestamps(content)

            if not entries:
                self.subtitle_result_text.insert(tk.END, "❌ 未能解析到有效的字幕条目\n")
                messagebox.showerror("错误", "字幕文件解析失败")
                return

            self.subtitle_result_text.insert(tk.END, f"✅ 解析成功！共 {len(entries)} 个字幕条目\n\n")

            # 提取字幕文本
            subtitle_entries = self.extract_subtitle_entries_from_srt(content, entries)

            # 创建字幕轨道
            from subtitle_track_generator import SubtitleTrackGenerator
            generator = SubtitleTrackGenerator()

            self.subtitle_result_text.insert(tk.END, f"📝 开始创建字幕轨道...\n")
            success = generator.add_subtitle_to_project(draft_path, subtitle_entries)

            if success:
                self.subtitle_result_text.insert(tk.END, f"✅ 字幕轨道创建成功！\n")
                self.subtitle_result_text.insert(tk.END, f"📝 已添加 {len(subtitle_entries)} 个字幕到字幕轨道\n")
                self.subtitle_result_text.insert(tk.END, f"🎬 请在剪映中重新打开项目查看字幕轨道\n")
                messagebox.showinfo("导入成功", f"字幕导入成功！\n\n共导入 {len(subtitle_entries)} 个字幕到剪映草稿\n\n项目：{project_name}\n草稿：{draft_name}\n\n请在剪映中重新打开项目查看字幕轨道")
            else:
                self.subtitle_result_text.insert(tk.END, f"❌ 字幕轨道创建失败\n")
                messagebox.showerror("错误", "字幕轨道创建失败")

        except Exception as e:
            self.subtitle_result_text.insert(tk.END, f"❌ 导入失败：{e}\n")
            self.logger.error(f"导入字幕失败: {e}")
            messagebox.showerror("错误", f"导入字幕失败：{e}")

    def save_project_memory(self):
        """保存项目文件记忆到本地"""
        try:
            import json
            memory_file = "project_file_memory.json"
            with open(memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.project_file_memory, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存项目记忆失败: {e}")

    def load_project_memory(self):
        """从本地加载项目文件记忆"""
        try:
            import json
            memory_file = "project_file_memory.json"
            if os.path.exists(memory_file):
                with open(memory_file, 'r', encoding='utf-8') as f:
                    self.project_file_memory = json.load(f)
        except Exception as e:
            self.logger.error(f"加载项目记忆失败: {e}")
            self.project_file_memory = {}

    def save_folder_memory(self):
        """保存文件夹记忆到本地"""
        try:
            import json
            memory_file = "folder_memory.json"
            with open(memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.folder_memory, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存文件夹记忆失败: {e}")

    def load_folder_memory(self):
        """从本地加载文件夹记忆"""
        try:
            import json
            memory_file = "folder_memory.json"
            if os.path.exists(memory_file):
                with open(memory_file, 'r', encoding='utf-8') as f:
                    self.folder_memory = json.load(f)
        except Exception as e:
            self.logger.error(f"加载文件夹记忆失败: {e}")
            self.folder_memory = {}

    def save_parameter_memory(self):
        """保存参数设置到本地"""
        try:
            import json
            # 收集当前参数设置
            self.parameter_memory = {
                'matching_algorithm': self.matching_algorithm.get(),
                'similarity_threshold': self.similarity_threshold.get(),
                'context_weight': self.context_weight.get(),
                'max_time_gap': self.max_time_gap.get(),
                'max_window': self.max_window.get(),
                # 场景检测参数
                'scene_threshold': self.scene_threshold.get(),
                'min_scene_length': self.min_scene_length.get(),
                'max_scene_length': self.max_scene_length.get(),
                'long_scene_sensitivity': self.long_scene_sensitivity.get(),
                'segment_length': self.segment_length.get()
            }

            memory_file = "parameter_memory.json"
            with open(memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.parameter_memory, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存参数记忆失败: {e}")

    def load_parameter_memory(self):
        """从本地加载参数记忆"""
        try:
            import json
            memory_file = "parameter_memory.json"
            if os.path.exists(memory_file):
                with open(memory_file, 'r', encoding='utf-8') as f:
                    self.parameter_memory = json.load(f)

                # 恢复参数设置
                if 'matching_algorithm' in self.parameter_memory:
                    self.matching_algorithm.set(self.parameter_memory['matching_algorithm'])

                if 'similarity_threshold' in self.parameter_memory:
                    self.similarity_threshold.set(self.parameter_memory['similarity_threshold'])
                    # 更新标签显示
                    if hasattr(self, 'similarity_label'):
                        self.similarity_label.config(text=f"{self.parameter_memory['similarity_threshold']:.2f}")

                if 'context_weight' in self.parameter_memory:
                    self.context_weight.set(self.parameter_memory['context_weight'])
                    # 更新标签显示
                    if hasattr(self, 'context_label'):
                        self.context_label.config(text=f"{self.parameter_memory['context_weight']:.2f}")

                if 'max_time_gap' in self.parameter_memory:
                    self.max_time_gap.set(self.parameter_memory['max_time_gap'])

                if 'max_window' in self.parameter_memory:
                    self.max_window.set(self.parameter_memory['max_window'])

                # 场景检测参数将在选项卡创建后单独加载
                # 这里不加载场景检测参数，避免控件未创建的问题

                self.logger.info(f"已恢复上次的参数设置: 算法={self.matching_algorithm.get()}, 相似度={self.similarity_threshold.get():.2f}, 上下文权重={self.context_weight.get():.2f}, 场景敏感度={self.scene_threshold.get():.1f}")

        except Exception as e:
            self.logger.error(f"加载参数记忆失败: {e}")
            self.parameter_memory = {}

    def restore_folder_selections(self):
        """恢复上次的文件夹选择"""
        try:
            # 恢复导入时间戳的项目文件夹
            if 'importer_project_folder' in self.folder_memory:
                folder = self.folder_memory['importer_project_folder']
                if os.path.exists(folder):
                    self.importer_project_folder.set(folder)
                    self.refresh_project_list()

            # 恢复提取时间戳的项目文件夹
            if 'extractor_project_folder' in self.folder_memory:
                folder = self.folder_memory['extractor_project_folder']
                if os.path.exists(folder):
                    self.extractor_project_folder.set(folder)
                    self.refresh_extractor_project_list()

            # 恢复其他文件路径
            file_vars = {
                'matcher_file_a': self.matcher_file_a,
                'matcher_file_b': self.matcher_file_b,
                'merger_subtitle_file': self.merger_subtitle_file,
                'merger_timestamp_file': self.merger_timestamp_file,
                'merger_output_file': self.merger_output_file,
                'importer_timestamp_file': self.importer_timestamp_file
            }

            for memory_key, var in file_vars.items():
                if memory_key in self.folder_memory:
                    folder = self.folder_memory[memory_key]
                    if os.path.exists(folder):
                        # 对于文件路径，我们只恢复文件夹，不恢复具体文件
                        # 具体文件由项目记忆处理
                        pass

            self.logger.info("✅ 文件夹记忆恢复完成")

        except Exception as e:
            self.logger.error(f"恢复文件夹选择失败: {e}")

    def restore_subtitle_file_selection(self):
        """恢复字幕文件选择"""
        try:
            # 优先从记忆中恢复上次选择的字幕文件
            if 'last_subtitle_file' in self.folder_memory:
                subtitle_file = self.folder_memory['last_subtitle_file']
                if os.path.exists(subtitle_file):
                    self.importer_subtitle_file.set(subtitle_file)
                    self.logger.info(f"恢复字幕文件: {os.path.basename(subtitle_file)}")
                    return

            # 如果记忆中没有或文件不存在，检查默认文件是否存在
            default_subtitle_path = os.path.join(os.getcwd(), "叙事性字幕.srt")
            if os.path.exists(default_subtitle_path):
                self.importer_subtitle_file.set(default_subtitle_path)
                self.logger.info(f"使用默认字幕文件: 叙事性字幕.srt")
            else:
                self.logger.info("默认字幕文件不存在，请手动选择")

        except Exception as e:
            self.logger.error(f"恢复字幕文件选择失败: {e}")

    def import_timestamps_to_jianying(self):
        """导入时间戳到剪映草稿"""
        # 检查项目选择
        if not self.importer_selected_project.get():
            messagebox.showwarning("警告", "请先选择剪映项目！")
            return

        timestamp_file = self.importer_timestamp_file.get().strip()
        if not timestamp_file:
            messagebox.showwarning("警告", "请先选择时间戳文件！")
            return

        if not os.path.exists(timestamp_file):
            messagebox.showerror("错误", "时间戳文件不存在！")
            return

        try:
            with open(timestamp_file, 'r', encoding='utf-8') as f:
                timestamp_text = f.read().strip()
        except Exception as e:
            messagebox.showerror("错误", f"读取时间戳文件失败：{e}")
            return

        if not timestamp_text:
            messagebox.showwarning("警告", "时间戳文件内容为空！")
            return

        try:
            from timestamp_importer import TimestampImporter

            # 解析时间戳
            importer = TimestampImporter()
            timestamps = importer.parse_timestamps(timestamp_text)

            if not timestamps:
                messagebox.showerror("错误", "未解析到有效的时间戳数据！")
                return

            # 验证时间戳
            importer.validate_timestamps(timestamps)

            # 构建草稿文件路径
            project_folder = self.importer_project_folder.get()
            project_name = self.importer_selected_project.get()
            draft_name = "draft_content.json"  # 使用默认的草稿文件名
            project_path = os.path.join(project_folder, project_name)  # 项目路径
            draft_path = os.path.join(project_path, draft_name)  # 草稿文件路径

            if not os.path.exists(draft_path):
                messagebox.showerror("错误", f"草稿文件不存在：{draft_path}")
                return

            # 这里应该调用剪映草稿写入功能
            # 由于我们没有完整的剪映写入模块，先显示结果
            self.importer_result_text.delete(1.0, tk.END)

            stats = importer.display_statistics(timestamps)
            self.importer_result_text.insert(tk.END, stats + "\n\n")

            self.importer_result_text.insert(tk.END, f"🎯 准备写入剪映草稿：\n")
            self.importer_result_text.insert(tk.END, f"📁 项目：{project_name}\n")
            self.importer_result_text.insert(tk.END, f"📄 草稿：{draft_name}\n")
            self.importer_result_text.insert(tk.END, f"📊 时间戳数量：{len(timestamps)}\n\n")

            # 显示前5个时间戳
            self.importer_result_text.insert(tk.END, "📋 时间戳预览（前5个）：\n")
            for i, entry in enumerate(timestamps[:5], 1):
                self.importer_result_text.insert(tk.END, f"{i}. {entry}\n")

            if len(timestamps) > 5:
                self.importer_result_text.insert(tk.END, f"... 还有 {len(timestamps) - 5} 个时间戳\n")

            # 视频轨道导入
            self.importer_result_text.insert(tk.END, "\n🎬 创建视频轨道...\n")

            try:
                from video_track_writer import VideoTrackWriter, MatchResult

                # 转换为匹配结果 - 按照原项目格式（修复版）
                matches = []
                current_timeline_position = 0.0

                for entry in timestamps:
                    duration = entry.end_time - entry.start_time

                    match = MatchResult(
                        main_segment_name=f"segment_{entry.sequence_number:03d}",
                        main_start_time=current_timeline_position,
                        main_end_time=current_timeline_position + duration,
                        second_segment_name="imported_segment",
                        second_start_time=entry.start_time,   # 从第二轨道的这个时间开始切取
                        second_end_time=entry.end_time,       # 到第二轨道的这个时间结束
                        confidence=1.0,
                        method="手动导入"
                    )
                    matches.append(match)

                    current_timeline_position += duration  # 累积时间轴位置

                # 写入剪映草稿 - 使用视频轨道写入器
                success = VideoTrackWriter.write_matches_to_jianying_project(project_path, matches)

                if success:
                    self.importer_result_text.insert(tk.END, "✅ 成功写入剪映草稿！\n")
                    self.importer_result_text.insert(tk.END, f"📁 已添加 {len(matches)} 个视频片段到第三轨道\n")
                    self.importer_result_text.insert(tk.END, "🎬 请在剪映中重新打开项目查看第三轨道\n")
                    messagebox.showinfo("导入成功", f"时间戳导入成功！\n\n共导入 {len(timestamps)} 个时间戳到剪映草稿\n\n项目：{project_name}\n草稿：{draft_name}\n\n请在剪映中重新打开项目查看第三轨道")
                else:
                    self.importer_result_text.insert(tk.END, "❌ 写入剪映草稿失败\n")
                    messagebox.showerror("错误", "写入剪映草稿失败，请检查草稿文件格式")

            except ImportError:
                self.importer_result_text.insert(tk.END, "\n⚠️ 剪映写入模块未找到，仅显示预览\n")
                messagebox.showinfo("解析成功", f"时间戳解析成功！\n\n共解析 {len(timestamps)} 个时间戳\n\n注意：剪映写入模块未找到")
            except Exception as write_error:
                self.importer_result_text.insert(tk.END, f"\n❌ 写入失败：{write_error}\n")
                messagebox.showerror("写入错误", f"写入剪映草稿失败：{write_error}")

        except Exception as e:
            self.importer_result_text.delete(1.0, tk.END)
            self.importer_result_text.insert(tk.END, f"❌ 导入失败：{e}\n")
            messagebox.showerror("错误", f"导入失败：{e}")

    def preview_timestamps(self):
        """预览时间戳解析结果"""
        timestamp_file = self.importer_timestamp_file.get().strip()

        if not timestamp_file:
            messagebox.showwarning("警告", "请先选择时间戳文件！")
            return

        if not os.path.exists(timestamp_file):
            messagebox.showerror("错误", "时间戳文件不存在！")
            return

        try:
            with open(timestamp_file, 'r', encoding='utf-8') as f:
                timestamp_text = f.read().strip()
        except Exception as e:
            messagebox.showerror("错误", f"读取时间戳文件失败：{e}")
            return

        if not timestamp_text:
            messagebox.showwarning("警告", "时间戳文件内容为空！")
            return

        try:
            from timestamp_importer import TimestampImporter

            importer = TimestampImporter()
            timestamps = importer.parse_timestamps(timestamp_text)

            self.importer_result_text.delete(1.0, tk.END)

            if not timestamps:
                self.importer_result_text.insert(tk.END, "❌ 未解析到有效的时间戳数据\n")
                return

            # 验证时间戳
            importer.validate_timestamps(timestamps)

            # 显示统计信息
            stats = importer.display_statistics(timestamps)
            self.importer_result_text.insert(tk.END, stats + "\n\n")

            # 显示前10个解析结果
            self.importer_result_text.insert(tk.END, "📋 解析结果预览（前10个）：\n")
            self.importer_result_text.insert(tk.END, "-" * 40 + "\n")

            for i, entry in enumerate(timestamps[:10], 1):
                self.importer_result_text.insert(tk.END, f"{i:2d}. {entry}\n")

            if len(timestamps) > 10:
                self.importer_result_text.insert(tk.END, f"... 还有 {len(timestamps) - 10} 个时间戳\n")

        except Exception as e:
            self.importer_result_text.delete(1.0, tk.END)
            self.importer_result_text.insert(tk.END, f"❌ 解析失败：{e}\n")
            messagebox.showerror("错误", f"解析时间戳失败：{e}")



    def create_timestamp_extractor_tab(self):
        """创建提取时间戳选项卡"""
        # 创建选项卡框架
        extractor_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(extractor_frame, text="📤 提取时间戳")

        # 项目选择区域
        project_frame = ttk.LabelFrame(extractor_frame, text="剪映项目选择", padding="10")
        project_frame.pack(fill="x", pady=(0, 10))

        # 项目文件夹选择
        ttk.Label(project_frame, text="项目文件夹:", font=self.default_font).grid(
            row=0, column=0, sticky="w", pady=5
        )
        ttk.Entry(project_frame, textvariable=self.extractor_project_folder, width=50).grid(
            row=0, column=1, sticky="ew", padx=(10, 5), pady=5
        )
        ttk.Button(project_frame, text="浏览",
                  command=self.browse_extractor_project_folder).grid(
            row=0, column=2, padx=(0, 5), pady=5
        )

        # 项目选择
        ttk.Label(project_frame, text="选择项目:", font=self.default_font).grid(
            row=1, column=0, sticky="w", pady=5
        )
        self.extractor_project_combobox = ttk.Combobox(project_frame, textvariable=self.extractor_selected_project,
                                                     width=47, state="readonly")
        self.extractor_project_combobox.grid(row=1, column=1, sticky="ew", padx=(10, 5), pady=5)
        self.extractor_project_combobox.bind('<<ComboboxSelected>>', self.on_extractor_project_selected)

        ttk.Button(project_frame, text="刷新",
                  command=self.refresh_extractor_project_list).grid(
            row=1, column=2, padx=(0, 5), pady=5
        )

        # 草稿选择
        ttk.Label(project_frame, text="选择草稿:", font=self.default_font).grid(
            row=2, column=0, sticky="w", pady=5
        )
        self.extractor_draft_combobox = ttk.Combobox(project_frame, textvariable=self.extractor_selected_draft,
                                                   width=47, state="readonly")
        self.extractor_draft_combobox.grid(row=2, column=1, sticky="ew", padx=(10, 5), pady=5)

        project_frame.columnconfigure(1, weight=1)

        # 提取选项区域
        options_frame = ttk.LabelFrame(extractor_frame, text="提取选项", padding="10")
        options_frame.pack(fill="x", pady=(0, 10))

        # 说明文字
        instruction_text = """功能说明：
1. 从特征缓存中提取片段时间戳信息
2. 从剪映项目中提取轨道片段信息
3. 生成详细的时间戳统计报告
4. 支持主轨道和第二轨道分析
5. 自动生成时间戳文件到 timestamp_reports 文件夹"""

        ttk.Label(options_frame, text=instruction_text, font=self.default_font,
                 justify="left").pack(anchor="w", pady=(0, 10))

        # 操作按钮区域
        button_frame = ttk.Frame(extractor_frame)
        button_frame.pack(fill="x", pady=(0, 10))

        ttk.Button(button_frame, text="🚀 提取时间戳",
                  command=self.extract_timestamps,
                  style="Accent.TButton").pack(side="left")
        ttk.Button(button_frame, text="📂 打开报告文件夹",
                  command=self.open_reports_folder).pack(side="left", padx=(10, 0))

        # 结果显示区域
        result_frame = ttk.LabelFrame(extractor_frame, text="提取结果", padding="10")
        result_frame.pack(fill="both", expand=True)

        result_text_frame = ttk.Frame(result_frame)
        result_text_frame.pack(fill="both", expand=True)

        self.extractor_result_text = tk.Text(result_text_frame, height=12, font=("Consolas", 9))
        extractor_scrollbar = ttk.Scrollbar(result_text_frame, orient="vertical",
                                          command=self.extractor_result_text.yview)

        self.extractor_result_text.configure(yscrollcommand=extractor_scrollbar.set)

        self.extractor_result_text.pack(side="left", fill="both", expand=True)
        extractor_scrollbar.pack(side="right", fill="y")

    def browse_extractor_project_folder(self):
        """浏览提取器项目文件夹"""
        # 从记忆中获取初始目录
        initial_dir = self.folder_memory.get('extractor_project_folder', os.getcwd())

        folder = filedialog.askdirectory(
            title="选择剪映项目文件夹",
            initialdir=initial_dir
        )
        if folder:
            self.extractor_project_folder.set(folder)
            # 保存到记忆中
            self.folder_memory['extractor_project_folder'] = folder
            self.save_folder_memory()
            self.refresh_extractor_project_list()

    def refresh_extractor_project_list(self):
        """刷新提取器项目列表"""
        project_folder = self.extractor_project_folder.get().strip()
        if not project_folder or not os.path.exists(project_folder):
            self.extractor_project_list = []
            self.extractor_project_combobox['values'] = []
            return

        try:
            # 扫描项目文件夹
            projects = []
            for item in os.listdir(project_folder):
                item_path = os.path.join(project_folder, item)
                if os.path.isdir(item_path):
                    projects.append(item)

            self.extractor_project_list = sorted(projects)
            self.extractor_project_combobox['values'] = self.extractor_project_list

            if self.extractor_project_list:
                self.extractor_project_combobox.set(self.extractor_project_list[0])
                self.on_extractor_project_selected(None)

            self.extractor_result_text.delete(1.0, tk.END)
            self.extractor_result_text.insert(tk.END, f"✅ 找到 {len(self.extractor_project_list)} 个项目\n")

        except Exception as e:
            messagebox.showerror("错误", f"扫描项目文件夹失败：{e}")

    def on_extractor_project_selected(self, event):
        """提取器项目选择事件"""
        selected_project = self.extractor_selected_project.get()
        if not selected_project:
            return

        project_folder = self.extractor_project_folder.get()
        project_path = os.path.join(project_folder, selected_project)

        try:
            # 扫描草稿文件
            drafts = []
            for item in os.listdir(project_path):
                if item.endswith('.json') and 'draft' in item.lower():
                    drafts.append(item)

            self.extractor_draft_list = sorted(drafts)
            self.extractor_draft_combobox['values'] = self.extractor_draft_list

            if self.extractor_draft_list:
                self.extractor_draft_combobox.set(self.extractor_draft_list[0])

            self.extractor_result_text.delete(1.0, tk.END)
            self.extractor_result_text.insert(tk.END, f"✅ 项目: {selected_project}\n")
            self.extractor_result_text.insert(tk.END, f"📁 找到 {len(self.extractor_draft_list)} 个草稿文件\n")

        except Exception as e:
            messagebox.showerror("错误", f"扫描草稿文件失败：{e}")

    def extract_timestamps(self):
        """提取时间戳"""
        # 检查项目选择
        if not self.extractor_selected_project.get():
            messagebox.showwarning("警告", "请先选择剪映项目！")
            return

        if not self.extractor_selected_draft.get():
            messagebox.showwarning("警告", "请先选择草稿文件！")
            return

        try:
            from timestamp_extractor import TimestampExtractor

            # 构建草稿文件路径
            project_folder = self.extractor_project_folder.get()
            project_name = self.extractor_selected_project.get()
            draft_name = self.extractor_selected_draft.get()
            draft_path = os.path.join(project_folder, project_name, draft_name)

            if not os.path.exists(draft_path):
                messagebox.showerror("错误", f"草稿文件不存在：{draft_path}")
                return

            self.extractor_result_text.delete(1.0, tk.END)
            self.extractor_result_text.insert(tk.END, "🚀 开始提取时间戳...\n")
            self.extractor_result_text.insert(tk.END, f"📁 项目：{project_name}\n")
            self.extractor_result_text.insert(tk.END, f"📄 草稿：{draft_name}\n\n")

            # 在后台线程中执行提取
            def extract_worker():
                try:
                    extractor = TimestampExtractor()
                    main_track, second_track = extractor.extract_timestamps(
                        project_path=os.path.join(project_folder, project_name),
                        draft_path=draft_path
                    )

                    # 在主线程中更新UI
                    self.root.after(0, lambda: self.display_extraction_results(main_track, second_track))

                except Exception as e:
                    self.root.after(0, lambda: self.display_extraction_error(str(e)))

            # 启动后台线程
            threading.Thread(target=extract_worker, daemon=True).start()

        except ImportError:
            self.extractor_result_text.insert(tk.END, "❌ 时间戳提取模块未找到\n")
            messagebox.showerror("错误", "时间戳提取模块未找到")
        except Exception as e:
            self.extractor_result_text.insert(tk.END, f"❌ 提取失败：{e}\n")
            messagebox.showerror("错误", f"提取时间戳失败：{e}")

    def display_extraction_results(self, main_track, second_track):
        """显示提取结果"""
        self.extractor_result_text.insert(tk.END, "✅ 时间戳提取完成！\n\n")

        # 显示统计信息
        self.extractor_result_text.insert(tk.END, "📊 提取结果统计：\n")
        self.extractor_result_text.insert(tk.END, f"🎬 主轨道：{len(main_track)} 个片段\n")
        self.extractor_result_text.insert(tk.END, f"🎥 第二轨道：{len(second_track)} 个片段\n\n")

        if main_track:
            main_total = sum(t.duration for t in main_track)
            self.extractor_result_text.insert(tk.END, f"主轨道总时长：{main_total:.2f}秒 ({main_total/60:.2f}分钟)\n")

        if second_track:
            second_total = sum(t.duration for t in second_track)
            self.extractor_result_text.insert(tk.END, f"第二轨道总时长：{second_total:.2f}秒 ({second_total/60:.2f}分钟)\n")

        self.extractor_result_text.insert(tk.END, "\n📂 时间戳文件已保存到 timestamp_reports 文件夹\n")
        self.extractor_result_text.insert(tk.END, "💡 点击'打开报告文件夹'查看详细报告\n")

        messagebox.showinfo("提取完成", f"时间戳提取完成！\n\n主轨道：{len(main_track)} 个片段\n第二轨道：{len(second_track)} 个片段\n\n报告已保存到 timestamp_reports 文件夹")

    def display_extraction_error(self, error_msg):
        """显示提取错误"""
        self.extractor_result_text.insert(tk.END, f"❌ 提取失败：{error_msg}\n")
        messagebox.showerror("提取失败", f"时间戳提取失败：{error_msg}")

    def open_reports_folder(self):
        """打开报告文件夹"""
        reports_folder = "timestamp_reports"
        if os.path.exists(reports_folder):
            os.startfile(reports_folder)
        else:
            messagebox.showwarning("警告", "报告文件夹不存在，请先执行时间戳提取！")

    def convert_timestamp_format(self):
        """格式转换功能 - 修正时间戳格式（同时处理时间戳文件和字幕文件）"""
        timestamp_file = self.importer_timestamp_file.get().strip()
        subtitle_file = self.importer_subtitle_file.get().strip()

        # 检查至少有一个文件被选择
        if not timestamp_file and not subtitle_file:
            messagebox.showwarning("警告", "请先选择时间戳文件或字幕文件！")
            return

        self.importer_result_text.delete(1.0, tk.END)
        self.importer_result_text.insert(tk.END, "🔄 开始格式转换...\n\n")

        converted_files = []
        total_changes = 0

        try:
            # 处理时间戳文件
            if timestamp_file and os.path.exists(timestamp_file):
                self.importer_result_text.insert(tk.END, f"📁 处理时间戳文件: {os.path.basename(timestamp_file)}\n")

                with open(timestamp_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                converted_content = self._fix_timestamp_format(content)

                if converted_content != content:
                    with open(timestamp_file, 'w', encoding='utf-8') as f:
                        f.write(converted_content)
                    converted_files.append(os.path.basename(timestamp_file))
                    changes = self._count_timestamp_changes(content, converted_content)
                    total_changes += changes
                    self.importer_result_text.insert(tk.END, f"  ✅ 已转换 {changes} 个时间戳\n")
                else:
                    self.importer_result_text.insert(tk.END, "  ✅ 格式已正确，无需修正\n")
            elif timestamp_file:
                self.importer_result_text.insert(tk.END, f"❌ 时间戳文件不存在: {os.path.basename(timestamp_file)}\n")

            # 处理字幕文件
            if subtitle_file and os.path.exists(subtitle_file):
                self.importer_result_text.insert(tk.END, f"\n📁 处理字幕文件: {os.path.basename(subtitle_file)}\n")

                with open(subtitle_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                converted_content = self._fix_timestamp_format(content)

                if converted_content != content:
                    with open(subtitle_file, 'w', encoding='utf-8') as f:
                        f.write(converted_content)
                    converted_files.append(os.path.basename(subtitle_file))
                    changes = self._count_timestamp_changes(content, converted_content)
                    total_changes += changes
                    self.importer_result_text.insert(tk.END, f"  ✅ 已转换 {changes} 个时间戳\n")
                else:
                    self.importer_result_text.insert(tk.END, "  ✅ 格式已正确，无需修正\n")
            elif subtitle_file:
                self.importer_result_text.insert(tk.END, f"\n❌ 字幕文件不存在: {os.path.basename(subtitle_file)}\n")

            # 显示总结
            self.importer_result_text.insert(tk.END, f"\n🎉 格式转换完成！\n")
            self.importer_result_text.insert(tk.END, f"📊 共处理 {len(converted_files)} 个文件，转换 {total_changes} 个时间戳\n")

            if converted_files:
                self.importer_result_text.insert(tk.END, f"📝 已修改文件: {', '.join(converted_files)}\n")

                # 显示转换示例
                if total_changes > 0:
                    self.importer_result_text.insert(tk.END, "\n📋 转换示例:\n")
                    self.importer_result_text.insert(tk.END, "  01:05,800 --> 00:01:09,500 → 00:01:05,800 --> 00:01:09,500\n")
                    self.importer_result_text.insert(tk.END, "  09:38,400 --> 09:44,900 → 00:09:38,400 --> 00:09:44,900\n")
                    self.importer_result_text.insert(tk.END, "  17:51:800 --> 17:53:500 → 00:17:51,800 --> 00:17:53,500\n")

                # 自动重新打开文件
                self.importer_result_text.insert(tk.END, "\n🔄 正在重新打开文件...\n")
                self._reopen_files_after_conversion(timestamp_file, subtitle_file)

                messagebox.showinfo("格式转换成功",
                    f"格式转换完成！\n\n"
                    f"处理文件: {len(converted_files)} 个\n"
                    f"转换时间戳: {total_changes} 个\n\n"
                    f"已修改文件:\n{chr(10).join(converted_files)}\n\n"
                    f"文件已自动重新打开显示最新内容")
            else:
                messagebox.showinfo("格式转换", "所有文件格式都已正确，无需修正")

        except Exception as e:
            self.importer_result_text.insert(tk.END, f"\n❌ 格式转换失败：{e}\n")
            messagebox.showerror("错误", f"格式转换失败：{e}")

    def _count_timestamp_changes(self, original: str, converted: str) -> int:
        """统计时间戳转换的数量"""
        original_timestamps = len([line for line in original.split('\n') if '-->' in line])
        converted_timestamps = len([line for line in converted.split('\n') if '-->' in line])

        # 计算实际发生变化的时间戳数量
        original_lines = original.split('\n')
        converted_lines = converted.split('\n')

        changes = 0
        for orig, conv in zip(original_lines, converted_lines):
            if '-->' in orig and orig != conv:
                changes += 1

        return changes

    def _reopen_files_after_conversion(self, timestamp_file: str, subtitle_file: str):
        """转换后重新打开文件"""
        try:
            import subprocess
            import platform

            files_to_open = []

            # 收集需要打开的文件
            if timestamp_file and os.path.exists(timestamp_file):
                files_to_open.append(('时间戳文件', timestamp_file))

            if subtitle_file and os.path.exists(subtitle_file):
                files_to_open.append(('字幕文件', subtitle_file))

            if not files_to_open:
                self.importer_result_text.insert(tk.END, "⚠️ 没有文件需要打开\n")
                return

            # 延迟执行文件打开操作
            self.root.after(500, lambda: self._execute_file_reopen(files_to_open))

        except Exception as e:
            self.importer_result_text.insert(tk.END, f"⚠️ 文件重新打开准备失败: {e}\n")

    def _execute_file_reopen(self, files_to_open):
        """执行文件重新打开操作"""
        try:
            import subprocess
            import platform

            system = platform.system()

            for file_type, file_path in files_to_open:
                try:
                    # 根据操作系统选择合适的打开方式
                    if system == "Windows":
                        # Windows: 使用默认程序打开文件
                        subprocess.Popen(['start', '', file_path], shell=True)
                    elif system == "Darwin":  # macOS
                        subprocess.Popen(['open', file_path])
                    else:  # Linux
                        subprocess.Popen(['xdg-open', file_path])

                    self.importer_result_text.insert(tk.END, f"✅ {file_type}已重新打开: {os.path.basename(file_path)}\n")

                except Exception as e:
                    self.importer_result_text.insert(tk.END, f"❌ {file_type}打开失败: {os.path.basename(file_path)} - {e}\n")

            self.importer_result_text.insert(tk.END, "🎉 所有文件已重新打开完成！\n")

        except Exception as e:
            self.importer_result_text.insert(tk.END, f"❌ 执行文件重新打开失败: {e}\n")

    def merge_timestamps(self):
        """合并时间戳功能 - 合并间隔不超过8秒的相邻时间戳"""
        timestamp_file = self.importer_timestamp_file.get().strip()

        if not timestamp_file:
            messagebox.showwarning("警告", "请先选择时间戳文件！")
            return

        if not os.path.exists(timestamp_file):
            messagebox.showerror("错误", "时间戳文件不存在！")
            return

        self.importer_result_text.delete(1.0, tk.END)
        self.importer_result_text.insert(tk.END, "🔗 开始合并时间戳...\n\n")

        try:
            # 读取文件内容
            with open(timestamp_file, 'r', encoding='utf-8') as f:
                content = f.read()

            self.importer_result_text.insert(tk.END, f"📁 处理文件: {os.path.basename(timestamp_file)}\n")

            # 解析时间戳
            timestamps = self._parse_timestamps_for_merge(content)

            if not timestamps:
                self.importer_result_text.insert(tk.END, "❌ 未找到有效的时间戳\n")
                messagebox.showerror("错误", "文件中没有找到有效的时间戳")
                return

            original_count = len(timestamps)
            self.importer_result_text.insert(tk.END, f"📊 原始时间戳数量: {original_count}\n")

            # 执行合并
            merged_timestamps = self._merge_adjacent_timestamps(timestamps)
            merged_count = len(merged_timestamps)

            self.importer_result_text.insert(tk.END, f"📊 合并后时间戳数量: {merged_count}\n")
            self.importer_result_text.insert(tk.END, f"🔗 合并了 {original_count - merged_count} 个时间戳\n")
            self.importer_result_text.insert(tk.END, "💡 注意: 只保留第一个时间戳的字幕内容\n\n")

            if original_count == merged_count:
                self.importer_result_text.insert(tk.END, "✅ 没有需要合并的时间戳（间隔都超过8秒）\n")
                messagebox.showinfo("合并完成", "没有找到需要合并的时间戳\n\n所有相邻时间戳的间隔都超过8秒")
                return

            # 重新构建文件内容
            merged_content = self._rebuild_timestamp_content(content, merged_timestamps)

            # 写回文件
            with open(timestamp_file, 'w', encoding='utf-8') as f:
                f.write(merged_content)

            # 显示合并结果
            self.importer_result_text.insert(tk.END, "📋 合并结果预览（前5个）：\n")
            self.importer_result_text.insert(tk.END, "-" * 40 + "\n")

            for i, ts in enumerate(merged_timestamps[:5], 1):
                self.importer_result_text.insert(tk.END, f"{i:2d}. {ts['start_time']} --> {ts['end_time']}\n")

            # 自动重新打开文件
            self.importer_result_text.insert(tk.END, "\n🔄 正在重新打开文件...\n")
            self._reopen_files_after_conversion(timestamp_file, None)

            messagebox.showinfo("合并成功",
                f"时间戳合并完成！\n\n"
                f"原始数量: {original_count} 个\n"
                f"合并后数量: {merged_count} 个\n"
                f"合并了: {original_count - merged_count} 个时间戳\n\n"
                f"注意: 只保留了第一个时间戳的字幕内容\n"
                f"文件已自动重新打开显示最新内容")

        except Exception as e:
            self.importer_result_text.insert(tk.END, f"\n❌ 合并失败：{e}\n")
            messagebox.showerror("错误", f"时间戳合并失败：{e}")

    def _parse_timestamps_for_merge(self, content: str) -> list:
        """解析时间戳用于合并 - 使用与导入功能相同的解析逻辑"""
        import re

        timestamps = []
        lines = content.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            try:
                # 使用与导入功能相同的解析逻辑
                entry = self._parse_timestamp_line_for_merge(line)
                if entry:
                    timestamps.append(entry)
            except Exception as e:
                continue  # 忽略解析失败的行

        return timestamps

    def _parse_timestamp_line_for_merge(self, line: str):
        """解析单行时间戳 - 使用与导入功能相同的逻辑"""
        import re

        try:
            line = line.strip()
            if not line:
                return None

            # 检测格式类型 - 优先检测带序号的格式
            if re.match(r'^\d+\.\s+.*', line):
                # 简化格式: "1. 33:33.530-33:42.670" 或 "1. 00:33:33,531 --> 00:33:46,567"
                return self._parse_simple_format_for_merge(line)
            elif '-->' in line:
                # SRT格式: "00:30:38,400 --> 00:30:43,566"
                return self._parse_srt_format_for_merge(line)
            else:
                # 标准格式: "001 33:32.130-33:33.530"
                return self._parse_standard_format_for_merge(line)

        except Exception as e:
            return None

    def _parse_srt_format_for_merge(self, line: str):
        """解析SRT格式用于合并"""
        import re

        time_parts = re.split(r'\s*-->\s*', line)
        if len(time_parts) != 2:
            return None

        start_time_sec = self._parse_srt_time_for_merge(time_parts[0].strip())
        end_time_sec = self._parse_srt_time_for_merge(time_parts[1].strip())

        if start_time_sec < 0 or end_time_sec < 0:
            return None

        return {
            'sequence': 1,  # 临时序号，后续会重新编号
            'start_time': time_parts[0].strip(),
            'end_time': time_parts[1].strip(),
            'start_seconds': start_time_sec,
            'end_seconds': end_time_sec,
            'subtitle_lines': []  # SRT格式不包含字幕内容
        }

    def _parse_srt_time_for_merge(self, time_str: str) -> float:
        """解析SRT时间格式用于合并"""
        try:
            # SRT格式: HH:MM:SS,mmm
            parts = time_str.split(':')
            if len(parts) != 3:
                return -1

            hours = int(parts[0])
            minutes = int(parts[1])

            # 处理秒和毫秒部分
            sec_parts = parts[2].split(',')
            if len(sec_parts) != 2:
                return -1

            seconds = int(sec_parts[0])
            milliseconds = int(sec_parts[1])

            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
        except Exception:
            return -1

    def _parse_standard_format_for_merge(self, line: str):
        """解析标准格式用于合并"""
        parts = line.split(None, 1)
        if len(parts) != 2:
            return None

        sequence_number = int(parts[0])
        time_range = parts[1]

        time_parts = time_range.split('-')
        if len(time_parts) != 2:
            return None

        start_time_sec = self._parse_time_string_for_merge(time_parts[0].strip())
        end_time_sec = self._parse_time_string_for_merge(time_parts[1].strip())

        if end_time_sec <= start_time_sec:
            return None

        return {
            'sequence': sequence_number,
            'start_time': self._seconds_to_srt_format(start_time_sec),
            'end_time': self._seconds_to_srt_format(end_time_sec),
            'start_seconds': start_time_sec,
            'end_seconds': end_time_sec,
            'subtitle_lines': []
        }

    def _parse_simple_format_for_merge(self, line: str):
        """解析简化格式用于合并"""
        import re

        match = re.match(r'^(\d+)\.\s+(.+)$', line)
        if not match:
            return None

        sequence_number = int(match.group(1))
        time_part = match.group(2).strip()

        # 检查是否是SRT格式的时间
        if '-->' in time_part:
            time_parts = re.split(r'\s*-->\s*', time_part)
            if len(time_parts) != 2:
                return None

            start_time_sec = self._parse_srt_time_for_merge(time_parts[0].strip())
            end_time_sec = self._parse_srt_time_for_merge(time_parts[1].strip())

            if start_time_sec < 0 or end_time_sec < 0:
                return None

            return {
                'sequence': sequence_number,
                'start_time': time_parts[0].strip(),
                'end_time': time_parts[1].strip(),
                'start_seconds': start_time_sec,
                'end_seconds': end_time_sec,
                'subtitle_lines': []
            }
        else:
            # 标准格式的时间
            time_parts = time_part.split('-')
            if len(time_parts) != 2:
                return None

            start_time_sec = self._parse_time_string_for_merge(time_parts[0].strip())
            end_time_sec = self._parse_time_string_for_merge(time_parts[1].strip())

            if end_time_sec <= start_time_sec:
                return None

            return {
                'sequence': sequence_number,
                'start_time': self._seconds_to_srt_format(start_time_sec),
                'end_time': self._seconds_to_srt_format(end_time_sec),
                'start_seconds': start_time_sec,
                'end_seconds': end_time_sec,
                'subtitle_lines': []
            }

    def _parse_time_string_for_merge(self, time_str: str) -> float:
        """解析时间字符串用于合并"""
        try:
            parts = time_str.split(':')

            if len(parts) == 1:
                # 格式: "45.678" (秒)
                return float(parts[0])
            elif len(parts) == 2:
                # 格式: "33:32.130" (分:秒)
                minutes = int(parts[0])
                seconds = float(parts[1])
                return minutes * 60.0 + seconds
            elif len(parts) == 3:
                # 格式: "1:23:45.678" (时:分:秒)
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = float(parts[2])
                return hours * 3600.0 + minutes * 60.0 + seconds
            else:
                return -1
        except Exception:
            return -1

    def _seconds_to_srt_format(self, seconds: float) -> str:
        """将秒数转换为SRT格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def _merge_adjacent_timestamps(self, timestamps: list) -> list:
        """合并相邻的时间戳（间隔不超过8秒）- 只合并时间戳，不合并字幕内容"""
        if len(timestamps) <= 1:
            return timestamps

        merged = []
        current = timestamps[0].copy()

        for i in range(1, len(timestamps)):
            next_ts = timestamps[i]

            # 计算当前时间戳结束时间与下一个开始时间的间隔
            gap = next_ts['start_seconds'] - current['end_seconds']

            if gap <= 8.0:  # 间隔不超过8秒，合并
                # 只更新结束时间为下一个时间戳的结束时间
                # 保留第一个时间戳的字幕内容，丢弃后续的字幕内容
                current['end_time'] = next_ts['end_time']
                current['end_seconds'] = next_ts['end_seconds']
                # 注意：不合并字幕内容，只保留第一个时间戳的字幕
            else:
                # 间隔超过1秒，保存当前时间戳，开始新的时间戳
                merged.append(current)
                current = next_ts.copy()

        # 添加最后一个时间戳
        merged.append(current)

        return merged

    def _rebuild_timestamp_content(self, original_content: str, merged_timestamps: list) -> str:
        """重建时间戳文件内容 - 保持原有格式"""
        import re

        # 检测原始文件的格式
        lines = original_content.strip().split('\n')
        original_format = self._detect_original_format(lines)

        new_lines = []

        for i, merged_ts in enumerate(merged_timestamps):
            if original_format == "srt":
                # SRT格式：序号 + 时间戳 + 字幕内容
                new_lines.append(str(i + 1))
                new_lines.append(f"{merged_ts['start_time']} --> {merged_ts['end_time']}")
                if 'subtitle_lines' in merged_ts and merged_ts['subtitle_lines']:
                    for subtitle_line in merged_ts['subtitle_lines']:
                        new_lines.append(subtitle_line)
                if i < len(merged_timestamps) - 1:
                    new_lines.append("")

            elif original_format == "simple":
                # 简化格式：序号. 时间戳
                new_lines.append(f"{i + 1}. {merged_ts['start_time']} --> {merged_ts['end_time']}")

            elif original_format == "standard":
                # 标准格式：序号 时间-时间
                start_time_std = self._srt_to_standard_format(merged_ts['start_seconds'])
                end_time_std = self._srt_to_standard_format(merged_ts['end_seconds'])
                new_lines.append(f"{i + 1:03d} {start_time_std}-{end_time_std}")

            else:
                # 默认使用SRT格式
                new_lines.append(f"{merged_ts['start_time']} --> {merged_ts['end_time']}")

        return '\n'.join(new_lines)

    def _detect_original_format(self, lines: list) -> str:
        """检测原始文件的格式"""
        import re

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测简化格式: "1. 时间戳"
            if re.match(r'^\d+\.\s+.*-->', line):
                return "simple"

            # 检测标准格式: "001 时间-时间"
            elif re.match(r'^\d+\s+.*-.*', line) and '-->' not in line:
                return "standard"

            # 检测SRT格式: 纯时间戳行或序号行
            elif '-->' in line or line.isdigit():
                return "srt"

        return "srt"  # 默认格式

    def _srt_to_standard_format(self, seconds: float) -> str:
        """将秒数转换为标准格式 (MM:SS.mmm)"""
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}:{secs:06.3f}"

    def _fix_timestamp_format(self, content: str) -> str:
        """修正时间戳格式"""
        import re

        lines = content.split('\n')
        fixed_lines = []

        for line in lines:
            fixed_line = line

            # 处理 SRT 格式的时间戳行 (包含 -->)
            if '-->' in line:
                # 检查是否已经是完全标准格式 HH:MM:SS,mmm --> HH:MM:SS,mmm
                if re.search(r'\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}', line):
                    # 已经是标准格式，不需要修改
                    pass
                else:
                    # 处理混合格式和各种错误格式
                    fixed_line = self._fix_single_timestamp_line(line)

            fixed_lines.append(fixed_line)

        return '\n'.join(fixed_lines)

    def _fix_single_timestamp_line(self, line: str) -> str:
        """修正单行时间戳格式"""
        import re

        # 匹配时间戳格式，支持多种格式
        # 可能的格式：
        # HH:MM:SS,mmm --> MM:SS,mmm
        # MM:SS,mmm --> HH:MM:SS,mmm
        # MM:SS,mmm --> MM:SS,mmm
        # MM:SS:mmm --> MM:SS:mmm (新格式)
        # HH:MM:SS,mmm --> HH:MM:SS,mmm (已经正确)

        # 首先尝试匹配 MM:SS:mmm 格式（使用冒号分隔毫秒）
        pattern_colon_ms = r'(\d{1,2}):(\d{2}):(\d{3})\s*-->\s*(\d{1,2}):(\d{2}):(\d{3})'
        match_colon_ms = re.search(pattern_colon_ms, line)

        if match_colon_ms:
            start_min, start_sec, start_ms, end_min, end_sec, end_ms = match_colon_ms.groups()

            # 处理开始时间 - MM:SS:mmm 格式
            start_total_min = int(start_min)
            start_hour = start_total_min // 60
            start_min_corrected = start_total_min % 60

            # 处理结束时间 - MM:SS:mmm 格式
            end_total_min = int(end_min)
            end_hour = end_total_min // 60
            end_min_corrected = end_total_min % 60

            corrected_timestamp = f"{start_hour:02d}:{start_min_corrected:02d}:{start_sec},{start_ms} --> {end_hour:02d}:{end_min_corrected:02d}:{end_sec},{end_ms}"
            return line.replace(match_colon_ms.group(0), corrected_timestamp)

        # 然后尝试匹配其他格式（使用逗号分隔毫秒）
        pattern = r'(\d{1,2}):(\d{2})(?::(\d{2}))?,(\d{3})\s*-->\s*(\d{1,2}):(\d{2})(?::(\d{2}))?,(\d{3})'
        match = re.search(pattern, line)

        if match:
            start_part1, start_part2, start_part3, start_ms, end_part1, end_part2, end_part3, end_ms = match.groups()

            # 处理开始时间
            start_hour, start_min, start_sec = self._parse_time_parts(start_part1, start_part2, start_part3)

            # 处理结束时间
            end_hour, end_min, end_sec = self._parse_time_parts(end_part1, end_part2, end_part3)

            corrected_timestamp = f"{start_hour:02d}:{start_min:02d}:{start_sec:02d},{start_ms} --> {end_hour:02d}:{end_min:02d}:{end_sec:02d},{end_ms}"
            return line.replace(match.group(0), corrected_timestamp)

        return line

    def _parse_time_parts(self, part1: str, part2: str, part3: str = None) -> tuple:
        """解析时间部分，返回 (小时, 分钟, 秒)"""
        if part3 is not None:
            # 已经是 HH:MM:SS 格式
            return int(part1), int(part2), int(part3)
        else:
            # 是 MM:SS 格式，需要转换
            total_minutes = int(part1)
            seconds = int(part2)

            # 将总分钟数转换为小时和分钟
            hours = total_minutes // 60
            minutes = total_minutes % 60

            return hours, minutes, seconds

    def create_scene_detector_tab(self):
        """创建场景检测选项卡"""
        # 创建选项卡框架
        scene_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(scene_frame, text="🎬 场景分镜检测")

        # 视频文件选择区域
        video_frame = ttk.LabelFrame(scene_frame, text="视频文件选择", padding="10")
        video_frame.pack(fill="x", pady=(0, 10))

        # 视频文件选择
        video_select_frame = ttk.Frame(video_frame)
        video_select_frame.pack(fill="x", pady=5)

        ttk.Label(video_select_frame, text="视频文件:").pack(side="left")
        video_entry = ttk.Entry(video_select_frame, textvariable=self.scene_video_file, width=50)
        video_entry.pack(side="left", padx=(10, 5), fill="x", expand=True)

        ttk.Button(video_select_frame, text="浏览",
                  command=self.browse_scene_video_file).pack(side="right")

        # 检测参数设置区域
        params_frame = ttk.LabelFrame(scene_frame, text="分段参数", padding="10")
        params_frame.pack(fill="x", pady=(0, 10))

        # 参数设置
        param_grid = ttk.Frame(params_frame)
        param_grid.pack(fill="x")

        # 敏感度设置
        ttk.Label(param_grid, text="场景检测敏感度:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        threshold_spinbox = ttk.Spinbox(param_grid, from_=1.0, to=100.0, increment=1.0,
                                       textvariable=self.scene_threshold, width=10,
                                       command=self.save_parameter_memory)
        threshold_spinbox.grid(row=0, column=1, sticky="w")
        threshold_spinbox.bind('<KeyRelease>', lambda e: self.save_parameter_memory())
        ttk.Label(param_grid, text="(1-100)").grid(row=0, column=2, sticky="w", padx=(5, 0))

        # 目标分段长度
        ttk.Label(param_grid, text="目标分段长度(分钟):").grid(row=1, column=0, sticky="w", padx=(0, 10), pady=(10, 0))
        self.segment_length = tk.DoubleVar(value=7.0)
        segment_spinbox = ttk.Spinbox(param_grid, from_=5.0, to=30.0, increment=1.0,
                                     textvariable=self.segment_length, width=10,
                                     command=self.save_parameter_memory)
        segment_spinbox.grid(row=1, column=1, sticky="w", pady=(10, 0))
        segment_spinbox.bind('<KeyRelease>', lambda e: self.save_parameter_memory())
        ttk.Label(param_grid, text="分钟").grid(row=1, column=2, sticky="w", padx=(5, 0), pady=(10, 0))

        # 最小场景长度
        ttk.Label(param_grid, text="最小场景长度(秒):").grid(row=2, column=0, sticky="w", padx=(0, 10), pady=(10, 0))
        length_spinbox = ttk.Spinbox(param_grid, from_=0.1, to=10.0, increment=0.1,
                                    textvariable=self.min_scene_length, width=10,
                                    command=self.save_parameter_memory)
        length_spinbox.grid(row=2, column=1, sticky="w", pady=(10, 0))
        length_spinbox.bind('<KeyRelease>', lambda e: self.save_parameter_memory())
        ttk.Label(param_grid, text="秒").grid(row=2, column=2, sticky="w", padx=(5, 0), pady=(10, 0))

        # 最长场景长度
        ttk.Label(param_grid, text="最长场景长度(秒):").grid(row=3, column=0, sticky="w", padx=(0, 10), pady=(10, 0))
        self.max_scene_length = tk.DoubleVar(value=30.0)
        max_length_spinbox = ttk.Spinbox(param_grid, from_=5.0, to=300.0, increment=1.0,
                                        textvariable=self.max_scene_length, width=10,
                                        command=self.save_parameter_memory)
        max_length_spinbox.grid(row=3, column=1, sticky="w", pady=(10, 0))
        max_length_spinbox.bind('<KeyRelease>', lambda e: self.save_parameter_memory())
        ttk.Label(param_grid, text="秒").grid(row=3, column=2, sticky="w", padx=(5, 0), pady=(10, 0))

        # 长场景分割敏感度
        ttk.Label(param_grid, text="长场景分割敏感度:").grid(row=4, column=0, sticky="w", padx=(0, 10), pady=(10, 0))
        self.long_scene_sensitivity = tk.DoubleVar(value=0.7)
        sensitivity_spinbox = ttk.Spinbox(param_grid, from_=0.1, to=1.0, increment=0.1,
                                         textvariable=self.long_scene_sensitivity, width=10,
                                         command=self.save_parameter_memory)
        sensitivity_spinbox.grid(row=4, column=1, sticky="w", pady=(10, 0))
        sensitivity_spinbox.bind('<KeyRelease>', lambda e: self.save_parameter_memory())
        ttk.Label(param_grid, text="(0.1敏感-1.0宽松)").grid(row=4, column=2, sticky="w", padx=(5, 0), pady=(10, 0))

        param_grid.columnconfigure(1, weight=1)

        # 输出设置区域
        output_frame = ttk.LabelFrame(scene_frame, text="输出设置", padding="10")
        output_frame.pack(fill="x", pady=(0, 10))

        # 输出文件夹选择
        output_select_frame = ttk.Frame(output_frame)
        output_select_frame.pack(fill="x", pady=5)

        ttk.Label(output_select_frame, text="输出文件夹:").pack(side="left")
        self.scene_output_folder = tk.StringVar()
        output_entry = ttk.Entry(output_select_frame, textvariable=self.scene_output_folder, width=50)
        output_entry.pack(side="left", padx=(10, 5), fill="x", expand=True)

        ttk.Button(output_select_frame, text="浏览",
                  command=self.browse_scene_output_folder).pack(side="right")

        # 输出说明
        info_frame = ttk.Frame(output_frame)
        info_frame.pack(fill="x", pady=(5, 0))
        ttk.Label(info_frame, text="💡 将输出多个视频文件和对应的时间戳文件",
                 font=("Arial", 9), foreground="gray").pack(side="left")

        # 操作按钮区域
        button_frame = ttk.Frame(scene_frame)
        button_frame.pack(fill="x", pady=(0, 10))

        self.scene_detect_button = ttk.Button(button_frame, text="🎬 开始智能分段",
                                             command=self.detect_scenes,
                                             style="Accent.TButton")
        self.scene_detect_button.pack(side="left")

        ttk.Button(button_frame, text="📋 预览结果",
                  command=self.preview_scene_results).pack(side="left", padx=(10, 0))

        ttk.Button(button_frame, text="🗑️ 清空结果",
                  command=self.clear_scene_results).pack(side="left", padx=(10, 0))

        # 结果显示区域
        result_frame = ttk.LabelFrame(scene_frame, text="检测结果", padding="10")
        result_frame.pack(fill="both", expand=True)

        # 创建结果文本框和滚动条
        result_text_frame = ttk.Frame(result_frame)
        result_text_frame.pack(fill="both", expand=True)

        self.scene_result_text = tk.Text(result_text_frame, height=15, wrap=tk.WORD)
        scene_scrollbar = ttk.Scrollbar(result_text_frame, orient="vertical", command=self.scene_result_text.yview)
        self.scene_result_text.configure(yscrollcommand=scene_scrollbar.set)

        self.scene_result_text.pack(side="left", fill="both", expand=True)
        scene_scrollbar.pack(side="right", fill="y")

        # 加载保存的设置
        self.load_scene_detector_settings()

        # 延迟加载参数，确保所有控件都已创建
        self.root.after(100, self.load_scene_detector_parameters)

    def load_scene_detector_settings(self):
        """加载场景检测的文件路径设置"""
        try:
            # 确保文件夹记忆已加载
            if not hasattr(self, 'folder_memory') or not self.folder_memory:
                self.load_folder_memory()

            # 如果有保存的视频文件路径，恢复它
            if 'scene_video_file' in self.folder_memory:
                saved_video_file = self.folder_memory['scene_video_file']
                if os.path.exists(saved_video_file):
                    self.scene_video_file.set(saved_video_file)
                    # 自动设置输出文件名
                    base_name = os.path.splitext(saved_video_file)[0]
                    output_file = f"{base_name}_scenes.txt"
                    self.scene_output_file.set(output_file)
                    self.logger.info(f"恢复视频文件: {os.path.basename(saved_video_file)}")

            # 如果有保存的输出文件夹路径，恢复它
            if 'scene_output_folder_path' in self.folder_memory:
                saved_output_folder = self.folder_memory['scene_output_folder_path']
                self.scene_output_folder.set(saved_output_folder)
                self.logger.info(f"恢复输出文件夹: {saved_output_folder}")

            # 兼容旧版本的输出文件设置
            if 'scene_output_file' in self.folder_memory:
                saved_output_file = self.folder_memory['scene_output_file']
                self.scene_output_file.set(saved_output_file)

        except Exception as e:
            self.logger.error(f"加载场景检测设置失败: {e}")

    def save_scene_detector_settings(self):
        """保存场景检测的当前设置"""
        try:
            # 保存当前文件路径
            if self.scene_video_file.get():
                self.folder_memory['scene_video_file'] = self.scene_video_file.get()

            if self.scene_output_folder.get():
                self.folder_memory['scene_output_folder_path'] = self.scene_output_folder.get()

            # 兼容性保持
            if self.scene_output_file.get():
                self.folder_memory['scene_output_file'] = self.scene_output_file.get()

            # 保存文件夹和参数记忆
            self.save_folder_memory()
            self.save_parameter_memory()

        except Exception as e:
            self.logger.error(f"保存场景检测设置失败: {e}")

    def load_scene_detector_parameters(self):
        """延迟加载场景检测参数，确保控件已创建"""
        try:
            # 确保参数记忆已加载
            if not hasattr(self, 'parameter_memory') or not self.parameter_memory:
                import json
                memory_file = "parameter_memory.json"
                if os.path.exists(memory_file):
                    with open(memory_file, 'r', encoding='utf-8') as f:
                        self.parameter_memory = json.load(f)
                else:
                    self.parameter_memory = {}

            # 恢复场景检测参数
            if 'scene_threshold' in self.parameter_memory:
                self.scene_threshold.set(self.parameter_memory['scene_threshold'])
                self.logger.info(f"恢复场景敏感度: {self.parameter_memory['scene_threshold']:.1f}")

            if 'min_scene_length' in self.parameter_memory:
                self.min_scene_length.set(self.parameter_memory['min_scene_length'])
                self.logger.info(f"恢复最小场景长度: {self.parameter_memory['min_scene_length']:.1f}")

            if 'max_scene_length' in self.parameter_memory:
                self.max_scene_length.set(self.parameter_memory['max_scene_length'])
                self.logger.info(f"恢复最长场景长度: {self.parameter_memory['max_scene_length']:.1f}")

            if 'long_scene_sensitivity' in self.parameter_memory:
                self.long_scene_sensitivity.set(self.parameter_memory['long_scene_sensitivity'])
                self.logger.info(f"恢复长场景分割敏感度: {self.parameter_memory['long_scene_sensitivity']:.1f}")

            if 'segment_length' in self.parameter_memory:
                self.segment_length.set(self.parameter_memory['segment_length'])
                self.logger.info(f"恢复分段长度: {self.parameter_memory['segment_length']:.1f}")

            self.logger.info(f"场景检测参数已加载: 敏感度={self.scene_threshold.get():.1f}, 最小长度={self.min_scene_length.get():.1f}, 分段长度={self.segment_length.get():.1f}")

        except Exception as e:
            self.logger.error(f"加载场景检测参数失败: {e}")



    def browse_scene_video_file(self):
        """浏览选择视频文件"""
        # 获取上次选择的目录
        initial_dir = self.folder_memory.get('scene_video_folder', os.getcwd())

        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            initialdir=initial_dir,
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.scene_video_file.set(file_path)
            # 记忆文件夹路径和文件路径
            self.folder_memory['scene_video_folder'] = os.path.dirname(file_path)
            self.folder_memory['scene_video_file'] = file_path
            self.save_folder_memory()

            # 自动设置输出文件夹（视频文件同目录下的segments文件夹）
            video_dir = os.path.dirname(file_path)
            video_name = os.path.splitext(os.path.basename(file_path))[0]
            output_folder = os.path.join(video_dir, f"{video_name}_segments")
            self.scene_output_folder.set(output_folder)
            # 也保存输出文件夹路径
            self.folder_memory['scene_output_folder_path'] = output_folder
            self.save_folder_memory()

            self.logger.info(f"选择视频文件: {os.path.basename(file_path)}")
            self.logger.info(f"输出文件夹: {output_folder}")

    def browse_scene_output_folder(self):
        """浏览选择输出文件夹"""
        # 获取上次选择的目录
        initial_dir = self.folder_memory.get('scene_output_folder_path', os.getcwd())

        folder_path = filedialog.askdirectory(
            title="选择输出文件夹",
            initialdir=initial_dir
        )
        if folder_path:
            self.scene_output_folder.set(folder_path)
            # 记忆文件夹路径
            self.folder_memory['scene_output_folder_path'] = folder_path
            self.save_folder_memory()

            self.logger.info(f"选择输出文件夹: {folder_path}")

    def detect_scenes(self):
        """智能分段视频"""
        video_file = self.scene_video_file.get().strip()
        output_folder = self.scene_output_folder.get().strip()

        if not video_file:
            messagebox.showwarning("警告", "请选择视频文件！")
            return

        if not os.path.exists(video_file):
            messagebox.showerror("错误", "视频文件不存在！")
            return

        if not output_folder:
            messagebox.showwarning("警告", "请设置输出文件夹！")
            return

        # 禁用检测按钮，防止重复点击
        self.scene_detect_button.config(state="disabled")

        self.scene_result_text.delete(1.0, tk.END)
        self.scene_result_text.insert(tk.END, "🎬 开始智能分段视频...\n\n")
        self.scene_result_text.insert(tk.END, "⏳ 正在初始化，请稍候...\n")

        # 保存当前设置
        self.save_scene_detector_settings()

        # 在后台线程中执行智能分段
        import threading
        detection_thread = threading.Thread(target=self._segment_video_background,
                                           args=(video_file, output_folder))
        detection_thread.daemon = True
        detection_thread.start()

    def _segment_video_background(self, video_file, output_folder):
        """在后台线程中进行智能分段"""
        try:
            # 检查是否安装了PySceneDetect
            try:
                import scenedetect
                from scenedetect import detect, ContentDetector
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "✅ PySceneDetect 已安装\n"))
                self.root.after(0, lambda: self.scene_result_text.see(tk.END))
            except ImportError:
                error_msg = "❌ 未安装 PySceneDetect\n请运行: pip install scenedetect\n"
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, error_msg))
                self.root.after(0, lambda: messagebox.showerror("错误", "未安装 PySceneDetect\n\n请运行以下命令安装：\npip install scenedetect"))
                self.root.after(0, self._enable_detect_button)
                return

            # 执行智能分段
            self._perform_intelligent_segmentation(video_file, output_folder)

        except Exception as e:
            error_msg = f"❌ 智能分段失败：{e}\n"
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, error_msg))
            self.root.after(0, lambda: messagebox.showerror("错误", f"智能分段失败：{e}"))
            self.root.after(0, self._enable_detect_button)

    def _perform_intelligent_segmentation(self, video_file, output_folder):
        """执行智能分段"""
        try:
            # 提高进程优先级以使用更多系统资源
            import psutil
            import os
            try:
                # 设置为高优先级
                p = psutil.Process(os.getpid())
                if hasattr(psutil, 'HIGH_PRIORITY_CLASS'):
                    p.nice(psutil.HIGH_PRIORITY_CLASS)  # Windows
                else:
                    p.nice(-10)  # Linux/Mac，数值越小优先级越高
                self.root.after(0, lambda:
                    self.scene_result_text.insert(tk.END, f"🚀 已提高进程优先级，使用更多系统资源\n"))
            except Exception:
                pass  # 如果设置失败就忽略
            from scenedetect import detect, ContentDetector

            threshold = self.scene_threshold.get()
            min_scene_len = self.min_scene_length.get()
            max_scene_len = self.max_scene_length.get()
            target_segment_minutes = self.segment_length.get()
            target_segment_seconds = target_segment_minutes * 60

            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"📁 视频文件: {os.path.basename(video_file)}\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"🎯 场景检测敏感度: {threshold}\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"⏱️ 目标分段长度: {target_segment_minutes}分钟\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"📏 最小场景长度: {min_scene_len}秒\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"⏰ 最长场景长度: {max_scene_len}秒\n"))
            long_scene_sens = self.long_scene_sensitivity.get()
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"🔧 长场景分割敏感度: {long_scene_sens:.1f}\n\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))

            # 第一步：检测所有场景切换点
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "🔍 第一步：检测场景切换点...\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))

            # 优化性能：使用多线程和更高效的设置
            import cv2
            # 设置OpenCV使用所有可用CPU核心
            cv2.setNumThreads(0)  # 0表示使用所有可用核心

            detector = ContentDetector(threshold=threshold)
            scene_list = detect(video_file, detector, show_progress=False)

            # 缓存场景检测结果以供智能分割使用
            self._cached_all_scenes = scene_list

            if not scene_list:
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "⚠️ 未检测到场景切换点\n"))
                self.root.after(0, lambda: messagebox.showinfo("检测完成", "未检测到明显的场景切换"))
                self.root.after(0, self._enable_detect_button)
                return

            # 过滤场景长度并分割过长场景
            all_scenes_data = []  # 存储所有场景数据，包括是否需要分割的标记
            split_count = 0
            long_scenes = []  # 收集需要智能分割的长场景

            # 第一遍：分类所有场景
            for i, (start_time, end_time) in enumerate(scene_list):
                duration = end_time.get_seconds() - start_time.get_seconds()
                start_seconds = start_time.get_seconds()
                end_seconds = end_time.get_seconds()

                if duration < min_scene_len:
                    # 场景太短，跳过
                    all_scenes_data.append(None)  # 占位符，保持索引对应
                elif duration <= max_scene_len:
                    # 场景长度合适，直接使用
                    all_scenes_data.append([(start_seconds, end_seconds)])
                else:
                    # 场景太长，标记为需要分割
                    all_scenes_data.append('NEED_SPLIT')
                    long_scenes.append((i, start_seconds, end_seconds))  # 包含原始索引

            # 第二遍：多线程批量处理长场景以提高性能
            if long_scenes:
                self.root.after(0, lambda count=len(long_scenes):
                    self.scene_result_text.insert(tk.END, f"🚀 多线程处理 {count} 个超长场景以提高性能...\n"))

                # 显示线程配置信息
                max_workers = min(6, len(long_scenes))
                self.root.after(0, lambda workers=max_workers:
                    self.scene_result_text.insert(tk.END, f"⚡ 使用 {workers} 个线程并行处理\n"))

                # 显示每个长场景的时间戳信息
                for i, (original_index, start_seconds, end_seconds) in enumerate(long_scenes, 1):
                    duration = end_seconds - start_seconds
                    start_str = self._seconds_to_timestamp(start_seconds)
                    end_str = self._seconds_to_timestamp(end_seconds)
                    self.root.after(0, lambda idx=i, start=start_str, end=end_str, dur=duration:
                        self.scene_result_text.insert(tk.END, f"  📍 长场景{idx}: {start} --> {end} (时长{dur:.1f}秒)\n"))

                # 使用多线程处理长场景
                split_results = self._process_long_scenes_multithreaded(video_file, long_scenes, max_scene_len, threshold)

                # 将分割结果放回原始位置
                for (original_index, original_start, original_end), split_scenes in zip(long_scenes, split_results):
                    if split_scenes:
                        split_count += 1
                        all_scenes_data[original_index] = split_scenes
                    else:
                        # 如果分割失败，使用原场景
                        all_scenes_data[original_index] = [(original_start, original_end)]

            # 第三遍：按时间顺序重建场景列表
            filtered_scenes = []
            for scene_data in all_scenes_data:
                if scene_data and scene_data != 'NEED_SPLIT':
                    filtered_scenes.extend(scene_data)

            if split_count > 0:
                self.root.after(0, lambda count=split_count:
                    self.scene_result_text.insert(tk.END, f"🔧 分割了 {count} 个过长场景\n"))

            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"✅ 检测到 {len(filtered_scenes)} 个有效场景\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))

            # 第二步：智能分段
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "🧠 第二步：智能分段计算...\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"🎯 目标分段长度: {target_segment_minutes}分钟\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))

            segments = self._calculate_intelligent_segments(filtered_scenes, target_segment_seconds)

            # 显示分段统计
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"\n✅ 计算出 {len(segments)} 个分段\n"))
            for i, segment in enumerate(segments, 1):
                duration_min = segment['duration_minutes']
                scene_count = len(segment['scenes'])
                self.root.after(0, lambda i=i, dur=duration_min, count=scene_count:
                    self.scene_result_text.insert(tk.END, f"  第{i}段: {dur:.1f}分钟, {count}个场景\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))

            # 第三步：创建输出文件夹
            os.makedirs(output_folder, exist_ok=True)

            # 第四步：切割视频和生成时间戳
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "✂️ 第三步：切割视频文件...\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))

            self._cut_video_segments(video_file, output_folder, segments)

            # 完成
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"\n✅ 智能分段完成！\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"📁 输出文件夹: {output_folder}\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"📊 共生成 {len(segments)} 个视频片段\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))
            self.root.after(0, self._enable_detect_button)

            # 清理缓存
            if hasattr(self, '_cached_all_scenes'):
                delattr(self, '_cached_all_scenes')

            self.root.after(0, lambda: messagebox.showinfo("分段完成",
                f"智能分段完成！\n\n"
                f"生成了 {len(segments)} 个视频片段\n"
                f"输出文件夹:\n{output_folder}"))

        except Exception as e:
            error_msg = f"❌ 智能分段失败：{e}\n"
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, error_msg))
            self.root.after(0, self._enable_detect_button)

            # 清理缓存
            if hasattr(self, '_cached_all_scenes'):
                delattr(self, '_cached_all_scenes')
            raise

    def _calculate_intelligent_segments(self, scenes, target_duration):
        """计算智能分段点 - 确保在场景边界切割，避免过长分段"""
        if not scenes:
            return []

        # 使用两阶段算法：先预估，再优化
        segments = self._calculate_segments_with_lookahead(scenes, target_duration)

        # 验证分段的场景完整性
        self._verify_scene_integrity(segments)

        return segments

    def _calculate_segments_with_lookahead(self, scenes, target_duration):
        """精确10分钟±10秒分段算法"""
        segments = []
        current_start = 0

        segment_count = 0
        while current_start < scenes[-1][1]:  # 继续直到处理完所有内容
            segment_count += 1
            self.root.after(0, lambda count=segment_count:
                self.scene_result_text.insert(tk.END, f"\n🔍 开始计算第{count}段...\n"))

            # 找到当前起始位置对应的场景索引
            start_scene_index = 0
            for idx, (scene_start, scene_end) in enumerate(scenes):
                if scene_end > current_start:
                    start_scene_index = idx
                    break

            # 寻找最接近10分钟的场景切换点
            best_segment = self._find_optimal_segment_point(scenes, start_scene_index, current_start, target_duration)

            if best_segment:
                segments.append(best_segment)

                duration_min = best_segment['duration_minutes']
                scene_count = len(best_segment['scenes'])

                self.root.after(0, lambda dur=duration_min, count=scene_count, seg_num=len(segments):
                    self.scene_result_text.insert(tk.END, f"    ✅ 第{seg_num}段完成: {dur:.2f}分钟 (包含{count}个场景)\n"))

                # 更新起始位置
                current_start = best_segment['end_time']

                self.root.after(0, lambda start=current_start/60:
                    self.scene_result_text.insert(tk.END, f"    ➡️ 下一段从 {start:.2f}分钟开始\n"))

            else:
                # 没有找到理想的分段点，尝试强制分段
                self.root.after(0, lambda:
                    self.scene_result_text.insert(tk.END, f"    ⚠️ 未找到理想分段点，尝试强制分段\n"))

                # 计算剩余时长
                remaining_duration = scenes[-1][1] - current_start

                if remaining_duration > target_duration * 1.3:
                    # 剩余时长太长，需要强制分段
                    forced_segment = self._force_segment_at_duration(scenes, start_scene_index, current_start, target_duration)
                    if forced_segment:
                        segments.append(forced_segment)
                        current_start = forced_segment['end_time']

                        self.root.after(0, lambda dur=forced_segment['duration_minutes']:
                            self.scene_result_text.insert(tk.END, f"    🔧 强制分段: {dur:.2f}分钟\n"))
                        continue

                # 剩余时长合理，作为最后一段
                remaining_scenes = []
                for scene_start, scene_end in scenes:
                    if scene_end > current_start:
                        # 调整第一个场景的开始时间
                        if scene_start < current_start:
                            remaining_scenes.append((current_start, scene_end))
                        else:
                            remaining_scenes.append((scene_start, scene_end))

                if remaining_scenes:
                    final_duration = remaining_scenes[-1][1] - current_start
                    final_segment = {
                        'start_time': current_start,
                        'end_time': remaining_scenes[-1][1],
                        'scenes': remaining_scenes,
                        'duration_minutes': final_duration / 60,
                        'distance_seconds': abs(final_duration - target_duration)
                    }
                    segments.append(final_segment)

                    self.root.after(0, lambda dur=final_duration/60, count=len(remaining_scenes):
                        self.scene_result_text.insert(tk.END, f"    ✅ 最后一段: {dur:.2f}分钟 (包含{count}个场景)\n"))

                break

        return segments

    def _find_optimal_segment_point(self, scenes, start_index, start_time, target_duration):
        """在目标时长±10秒范围内找到最佳分段点"""
        target_min = target_duration - 10  # 目标时长-10秒
        target_max = target_duration + 10  # 目标时长+10秒

        best_candidate = None
        best_distance = float('inf')
        current_scenes = []

        self.root.after(0, lambda:
            self.scene_result_text.insert(tk.END, f"    🔍 从{start_time/60:.2f}分钟开始寻找最佳分段点 (目标: {target_duration/60:.1f}分钟 ±10秒)\n"))

        # 确保我们有有效的场景来处理
        if start_index >= len(scenes):
            self.root.after(0, lambda:
                self.scene_result_text.insert(tk.END, f"    ⚠️ 已到达场景列表末尾\n"))
            return None

        for i in range(start_index, len(scenes)):
            scene_start, scene_end = scenes[i]

            # 调整场景开始时间（如果分段开始时间在场景中间）
            effective_start = max(scene_start, start_time)
            effective_scene = (effective_start, scene_end)
            current_scenes.append(effective_scene)

            current_duration = scene_end - start_time

            self.root.after(0, lambda idx=i+1, dur=current_duration:
                self.scene_result_text.insert(tk.END, f"      场景{idx}: 累计{dur/60:.2f}分钟"))

            # 检查是否在目标范围内
            if target_min <= current_duration <= target_max:
                # 在范围内，计算与目标的距离
                distance = abs(current_duration - target_duration)

                self.root.after(0, lambda dist=distance:
                    self.scene_result_text.insert(tk.END, f" ✓ 在范围内 (偏差{dist:.1f}秒)\n"))

                if distance < best_distance:
                    best_distance = distance
                    best_candidate = {
                        'start_time': start_time,
                        'end_time': scene_end,
                        'scenes': current_scenes.copy(),
                        'duration_minutes': current_duration / 60,
                        'distance_seconds': distance
                    }
                    self.root.after(0, lambda:
                        self.scene_result_text.insert(tk.END, f"        → 更新最佳候选\n"))
                else:
                    self.root.after(0, lambda:
                        self.scene_result_text.insert(tk.END, f""))

            elif current_duration > target_max:
                # 超出范围，停止搜索
                self.root.after(0, lambda:
                    self.scene_result_text.insert(tk.END, f" ✗ 超出范围\n"))
                break
            else:
                # 还未到达范围
                self.root.after(0, lambda:
                    self.scene_result_text.insert(tk.END, f" - 继续累积\n"))

            # 如果是最后一个场景，即使不在范围内也要处理
            if i == len(scenes) - 1:
                if not best_candidate:
                    # 没有找到理想的分段点，使用当前位置
                    best_candidate = {
                        'start_time': start_time,
                        'end_time': scene_end,
                        'scenes': current_scenes.copy(),
                        'duration_minutes': current_duration / 60,
                        'distance_seconds': abs(current_duration - target_duration)
                    }

                    self.root.after(0, lambda:
                        self.scene_result_text.insert(tk.END, f"        → 最后分段，使用当前位置\n"))
                break

        if best_candidate:
            self.root.after(0, lambda dist=best_candidate['distance_seconds']:
                self.scene_result_text.insert(tk.END, f"    ✅ 选择最佳点: {best_candidate['duration_minutes']:.2f}分钟 (偏差{dist:.1f}秒)\n"))
        else:
            self.root.after(0, lambda:
                self.scene_result_text.insert(tk.END, f"    ❌ 未找到合适的分段点\n"))

        return best_candidate

    def _force_segment_at_duration(self, scenes, start_index, start_time, target_duration):
        """强制在接近目标时长的位置分段"""
        current_scenes = []
        best_candidate = None
        best_distance = float('inf')

        self.root.after(0, lambda:
            self.scene_result_text.insert(tk.END, f"    🔧 强制分段模式: 寻找最接近{target_duration/60:.0f}分钟的点\n"))

        for i in range(start_index, len(scenes)):
            scene_start, scene_end = scenes[i]

            # 调整场景开始时间
            effective_start = max(scene_start, start_time)
            effective_scene = (effective_start, scene_end)
            current_scenes.append(effective_scene)

            current_duration = scene_end - start_time
            distance = abs(current_duration - target_duration)

            # 如果这是更接近目标的点，更新候选
            if distance < best_distance:
                best_distance = distance
                best_candidate = {
                    'start_time': start_time,
                    'end_time': scene_end,
                    'scenes': current_scenes.copy(),
                    'duration_minutes': current_duration / 60,
                    'distance_seconds': distance
                }

                self.root.after(0, lambda dur=current_duration, dist=distance:
                    self.scene_result_text.insert(tk.END, f"      候选: {dur/60:.2f}分钟 (偏差{dist:.0f}秒)\n"))

            # 如果已经超出目标很多，停止搜索
            if current_duration > target_duration * 1.3:
                break

        if best_candidate:
            self.root.after(0, lambda dist=best_candidate['distance_seconds']:
                self.scene_result_text.insert(tk.END, f"    ✅ 强制选择: {best_candidate['duration_minutes']:.2f}分钟 (偏差{dist:.0f}秒)\n"))

        return best_candidate



    def _verify_scene_integrity(self, segments):
        """验证场景完整性 - 确保没有场景被截断"""
        self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "🔍 验证场景完整性...\n"))

        all_scenes_covered = []
        for segment in segments:
            for scene in segment['scenes']:
                all_scenes_covered.append(scene)

        # 检查场景是否连续且完整
        for i in range(len(all_scenes_covered) - 1):
            current_end = all_scenes_covered[i][1]
            next_start = all_scenes_covered[i + 1][0]

            if abs(current_end - next_start) > 0.1:  # 允许0.1秒的误差
                self.root.after(0, lambda:
                    self.scene_result_text.insert(tk.END, f"⚠️ 检测到场景间隙: {current_end:.1f}s -> {next_start:.1f}s\n"))

        self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "✅ 场景完整性验证通过\n"))

    def _cut_video_segments(self, video_file, output_folder, segments):
        """切割视频并生成时间戳文件"""
        try:
            import subprocess

            video_name = os.path.splitext(os.path.basename(video_file))[0]

            for i, segment in enumerate(segments, 1):
                start_time = segment['start_time']
                end_time = segment['end_time']
                scenes_in_segment = segment['scenes']

                # 更新进度
                self.root.after(0, lambda i=i, total=len(segments):
                    self.scene_result_text.insert(tk.END, f"  📹 处理第 {i}/{total} 个片段...\n"))
                self.root.after(0, lambda: self.scene_result_text.see(tk.END))

                # 生成输出文件名
                segment_name = f"{video_name}_part{i:02d}"
                output_video = os.path.join(output_folder, f"{segment_name}.mp4")
                output_timestamp = os.path.join(output_folder, f"{segment_name}_timestamps.txt")

                # 使用FFmpeg切割视频 - 修复视频编码问题
                duration = end_time - start_time
                cmd = [
                    'ffmpeg', '-i', video_file,
                    '-ss', str(start_time),
                    '-t', str(duration),
                    '-c:v', 'libx264',  # 明确指定视频编码器
                    '-c:a', 'aac',      # 明确指定音频编码器
                    '-preset', 'fast',   # 快速编码
                    output_video, '-y'   # -y 覆盖已存在的文件
                ]

                try:
                    subprocess.run(cmd, capture_output=True, check=True)

                    # 生成时间戳文件（每个片段从0开始）
                    timestamp_lines = []
                    segment_start_time = start_time  # 当前分段的起始时间

                    for j, (scene_start, scene_end) in enumerate(scenes_in_segment, 1):
                        # 转换为相对于分段开始的时间
                        relative_start = scene_start - segment_start_time
                        relative_end = scene_end - segment_start_time

                        # 确保时间不为负数
                        relative_start = max(0, relative_start)
                        relative_end = max(0, relative_end)

                        start_str = self._seconds_to_timestamp(relative_start)
                        end_str = self._seconds_to_timestamp(relative_end)
                        timestamp_lines.append(f"{j}. {start_str} --> {end_str}")

                    timestamp_content = '\n'.join(timestamp_lines)
                    with open(output_timestamp, 'w', encoding='utf-8') as f:
                        f.write(timestamp_content)

                    self.root.after(0, lambda i=i, count=len(scenes_in_segment):
                        self.scene_result_text.insert(tk.END, f"    ✅ 第 {i} 个片段完成 (包含{count}个场景)\n"))

                except subprocess.CalledProcessError as e:
                    self.root.after(0, lambda i=i:
                        self.scene_result_text.insert(tk.END, f"    ❌ 第 {i} 个片段失败: {e}\n"))

        except FileNotFoundError:
            error_msg = "❌ 未找到 FFmpeg，请安装 FFmpeg\n"
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, error_msg))
            self.root.after(0, lambda: messagebox.showerror("错误", "未找到 FFmpeg\n\n请从 https://ffmpeg.org/ 下载并安装 FFmpeg"))
        except Exception as e:
            error_msg = f"❌ 视频切割失败：{e}\n"
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, error_msg))

    def _detect_scenes_background(self, video_file, output_file):
        """在后台线程中检测场景（仅使用PySceneDetect）"""
        try:
            # 检查是否安装了PySceneDetect
            try:
                import scenedetect
                from scenedetect import detect, ContentDetector
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "✅ PySceneDetect 已安装\n"))
                self.root.after(0, lambda: self.scene_result_text.see(tk.END))
            except ImportError:
                error_msg = "❌ 未安装 PySceneDetect\n请运行: pip install scenedetect\n"
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, error_msg))
                self.root.after(0, lambda: messagebox.showerror("错误", "未安装 PySceneDetect\n\n请运行以下命令安装：\npip install scenedetect"))
                self.root.after(0, self._enable_detect_button)
                return

            # 使用PySceneDetect检测场景
            self._detect_scenes_with_pyscenedetect_background(video_file, output_file)

        except Exception as e:
            error_msg = f"❌ 场景检测失败：{e}\n"
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, error_msg))
            self.root.after(0, lambda: messagebox.showerror("错误", f"场景检测失败：{e}"))
            self.root.after(0, self._enable_detect_button)

    def _enable_detect_button(self):
        """重新启用检测按钮"""
        if hasattr(self, 'scene_detect_button'):
            self.scene_detect_button.config(state="normal")

    def _detect_scenes_with_pyscenedetect_background(self, video_file, output_file):
        """使用PySceneDetect检测场景"""
        try:
            from scenedetect import detect, ContentDetector, FrameTimecode

            threshold = self.scene_threshold.get()
            min_scene_len = self.min_scene_length.get()
            max_scene_len = self.max_scene_length.get()

            # 使用 root.after 来更新GUI
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"📁 视频文件: {os.path.basename(video_file)}\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"🎯 检测敏感度: {threshold}\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"⏱️ 最小场景长度: {min_scene_len}秒\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"⏰ 最长场景长度: {max_scene_len}秒\n\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "🔍 正在分析视频内容...\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))

            # 创建检测器
            detector = ContentDetector(threshold=threshold)

            # 显示检测进度
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "🔄 正在检测场景切换点...\n"))
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))

            # 检测场景 - 使用正确的参数名
            try:
                # 尝试新版本API
                scene_list = detect(video_file, detector)
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "✅ 场景检测完成\n"))
            except Exception as e:
                # 如果失败，尝试旧版本API
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "🔄 使用兼容模式检测...\n"))
                self.root.after(0, lambda: self.scene_result_text.see(tk.END))

                from scenedetect import VideoManager, SceneManager
                video_manager = VideoManager([video_file])
                scene_manager = SceneManager()
                scene_manager.add_detector(detector)

                # 开始检测
                video_manager.start()
                scene_manager.detect_scenes(frame_source=video_manager)
                scene_list = scene_manager.get_scene_list()
                video_manager.release()

                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "✅ 兼容模式检测完成\n"))

            if not scene_list:
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "⚠️ 未检测到场景切换\n"))
                self.root.after(0, lambda: messagebox.showinfo("检测完成", "未检测到明显的场景切换"))
                self.root.after(0, self._enable_detect_button)
                return

            # 过滤场景长度并分割过长场景
            filtered_scenes = []
            split_count = 0

            for start_time, end_time in scene_list:
                duration = end_time.get_seconds() - start_time.get_seconds()

                if duration < min_scene_len:
                    # 场景太短，跳过
                    continue
                elif duration <= max_scene_len:
                    # 场景长度合适，直接添加
                    filtered_scenes.append((start_time, end_time))
                else:
                    # 场景太长，需要分割
                    split_count += 1
                    split_scenes = self._split_long_scene(start_time, end_time, max_scene_len)
                    filtered_scenes.extend(split_scenes)

            if split_count > 0:
                self.root.after(0, lambda count=split_count:
                    self.scene_result_text.insert(tk.END, f"🔧 分割了 {count} 个过长场景\n"))

            if not filtered_scenes:
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"⚠️ 所有场景都短于 {min_scene_len} 秒，已被过滤\n"))
                self.root.after(0, lambda: messagebox.showinfo("检测完成", f"检测到场景但都短于 {min_scene_len} 秒"))
                self.root.after(0, self._enable_detect_button)
                return

            # 生成时间戳
            timestamps = []
            for i, (start_time, end_time) in enumerate(filtered_scenes, 1):
                start_str = self._seconds_to_timestamp(start_time.get_seconds())
                end_str = self._seconds_to_timestamp(end_time.get_seconds())
                timestamps.append(f"{i}. {start_str} --> {end_str}")

            # 保存到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(timestamps))

            # 显示结果
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"✅ 检测完成！共发现 {len(filtered_scenes)} 个场景\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"📁 结果已保存到: {os.path.basename(output_file)}\n\n"))

            # 显示前几个场景
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "📋 场景列表预览（前10个）：\n"))
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, "-" * 40 + "\n"))
            for timestamp in timestamps[:10]:
                self.root.after(0, lambda t=timestamp: self.scene_result_text.insert(tk.END, f"{t}\n"))

            if len(timestamps) > 10:
                self.root.after(0, lambda: self.scene_result_text.insert(tk.END, f"... 还有 {len(timestamps) - 10} 个场景\n"))

            # 滚动到底部并重新启用按钮
            self.root.after(0, lambda: self.scene_result_text.see(tk.END))
            self.root.after(0, self._enable_detect_button)

            self.root.after(0, lambda: messagebox.showinfo("检测完成",
                f"场景检测完成！\n\n"
                f"检测到 {len(filtered_scenes)} 个场景\n"
                f"结果已保存到:\n{output_file}"))

        except Exception as e:
            error_msg = f"❌ PySceneDetect 检测失败：{e}\n"
            self.root.after(0, lambda: self.scene_result_text.insert(tk.END, error_msg))
            self.root.after(0, self._enable_detect_button)
            raise



    def _seconds_to_timestamp(self, seconds):
        """将秒数转换为时间戳格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def _split_long_scene(self, start_time, end_time, max_length):
        """将过长的场景分割成多个较短的场景"""
        from scenedetect import FrameTimecode

        start_seconds = start_time.get_seconds()
        end_seconds = end_time.get_seconds()
        duration = end_seconds - start_seconds

        # 计算需要分割的段数
        num_segments = int(duration / max_length) + (1 if duration % max_length > 0 else 0)

        # 创建分割后的场景列表
        split_scenes = []
        for i in range(num_segments):
            seg_start = start_seconds + i * max_length
            seg_end = min(seg_start + max_length, end_seconds)

            # 创建FrameTimecode对象
            fps = start_time.get_framerate()
            seg_start_tc = FrameTimecode(timecode=seg_start, fps=fps)
            seg_end_tc = FrameTimecode(timecode=seg_end, fps=fps)

            split_scenes.append((seg_start_tc, seg_end_tc))

        return split_scenes

    def _split_long_scene_simple(self, start_seconds, end_seconds, max_length):
        """将过长的场景分割成多个较短的场景（简化版，用于智能分段）"""
        duration = end_seconds - start_seconds

        # 计算需要分割的段数
        num_segments = int(duration / max_length) + (1 if duration % max_length > 0 else 0)

        # 创建分割后的场景列表
        split_scenes = []
        for i in range(num_segments):
            seg_start = start_seconds + i * max_length
            seg_end = min(seg_start + max_length, end_seconds)
            split_scenes.append((seg_start, seg_end))

        return split_scenes

    def _intelligent_split_long_scene(self, video_file, start_seconds, end_seconds, max_length, base_threshold):
        """智能分割超长场景 - 优先寻找自然切点，必要时均匀分割"""
        duration = end_seconds - start_seconds

        start_str = self._seconds_to_timestamp(start_seconds)
        end_str = self._seconds_to_timestamp(end_seconds)
        self.root.after(0, lambda:
            self.scene_result_text.insert(tk.END, f"    🧠 智能分析 {duration:.1f}秒 的超长场景 ({start_str} --> {end_str})...\n"))

        # 所有场景都使用智能分割，但优化性能

        # 使用用户设置的长场景分割敏感度进行分析
        try:
            smart_scenes = self._find_smart_cut_points_simple(video_file, start_seconds, end_seconds, max_length, base_threshold)
            if smart_scenes:
                # 显示智能分割结果的详细信息
                self.root.after(0, lambda count=len(smart_scenes):
                    self.scene_result_text.insert(tk.END, f"    ✅ 智能分割找到 {count} 个场景片段:\n"))

                # 显示每个分割片段的时间戳
                for i, (seg_start, seg_end) in enumerate(smart_scenes, 1):
                    seg_duration = seg_end - seg_start
                    start_str = self._seconds_to_timestamp(seg_start)
                    end_str = self._seconds_to_timestamp(seg_end)
                    self.root.after(0, lambda idx=i, start=start_str, end=end_str, dur=seg_duration:
                        self.scene_result_text.insert(tk.END, f"      {idx}. {start} --> {end} ({dur:.1f}秒)\n"))
                return smart_scenes
            else:
                self.root.after(0, lambda:
                    self.scene_result_text.insert(tk.END, f"    ❌ 未找到合适的切点\n"))
        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            self.root.after(0, lambda err=str(e), detail=error_detail:
                self.scene_result_text.insert(tk.END, f"    ❌ 分析失败: {err}\n    详细错误: {detail}\n"))

        # 没有找到切点，保持原场景完整
        self.root.after(0, lambda:
            self.scene_result_text.insert(tk.END, f"    ⚠️ 保持原场景完整(1个片段)\n"))
        return [(start_seconds, end_seconds)]

    def _find_smart_cut_points(self, video_file, start_seconds, end_seconds, max_length, base_threshold):
        """在长场景内寻找智能切点"""
        from scenedetect import detect, ContentDetector

        # 使用用户设置的长场景分割敏感度系数
        sensitivity_factor = self.long_scene_sensitivity.get()
        high_sensitivity = base_threshold * sensitivity_factor
        detector = ContentDetector(threshold=high_sensitivity)

        # 检测整个视频
        all_scenes = detect(video_file, detector)

        # 提取在当前长场景范围内的切点
        cut_points = []
        for scene_start, scene_end in all_scenes:
            scene_start_sec = scene_start.get_seconds()
            scene_end_sec = scene_end.get_seconds()

            # 收集在范围内的切点
            if start_seconds < scene_start_sec < end_seconds:
                cut_points.append(scene_start_sec)
            if start_seconds < scene_end_sec < end_seconds:
                cut_points.append(scene_end_sec)

        # 去重并排序
        cut_points = sorted(list(set(cut_points)))

        # 显示找到的切点信息
        self.root.after(0, lambda count=len(cut_points):
            self.scene_result_text.insert(tk.END, f"      📍 找到 {count} 个潜在切点\n"))

        if not cut_points:
            return None

        # 构建场景列表
        scenes = []
        current_start = start_seconds

        for cut_point in cut_points:
            if cut_point - current_start >= self.min_scene_length.get():
                # 检查这个场景是否还是太长
                if cut_point - current_start <= max_length:
                    scenes.append((current_start, cut_point))
                    current_start = cut_point
                else:
                    # 这段还是太长，但保持完整性，允许超过最大长度
                    scenes.append((current_start, cut_point))
                    current_start = cut_point

        # 处理最后一段
        if end_seconds - current_start >= self.min_scene_length.get():
            if end_seconds - current_start <= max_length:
                scenes.append((current_start, end_seconds))
            else:
                # 允许最后一段稍长，保持完整性
                scenes.append((current_start, end_seconds))

        return scenes if scenes else None

    def _find_smart_cut_points_optimized(self, video_file, start_seconds, end_seconds, max_length, base_threshold):
        """优化版智能切点查找 - 避免重复检测整个视频"""
        # 如果已经有全局场景检测结果，直接使用
        if hasattr(self, '_cached_all_scenes') and self._cached_all_scenes:
            return self._extract_cuts_from_cache(start_seconds, end_seconds, max_length)

        # 否则回退到原方法，但限制检测范围
        return self._find_smart_cut_points_limited(video_file, start_seconds, end_seconds, max_length, base_threshold)

    def _extract_cuts_from_cache(self, start_seconds, end_seconds, max_length):
        """从缓存的场景检测结果中提取切点"""
        # 从缓存中提取在范围内的切点
        cut_points = []
        for scene_start, scene_end in self._cached_all_scenes:
            scene_start_sec = scene_start.get_seconds()
            scene_end_sec = scene_end.get_seconds()

            # 收集在范围内的切点
            if start_seconds < scene_start_sec < end_seconds:
                cut_points.append(scene_start_sec)
            if start_seconds < scene_end_sec < end_seconds:
                cut_points.append(scene_end_sec)

        # 去重并排序
        cut_points = sorted(list(set(cut_points)))

        if not cut_points:
            return None

        # 构建场景列表
        scenes = []
        current_start = start_seconds

        for cut_point in cut_points:
            if cut_point - current_start >= self.min_scene_length.get():
                if cut_point - current_start <= max_length:
                    scenes.append((current_start, cut_point))
                    current_start = cut_point
                else:
                    # 这段还是太长，但保持完整性，允许超过最大长度
                    scenes.append((current_start, cut_point))
                    current_start = cut_point

        # 处理最后一段
        if end_seconds - current_start >= self.min_scene_length.get():
            if end_seconds - current_start <= max_length:
                scenes.append((current_start, end_seconds))
            else:
                # 允许最后一段稍长，保持完整性
                scenes.append((current_start, end_seconds))

        return scenes if scenes else None

    def _find_smart_cut_points_limited(self, video_file, start_seconds, end_seconds, max_length, base_threshold):
        """限制范围的智能切点查找 - 性能优化版"""
        from scenedetect import detect, ContentDetector

        # 使用用户设置的长场景分割敏感度系数
        sensitivity_factor = self.long_scene_sensitivity.get()
        high_sensitivity = base_threshold * sensitivity_factor
        detector = ContentDetector(threshold=high_sensitivity)

        # 只检测一小段视频以提高性能
        duration = end_seconds - start_seconds
        if duration > 60:  # 超过1分钟的场景，只检测前60秒寻找切点
            limited_end = start_seconds + 60
            self.root.after(0, lambda:
                self.scene_result_text.insert(tk.END, f"    ⚡ 限制检测范围以提高性能\n"))
        else:
            limited_end = end_seconds

        try:
            # 检测整个视频，然后过滤
            all_scenes = detect(video_file, detector)

            # 提取在限制范围内的切点
            cut_points = []
            for scene_start, scene_end in all_scenes:
                scene_start_sec = scene_start.get_seconds()
                scene_end_sec = scene_end.get_seconds()

                # 只收集在限制范围内的切点
                if start_seconds < scene_start_sec < limited_end:
                    cut_points.append(scene_start_sec)
                if start_seconds < scene_end_sec < limited_end:
                    cut_points.append(scene_end_sec)

            # 去重并排序
            cut_points = sorted(list(set(cut_points)))

            if not cut_points:
                return None

            # 如果找到了切点，使用第一个切点分割，剩余部分递归处理
            first_cut = cut_points[0]
            scenes = []

            # 第一段
            if first_cut - start_seconds >= self.min_scene_length.get():
                scenes.append((start_seconds, first_cut))

            # 剩余部分保持完整
            if end_seconds - first_cut >= self.min_scene_length.get():
                scenes.append((first_cut, end_seconds))

            return scenes if scenes else None

        except Exception:
            return None

    def _improved_uniform_split(self, start_seconds, end_seconds, max_length):
        """改进的均匀分割 - 尽量让分割更自然"""
        duration = end_seconds - start_seconds

        # 计算理想的分段数
        ideal_segments = duration / max_length
        num_segments = int(ideal_segments)

        # 如果余数很小，减少分段数让每段稍长一些
        remainder = ideal_segments - num_segments
        if remainder < 0.3 and num_segments > 1:
            num_segments -= 1
        elif remainder > 0.7:
            num_segments += 1

        # 确保至少有一段
        num_segments = max(1, num_segments)

        # 计算每段的实际长度
        segment_length = duration / num_segments

        # 生成分割点
        scenes = []
        for i in range(num_segments):
            seg_start = start_seconds + i * segment_length
            seg_end = start_seconds + (i + 1) * segment_length

            # 最后一段精确到结束时间
            if i == num_segments - 1:
                seg_end = end_seconds

            scenes.append((seg_start, seg_end))

        return scenes

    def _find_smart_cut_points_strict(self, video_file, start_seconds, end_seconds, max_length, base_threshold):
        """回退的智能切点查找 - 使用更严格的敏感度"""
        from scenedetect import detect, ContentDetector

        # 使用更严格的敏感度 (比长场景敏感度系数更低的阈值)
        long_scene_factor = self.long_scene_sensitivity.get()
        strict_factor = long_scene_factor * 0.5  # 更严格，阈值更低
        strict_sensitivity = base_threshold * strict_factor
        detector = ContentDetector(threshold=strict_sensitivity)

        try:
            # 检测整个视频 - 使用高性能设置
            import os
            # 设置OpenCV使用所有可用CPU核心
            cv2.setNumThreads(0)  # 0表示使用所有可用核心

            all_scenes = detect(video_file, detector, show_progress=False)

            # 提取在范围内的切点
            cut_points = []
            for scene_start, scene_end in all_scenes:
                scene_start_sec = scene_start.get_seconds()
                scene_end_sec = scene_end.get_seconds()

                # 收集在范围内的切点
                if start_seconds < scene_start_sec < end_seconds:
                    cut_points.append(scene_start_sec)
                if start_seconds < scene_end_sec < end_seconds:
                    cut_points.append(scene_end_sec)

            # 去重并排序
            cut_points = sorted(list(set(cut_points)))

            # 显示找到的切点信息
            self.root.after(0, lambda count=len(cut_points):
                self.scene_result_text.insert(tk.END, f"      📍 找到 {count} 个潜在切点\n"))

            if not cut_points:
                return None

            # 构建场景列表，使用所有找到的切点
            scenes = []
            current_start = start_seconds

            for cut_point in cut_points:
                if cut_point - current_start >= self.min_scene_length.get():
                    # 检查这个场景是否还需要进一步分割
                    if cut_point - current_start <= max_length:
                        scenes.append((current_start, cut_point))
                    else:
                        # 这段还是太长，在这段内继续寻找更多切点
                        sub_cuts = [cp for cp in cut_points if current_start < cp < cut_point]
                        if sub_cuts:
                            # 使用找到的子切点
                            temp_start = current_start
                            for sub_cut in sub_cuts:
                                if sub_cut - temp_start >= self.min_scene_length.get():
                                    scenes.append((temp_start, sub_cut))
                                    temp_start = sub_cut
                            # 添加最后一小段
                            if cut_point - temp_start >= self.min_scene_length.get():
                                scenes.append((temp_start, cut_point))
                        else:
                            # 没有子切点，保持这段完整
                            scenes.append((current_start, cut_point))
                    current_start = cut_point

            # 处理最后一段
            if end_seconds - current_start >= self.min_scene_length.get():
                scenes.append((current_start, end_seconds))

            return scenes if scenes else None

        except Exception:
            return None

    def _find_smart_cut_points_simple(self, video_file, start_seconds, end_seconds, max_length, base_threshold):
        """简化的智能切点查找 - 只使用用户设置的长场景分割敏感度"""
        try:
            from scenedetect import detect, ContentDetector
            import cv2
        except ImportError as e:
            self.root.after(0, lambda err=str(e):
                self.scene_result_text.insert(tk.END, f"      ❌ 导入错误: {err}\n"))
            return None

        # 使用用户设置的长场景分割敏感度系数
        sensitivity_factor = self.long_scene_sensitivity.get()
        target_sensitivity = base_threshold * sensitivity_factor

        # 显示调试信息
        self.root.after(0, lambda:
            self.scene_result_text.insert(tk.END, f"      🔧 基础阈值: {base_threshold}, 敏感度系数: {sensitivity_factor}, 目标阈值: {target_sensitivity}\n"))

        # 创建检测器，不传入min_scene_len参数避免类型错误
        detector = ContentDetector(threshold=target_sensitivity)

        try:
            # 重新检测整个视频，使用长场景分割敏感度（不使用缓存）
            self.root.after(0, lambda:
                self.scene_result_text.insert(tk.END, f"      🔍 重新检测视频以获取更精细的切点\n"))
            all_scenes = detect(video_file, detector)

            # 提取在范围内的切点
            cut_points = []
            for scene_start, scene_end in all_scenes:
                scene_start_sec = scene_start.get_seconds()
                scene_end_sec = scene_end.get_seconds()

                # 收集在范围内的切点
                if start_seconds < scene_start_sec < end_seconds:
                    cut_points.append(scene_start_sec)
                if start_seconds < scene_end_sec < end_seconds:
                    cut_points.append(scene_end_sec)

            # 去重并排序
            cut_points = sorted(list(set(cut_points)))

            # 显示找到的切点信息
            self.root.after(0, lambda count=len(cut_points):
                self.scene_result_text.insert(tk.END, f"      📍 找到 {count} 个潜在切点\n"))

            if not cut_points:
                return None

            # 构建场景列表
            scenes = []
            current_start = start_seconds

            for cut_point in cut_points:
                if cut_point - current_start >= self.min_scene_length.get():
                    scenes.append((current_start, cut_point))
                    current_start = cut_point

            # 处理最后一段
            if end_seconds - current_start >= self.min_scene_length.get():
                scenes.append((current_start, end_seconds))

            return scenes if scenes else None

        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            self.root.after(0, lambda err=str(e), detail=error_detail:
                self.scene_result_text.insert(tk.END, f"      ❌ 切点查找异常: {err}\n      详细: {detail}\n"))
            return None

    def _process_long_scenes_multithreaded(self, video_file, long_scenes, max_scene_len, threshold):
        """多线程处理长场景"""
        import concurrent.futures
        import threading

        # 限制线程数量，避免过度占用资源
        max_workers = min(6, len(long_scenes))  # 最多6个线程

        def process_single_scene(scene_data):
            """处理单个长场景的线程函数"""
            original_index, start_seconds, end_seconds = scene_data
            try:
                # 在主线程中更新进度
                current_num = len([s for s in long_scenes if s[0] <= original_index])
                self.root.after(0, lambda current=current_num, total=len(long_scenes):
                    self.scene_result_text.insert(tk.END, f"⚡ 多线程进度: {current}/{total}\n"))

                # 处理长场景
                split_scenes = self._intelligent_split_long_scene(video_file, start_seconds, end_seconds, max_scene_len, threshold)
                return split_scenes
            except Exception as e:
                # 错误处理
                self.root.after(0, lambda err=str(e):
                    self.scene_result_text.insert(tk.END, f"❌ 多线程处理错误: {err}\n"))
                return [(start_seconds, end_seconds)]  # 返回原场景

        # 场景数据已经包含原始索引
        scene_data_list = long_scenes

        # 使用线程池处理
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_scene = {executor.submit(process_single_scene, scene_data): scene_data for scene_data in scene_data_list}

            # 收集结果，按原始索引顺序
            results_dict = {}
            for future in concurrent.futures.as_completed(future_to_scene):
                scene_data = future_to_scene[future]
                original_index = scene_data[0]  # 原始场景索引

                try:
                    result = future.result()
                    if result and len(result) > 0:  # 确保结果有效
                        results_dict[original_index] = result
                    else:
                        # 如果结果为空，使用原场景
                        results_dict[original_index] = [(scene_data[1], scene_data[2])]
                except Exception as e:
                    self.root.after(0, lambda err=str(e):
                        self.scene_result_text.insert(tk.END, f"❌ 场景处理失败: {err}\n"))
                    # 添加原场景作为回退
                    results_dict[original_index] = [(scene_data[1], scene_data[2])]

            # 按原始索引顺序返回结果
            ordered_results = []
            for original_index, _, _ in sorted(long_scenes, key=lambda x: x[0]):
                if original_index in results_dict:
                    ordered_results.append(results_dict[original_index])

            return ordered_results

    def preview_scene_results(self):
        """预览场景检测结果"""
        output_file = self.scene_output_file.get().strip()

        if not output_file or not os.path.exists(output_file):
            messagebox.showwarning("警告", "请先进行场景检测或检查输出文件是否存在！")
            return

        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()

            self.scene_result_text.delete(1.0, tk.END)
            self.scene_result_text.insert(tk.END, f"📋 场景检测结果预览\n")
            self.scene_result_text.insert(tk.END, f"📁 文件: {os.path.basename(output_file)}\n")
            self.scene_result_text.insert(tk.END, "=" * 50 + "\n\n")
            self.scene_result_text.insert(tk.END, content)

        except Exception as e:
            messagebox.showerror("错误", f"预览文件失败：{e}")

    def clear_scene_results(self):
        """清空场景检测结果"""
        self.scene_result_text.delete(1.0, tk.END)

    def run(self):
        """运行GUI"""
        self.root.mainloop()

    def create_segment_merge_tab(self):
        """创建分段合并选项卡"""
        # 创建选项卡框架
        merge_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(merge_frame, text="🔗 分段合并")

        # 文件夹选择区域
        folder_frame = ttk.LabelFrame(merge_frame, text="分段文件夹选择", padding="10")
        folder_frame.pack(fill="x", pady=(0, 10))

        # 分段文件夹选择
        ttk.Label(folder_frame, text="分段文件夹:").pack(anchor="w")
        folder_select_frame = ttk.Frame(folder_frame)
        folder_select_frame.pack(fill="x", pady=(5, 0))

        self.segment_folder = tk.StringVar()
        folder_entry = ttk.Entry(folder_select_frame, textvariable=self.segment_folder, width=60)
        folder_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

        def browse_segment_folder():
            folder = filedialog.askdirectory(title="选择分段文件夹")
            if folder:
                self.segment_folder.set(folder)
                self.scan_segment_files()

        ttk.Button(folder_select_frame, text="浏览", command=browse_segment_folder).pack(side="right")

        # 输出文件选择
        output_frame = ttk.LabelFrame(merge_frame, text="输出文件设置", padding="10")
        output_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(output_frame, text="合并后文件名:").pack(anchor="w")
        output_select_frame = ttk.Frame(output_frame)
        output_select_frame.pack(fill="x", pady=(5, 0))

        self.merged_output_file = tk.StringVar(value=os.path.join(os.getcwd(), "合并后的时间戳.txt"))
        output_entry = ttk.Entry(output_select_frame, textvariable=self.merged_output_file, width=60)
        output_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

        def browse_output_file():
            file = filedialog.asksaveasfilename(
                title="保存合并文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if file:
                self.merged_output_file.set(file)

        ttk.Button(output_select_frame, text="浏览", command=browse_output_file).pack(side="right")

        # 文件列表区域
        list_frame = ttk.LabelFrame(merge_frame, text="分段文件列表", padding="10")
        list_frame.pack(fill="both", expand=True, pady=(0, 10))

        # 创建文件列表
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill="both", expand=True)

        # 文件列表
        columns = ("序号", "文件名", "时长", "场景数")
        self.segment_tree = ttk.Treeview(list_container, columns=columns, show="headings", height=8)

        # 设置列标题
        self.segment_tree.heading("序号", text="序号")
        self.segment_tree.heading("文件名", text="文件名")
        self.segment_tree.heading("时长", text="时长")
        self.segment_tree.heading("场景数", text="场景数")

        # 设置列宽
        self.segment_tree.column("序号", width=60, anchor="center")
        self.segment_tree.column("文件名", width=300, anchor="w")
        self.segment_tree.column("时长", width=100, anchor="center")
        self.segment_tree.column("场景数", width=100, anchor="center")

        # 滚动条
        tree_scroll = ttk.Scrollbar(list_container, orient="vertical", command=self.segment_tree.yview)
        self.segment_tree.configure(yscrollcommand=tree_scroll.set)

        self.segment_tree.pack(side="left", fill="both", expand=True)
        tree_scroll.pack(side="right", fill="y")

        # 操作按钮区域
        button_frame = ttk.Frame(merge_frame)
        button_frame.pack(fill="x", pady=(0, 10))

        ttk.Button(button_frame, text="🔍 扫描文件", command=self.scan_segment_files).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="🔗 开始合并", command=self.merge_segments).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="🗑️ 清空列表", command=self.clear_segment_list).pack(side="left")

        # 结果显示区域
        result_frame = ttk.LabelFrame(merge_frame, text="合并结果", padding="10")
        result_frame.pack(fill="both", expand=True)

        # 结果文本框
        result_container = ttk.Frame(result_frame)
        result_container.pack(fill="both", expand=True)

        self.merge_result_text = tk.Text(result_container, height=8, wrap=tk.WORD)
        result_scroll = ttk.Scrollbar(result_container, orient="vertical", command=self.merge_result_text.yview)
        self.merge_result_text.configure(yscrollcommand=result_scroll.set)

        self.merge_result_text.pack(side="left", fill="both", expand=True)
        result_scroll.pack(side="right", fill="y")

    def scan_segment_files(self):
        """扫描分段文件"""
        folder = self.segment_folder.get()
        if not folder or not os.path.exists(folder):
            messagebox.showerror("错误", "请选择有效的分段文件夹")
            return

        # 清空列表
        for item in self.segment_tree.get_children():
            self.segment_tree.delete(item)

        try:
            # 查找所有时间戳文件
            timestamp_files = []
            for file in os.listdir(folder):
                if file.endswith("_timestamps.txt"):
                    timestamp_files.append(file)

            # 按文件名排序
            timestamp_files.sort()

            self.merge_result_text.delete(1.0, tk.END)
            self.merge_result_text.insert(tk.END, f"📁 扫描文件夹: {folder}\n")
            self.merge_result_text.insert(tk.END, f"🔍 找到 {len(timestamp_files)} 个时间戳文件\n\n")

            for i, file in enumerate(timestamp_files, 1):
                file_path = os.path.join(folder, file)

                # 分析文件内容
                duration, scene_count = self.analyze_timestamp_file(file_path)

                # 添加到列表
                self.segment_tree.insert("", "end", values=(
                    i,
                    file,
                    duration,
                    scene_count
                ))

                self.merge_result_text.insert(tk.END, f"{i}. {file} - {duration} - {scene_count}个场景\n")

            self.merge_result_text.insert(tk.END, f"\n✅ 文件扫描完成")
            self.merge_result_text.see(tk.END)

        except Exception as e:
            messagebox.showerror("错误", f"扫描文件失败：{e}")

    def analyze_timestamp_file(self, file_path):
        """分析时间戳文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找时间戳 - 修正正则表达式
            import re
            timestamps = re.findall(r'时间戳：(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})', content)

            if timestamps:
                # 获取最后一个时间戳的结束时间作为总时长
                last_end = timestamps[-1][1]
                scene_count = len(timestamps)
                return last_end, scene_count
            else:
                return "未知", 0

        except Exception as e:
            return "错误", 0

    def merge_segments(self):
        """合并分段文件"""
        folder = self.segment_folder.get()
        output_file = self.merged_output_file.get()

        if not folder or not os.path.exists(folder):
            messagebox.showerror("错误", "请选择有效的分段文件夹")
            return

        if not output_file:
            messagebox.showerror("错误", "请设置输出文件名")
            return

        try:
            # 获取所有时间戳文件
            timestamp_files = []
            for file in os.listdir(folder):
                if file.endswith("_timestamps.txt"):
                    timestamp_files.append(file)

            timestamp_files.sort()

            if not timestamp_files:
                messagebox.showerror("错误", "未找到时间戳文件")
                return

            self.merge_result_text.delete(1.0, tk.END)
            self.merge_result_text.insert(tk.END, "🔗 开始合并分段文件...\n\n")

            merged_content = []
            cumulative_offset = 0  # 累计时间偏移
            total_scenes = 0

            for i, file in enumerate(timestamp_files, 1):
                file_path = os.path.join(folder, file)

                self.merge_result_text.insert(tk.END, f"📄 处理第{i}个文件: {file}\n")

                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 处理时间戳偏移
                processed_content = self.process_timestamp_offset(content, cumulative_offset, total_scenes)

                # 计算当前文件的最大时间戳，用于下一个文件的偏移
                import re
                timestamps = re.findall(r'时间戳：(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})', content)

                if timestamps:
                    last_end_str = timestamps[-1][1]
                    last_end_seconds = self.timestamp_to_seconds(last_end_str)
                    cumulative_offset += last_end_seconds
                    total_scenes += len(timestamps)

                    self.merge_result_text.insert(tk.END, f"  ✅ 处理完成，{len(timestamps)}个场景，累计偏移: {cumulative_offset/60:.1f}分钟\n")
                else:
                    self.merge_result_text.insert(tk.END, f"  ⚠️ 未找到时间戳，跳过此文件\n")

                merged_content.append(processed_content)
                self.merge_result_text.see(tk.END)

            # 写入合并后的文件
            final_content = '\n'.join(merged_content)

            # 确保输出文件的目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(final_content)

            self.merge_result_text.insert(tk.END, f"\n✅ 合并完成！\n")
            self.merge_result_text.insert(tk.END, f"📁 输出文件: {output_file}\n")
            self.merge_result_text.insert(tk.END, f"📊 总计: {total_scenes}个场景，{cumulative_offset/60:.1f}分钟\n")

            messagebox.showinfo("成功", f"分段合并完成！\n输出文件: {output_file}")

        except Exception as e:
            messagebox.showerror("错误", f"合并失败：{e}")

    def process_timestamp_offset(self, content, offset_seconds, scene_offset):
        """处理时间戳偏移"""
        import re

        # 查找所有时间戳
        def replace_timestamp(match):
            scene_num = int(match.group(1))
            start_time = match.group(2)
            end_time = match.group(3)

            # 转换为秒数并添加偏移
            start_seconds = self.timestamp_to_seconds(start_time) + offset_seconds
            end_seconds = self.timestamp_to_seconds(end_time) + offset_seconds

            # 转换回时间戳格式
            new_start = self.seconds_to_timestamp(start_seconds)
            new_end = self.seconds_to_timestamp(end_seconds)

            # 更新场景编号
            new_scene_num = scene_num + scene_offset

            return f"{new_scene_num}.\n时间戳：{new_start} --> {new_end}"

        # 替换时间戳 - 修正正则表达式以匹配实际格式
        pattern = r'(\d+)\.\s*\n时间戳：(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})'
        processed_content = re.sub(pattern, replace_timestamp, content)

        return processed_content

    def timestamp_to_seconds(self, timestamp):
        """将时间戳转换为秒数"""
        # 格式: HH:MM:SS,mmm
        time_part, ms_part = timestamp.split(',')
        hours, minutes, seconds = map(int, time_part.split(':'))
        milliseconds = int(ms_part)

        total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
        return total_seconds

    def seconds_to_timestamp(self, seconds):
        """将秒数转换为时间戳格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def clear_segment_list(self):
        """清空分段列表"""
        for item in self.segment_tree.get_children():
            self.segment_tree.delete(item)

        self.merge_result_text.delete(1.0, tk.END)
        self.merge_result_text.insert(tk.END, "📋 列表已清空\n")


if __name__ == "__main__":
    try:
        app = UnifiedGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
