#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试智能字幕匹配修复
"""

from subtitle_matcher import SubtitleMatcher

def test_subtitle_matching_fix():
    """测试修复后的智能字幕匹配功能"""
    print("=" * 60)
    print("测试修复后的智能字幕匹配功能")
    print("=" * 60)
    
    # 创建匹配器
    matcher = SubtitleMatcher()
    
    # 文件路径
    file_a_path = "D:/二次剪辑与原视频匹配/二次剪辑素材/二次剪辑.txt"
    file_b_path = "D:/二次剪辑与原视频匹配/原视频匹配素材/原素材.txt"
    
    print(f"📁 文件A路径: {file_a_path}")
    print(f"📁 文件B路径: {file_b_path}")
    
    # 检查文件是否存在
    import os
    if not os.path.exists(file_a_path):
        print("❌ 文件A不存在")
        return False
    
    if not os.path.exists(file_b_path):
        print("❌ 文件B不存在")
        return False
    
    print("✅ 文件检查通过")
    
    # 执行匹配
    try:
        success, timestamps, error_msg = matcher.process_files(
            file_a_path, 
            file_b_path, 
            auto_save=False,  # 不自动保存，避免覆盖
            max_time_gap=30.0
        )
        
        if success:
            print(f"\n✅ 匹配成功！")
            print(f"📊 生成时间戳数量: {len(timestamps)}")
            
            # 显示前几个时间戳
            print(f"\n📋 时间戳预览（前5个）:")
            for i, timestamp in enumerate(timestamps[:5], 1):
                print(f"  {i}. {timestamp}")
            
            if len(timestamps) > 5:
                print(f"  ... 还有 {len(timestamps) - 5} 个时间戳")
            
            # 分析匹配统计
            print(f"\n📊 匹配统计:")
            print(f"  文件A台词数: {len(matcher.file_a_entries)}")
            print(f"  文件B镜头数: {len(matcher.file_b_entries)}")
            print(f"  匹配结果数: {len(matcher.match_results)}")
            
            # 计算匹配成功率
            successful_matches = sum(1 for result in matcher.match_results if result.matched_sequences)
            match_rate = (successful_matches / len(matcher.file_a_entries)) * 100 if matcher.file_a_entries else 0
            print(f"  匹配成功率: {match_rate:.1f}%")
            
            return True
            
        else:
            print(f"❌ 匹配失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ 匹配过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_block_expansion_fix():
    """测试区块扩展修复"""
    print("\n" + "=" * 60)
    print("测试区块扩展修复")
    print("=" * 60)
    
    # 创建匹配器
    matcher = SubtitleMatcher()
    
    # 模拟一些file_b_entries（原始素材）
    from subtitle_matcher import SubtitleEntry
    
    # 模拟原始素材中的序号（有间隔）
    test_sequences = [1, 2, 3, 5, 6, 7, 10, 11, 12, 20, 21, 22]
    
    for seq in test_sequences:
        entry = SubtitleEntry(
            sequence=seq,
            start_time=f"00:00:{seq:02d},000",
            end_time=f"00:00:{seq:02d},500",
            text=f"测试台词{seq}",
            normalized_text=f"测试台词{seq}"
        )
        matcher.file_b_entries[seq] = entry
    
    print(f"📊 模拟原始素材序号: {sorted(matcher.file_b_entries.keys())}")
    
    # 模拟匹配结果
    from subtitle_matcher import MatchResult
    matcher.match_results = [
        MatchResult("台词1", [1, 2, 3]),
        MatchResult("台词2", [5, 6, 7]),
        MatchResult("台词3", [20, 21, 22])
    ]
    
    print(f"📊 模拟匹配结果: {[result.matched_sequences for result in matcher.match_results]}")
    
    # 测试区块创建和扩展
    blocks = matcher.create_blocks_and_expand(max_time_gap=30.0)
    
    print(f"\n📊 区块划分结果:")
    for i, (min_seq, max_seq) in enumerate(blocks, 1):
        print(f"  区块{i}: {min_seq}-{max_seq}")
        
        # 检查序号是否存在
        min_exists = min_seq in matcher.file_b_entries
        max_exists = max_seq in matcher.file_b_entries
        
        print(f"    序号{min_seq}存在: {'✅' if min_exists else '❌'}")
        print(f"    序号{max_seq}存在: {'✅' if max_exists else '❌'}")
    
    # 测试时间戳生成
    timestamps = matcher.generate_final_timestamps(max_time_gap=30.0)
    
    print(f"\n📊 时间戳生成结果:")
    print(f"  区块数量: {len(blocks)}")
    print(f"  时间戳数量: {len(timestamps)}")
    print(f"  成功率: {len(timestamps)/len(blocks)*100:.1f}%")
    
    for i, timestamp in enumerate(timestamps, 1):
        print(f"  {i}. {timestamp}")
    
    # 检查是否还有失败的区块
    failed_blocks = len(blocks) - len(timestamps)
    if failed_blocks == 0:
        print("✅ 所有区块都成功生成时间戳")
        return True
    else:
        print(f"❌ 仍有 {failed_blocks} 个区块生成时间戳失败")
        return False

def compare_before_after():
    """对比修复前后的差异"""
    print("\n" + "=" * 60)
    print("对比修复前后的差异")
    print("=" * 60)
    
    print("📋 修复前的问题:")
    print("  1. 区块扩展时不检查序号是否存在")
    print("  2. 扩展后的序号可能在原始素材中不存在")
    print("  3. 导致'找不到序号的开始时间'或'结束时间'错误")
    print("  4. 最终生成的时间戳数量少于区块数量")
    
    print("\n📋 修复后的改进:")
    print("  1. 扩展前检查序号是否在原始素材中存在")
    print("  2. 如果扩展后的序号不存在，使用原始范围")
    print("  3. 确保所有区块的序号都在原始素材中存在")
    print("  4. 提高时间戳生成的成功率")
    
    print("\n📋 具体修复逻辑:")
    print("  - 扩展前: expanded_min = max(1, min_seq - 1)")
    print("  - 扩展后: 检查 expanded_min 是否在 file_b_entries 中")
    print("  - 如果不存在: 向上调整到最近的存在序号")
    print("  - 如果扩展失败: 使用原始范围，避免错误")

def main():
    """主测试函数"""
    print("🧪 智能字幕匹配修复验证")
    print("=" * 60)
    
    # 测试区块扩展修复
    block_fix_success = test_block_expansion_fix()
    
    # 对比修复前后
    compare_before_after()
    
    # 测试完整匹配功能
    matching_success = test_subtitle_matching_fix()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 区块扩展修复: {'通过' if block_fix_success else '失败'}")
    print(f"✅ 完整匹配功能: {'通过' if matching_success else '失败'}")
    
    if block_fix_success and matching_success:
        print("\n🎉 智能字幕匹配修复成功！")
        print("📋 修复效果:")
        print("1. 解决了区块扩展导致的序号越界问题")
        print("2. 确保所有扩展后的序号都在原始素材中存在")
        print("3. 提高了时间戳生成的成功率")
        print("4. 减少了'找不到序号'的错误")
        print("5. 现在应该能生成更多有效的时间戳")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
