#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时间戳提取器 - 提取两个轨道分割片段的时间戳信息

功能：
1. 从特征缓存中读取片段信息
2. 提取每个片段的开始时间、结束时间、时长
3. 生成详细的时间戳报告
4. 支持多种导出格式（TXT、CSV、JSON）
5. 统计分析片段分布情况
6. 从剪映项目提取时间戳
"""

import os
import json
import re
import logging
from typing import List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SegmentTimestamp:
    """片段时间戳信息"""
    file_name: str
    sequence_number: int
    start_time: float
    end_time: float
    duration: float
    track_name: str
    
    def format_time(self, seconds: float) -> str:
        """格式化时间为可读格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        if hours > 0:
            return f"{hours}:{minutes:02d}:{secs:06.3f}"
        else:
            return f"{minutes}:{secs:06.3f}"
    
    def __str__(self) -> str:
        return f"[{self.track_name}] 片段{self.sequence_number:03d}: {self.format_time(self.start_time)} - {self.format_time(self.end_time)} (时长: {self.format_time(self.duration)})"

class TimestampExtractor:
    """时间戳提取器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_timestamps(self, project_path: str = None, draft_path: str = None) -> Tuple[List[SegmentTimestamp], List[SegmentTimestamp]]:
        """提取时间戳主入口 - 完全按照原项目逻辑"""
        print("⏰ 时间戳提取器启动")
        print("📊 提取两个轨道分割片段的时间戳信息")
        print("=" * 60)

        try:
            # 提取主轨道时间戳
            main_track_timestamps = self._extract_track_timestamps("feature_cache/main_track", "主轨道")

            # 提取第二轨道时间戳
            second_track_timestamps = self._extract_track_timestamps("feature_cache/second_track", "第二轨道")

            if not main_track_timestamps and not second_track_timestamps:
                print("⚠️ 特征文件不存在，尝试从剪映项目提取时间戳...")
                return self._extract_from_jianying_project(draft_path)

            # 显示统计信息
            self._display_statistics(main_track_timestamps, second_track_timestamps)

            # 生成报告
            self._generate_reports(main_track_timestamps, second_track_timestamps)

            print("✅ 时间戳提取完成！")
            return main_track_timestamps, second_track_timestamps

        except Exception as e:
            print(f"❌ 时间戳提取失败: {e}")
            import traceback
            traceback.print_exc()
            return [], []
    
    def _extract_track_timestamps(self, track_path: str, track_name: str) -> List[SegmentTimestamp]:
        """提取单个轨道的时间戳"""
        timestamps = []
        
        if not os.path.exists(track_path):
            self.logger.warning(f"⚠️ {track_name}目录不存在: {track_path}")
            return timestamps
        
        # 查找特征文件
        feature_files = [f for f in os.listdir(track_path) if f.endswith('.features')]
        if not feature_files:
            self.logger.warning(f"⚠️ {track_name}没有找到特征文件")
            return timestamps
        
        # 按序号数字排序
        def extract_sequence_number(filename):
            try:
                return int(filename.split('_')[0])
            except:
                return 0
        
        feature_files.sort(key=extract_sequence_number)
        
        self.logger.info(f"📁 正在提取{track_name}时间戳: {len(feature_files)}个片段")
        
        for file_name in feature_files:
            try:
                timestamp = self._parse_timestamp_from_filename(file_name, track_name)
                if timestamp:
                    timestamps.append(timestamp)
            except Exception as e:
                self.logger.error(f"解析文件名失败: {file_name} - {e}")
        
        self.logger.info(f"✅ {track_name}时间戳提取完成: {len(timestamps)}个有效片段")
        return timestamps
    
    def _parse_timestamp_from_filename(self, file_name: str, track_name: str) -> Optional[SegmentTimestamp]:
        """从文件名解析时间戳信息"""
        try:
            # 文件名格式: 001_0.00s_2.50s_2.50s.features
            parts = file_name.split('_')
            if len(parts) < 4:
                self.logger.error(f"文件名格式不正确: {file_name}")
                return None
            
            sequence_number = int(parts[0])
            start_time = float(parts[1].replace('s', ''))
            end_time = float(parts[2].replace('s', ''))
            duration = float(parts[3].replace('s.features', ''))
            
            return SegmentTimestamp(file_name, sequence_number, start_time, end_time, duration, track_name)
            
        except Exception as e:
            self.logger.error(f"解析时间戳失败: {file_name} - {e}")
            return None
    
    def _extract_from_jianying_project(self, draft_path: str) -> Tuple[List[SegmentTimestamp], List[SegmentTimestamp]]:
        """从剪映项目提取时间戳 - 按照原项目逻辑"""
        print("📁 尝试从剪映项目提取时间戳...")

        if not draft_path or not os.path.exists(draft_path):
            print("❌ 未选择剪映草稿，无法提取时间戳")
            print("💡 建议：")
            print("   1. 先选择剪映项目文件夹")
            print("   2. 再选择具体的草稿文件")
            print("   3. 或使用'导入时间戳'功能手动导入")
            return [], []

        print(f"📂 当前草稿文件: {draft_path}")

        try:
            print("🔍 解析剪映草稿文件...")

            # 调用已有的剪映项目解析功能
            project_path = os.path.dirname(draft_path)
            print(f"📂 项目路径: {project_path}")

            # 调用片段解析功能
            main_track_timestamps = self._extract_jianying_segments(project_path, "主轨道")
            second_track_timestamps = self._extract_jianying_segments(project_path, "第二轨道")

            # 显示统计信息
            print("📊 解析结果:")
            print(f"   主轨道: {len(main_track_timestamps)} 个片段")
            print(f"   第二轨道: {len(second_track_timestamps)} 个片段")

            if main_track_timestamps or second_track_timestamps:
                # 生成报告
                self._generate_reports(main_track_timestamps, second_track_timestamps)
                print("✅ 时间戳提取和报告生成完成！")
            else:
                print("⚠️ 未找到有效的片段信息")

            return main_track_timestamps, second_track_timestamps

        except Exception as e:
            print(f"❌ 解析剪映草稿失败: {e}")
            import traceback
            traceback.print_exc()
            return [], []

    def _extract_jianying_segments(self, project_path: str, track_name: str) -> List[SegmentTimestamp]:
        """从剪映项目提取指定轨道的片段信息 - 按照原项目逻辑"""
        timestamps = []

        try:
            print(f"🔍 解析{track_name}片段信息...")

            # 读取剪映项目文件
            draft_file = os.path.join(project_path, "draft_content.json")
            if not os.path.exists(draft_file):
                print(f"❌ 项目文件不存在: {draft_file}")
                return timestamps

            # 读取草稿文件
            with open(draft_file, 'r', encoding='utf-8') as f:
                draft_data = json.load(f)

            # 提取轨道信息
            if track_name == "主轨道":
                timestamps = self._parse_track_segments(draft_data, 0, track_name)
            elif track_name == "第二轨道":
                timestamps = self._parse_track_segments(draft_data, 1, track_name)

            print(f"✅ {track_name}: 成功提取 {len(timestamps)} 个片段")

        except Exception as e:
            print(f"❌ 解析{track_name}失败: {e}")

        return timestamps

    def _parse_track_segments(self, draft_data: dict, track_index: int, track_name: str) -> List[SegmentTimestamp]:
        """解析轨道片段 - 按照原项目逻辑"""
        timestamps = []

        try:
            tracks = draft_data.get('tracks', [])
            video_tracks = [track for track in tracks if track.get('type') == 'video']

            if track_index >= len(video_tracks):
                print(f"⚠️ {track_name}不存在（轨道索引: {track_index}）")
                return timestamps

            track = video_tracks[track_index]
            segments = track.get('segments', [])

            for i, segment in enumerate(segments):
                try:
                    # 提取时间信息（微秒转秒）
                    target_timerange = segment.get('target_timerange', {})
                    start_time = target_timerange.get('start', 0) / 1000000.0
                    duration = target_timerange.get('duration', 0) / 1000000.0
                    end_time = start_time + duration

                    # 跳过时长为0的片段
                    if duration <= 0:
                        continue

                    # 生成文件名（按照原项目格式）
                    file_name = f"{i+1:03d}_{start_time:.3f}s_{end_time:.3f}s_{duration:.3f}s.features"

                    timestamp = SegmentTimestamp(
                        file_name=file_name,
                        sequence_number=i + 1,
                        start_time=start_time,
                        end_time=end_time,
                        duration=duration,
                        track_name=track_name
                    )
                    timestamps.append(timestamp)

                except Exception as e:
                    print(f"解析{track_name}片段{i+1}失败: {e}")

        except Exception as e:
            print(f"❌ 解析{track_name}轨道失败: {e}")

        return timestamps
    

    
    def _display_statistics(self, main_track: List[SegmentTimestamp], second_track: List[SegmentTimestamp]):
        """显示统计信息"""
        print("\n📊 时间戳统计信息")
        print("-" * 50)
        
        # 主轨道统计
        if main_track:
            main_total_duration = sum(t.duration for t in main_track)
            main_avg_duration = main_total_duration / len(main_track)
            main_min_duration = min(t.duration for t in main_track)
            main_max_duration = max(t.duration for t in main_track)
            
            print(f"🎬 主轨道:")
            print(f"   片段数量: {len(main_track)}个")
            print(f"   总时长: {main_total_duration:.2f}秒 ({main_total_duration/60:.2f}分钟)")
            print(f"   平均时长: {main_avg_duration:.2f}秒")
            print(f"   时长范围: {main_min_duration:.2f}秒 - {main_max_duration:.2f}秒")
            print(f"   时间范围: {main_track[0].format_time(main_track[0].start_time)} - {main_track[-1].format_time(main_track[-1].end_time)}")
        
        # 第二轨道统计
        if second_track:
            second_total_duration = sum(t.duration for t in second_track)
            second_avg_duration = second_total_duration / len(second_track)
            second_min_duration = min(t.duration for t in second_track)
            second_max_duration = max(t.duration for t in second_track)
            
            print(f"\n🎥 第二轨道:")
            print(f"   片段数量: {len(second_track)}个")
            print(f"   总时长: {second_total_duration:.2f}秒 ({second_total_duration/60:.2f}分钟)")
            print(f"   平均时长: {second_avg_duration:.2f}秒")
            print(f"   时长范围: {second_min_duration:.2f}秒 - {second_max_duration:.2f}秒")
            print(f"   时间范围: {second_track[0].format_time(second_track[0].start_time)} - {second_track[-1].format_time(second_track[-1].end_time)}")
        
        # 对比分析
        if main_track and second_track:
            main_total = sum(t.duration for t in main_track)
            second_total = sum(t.duration for t in second_track)
            ratio = main_total / second_total if second_total > 0 else 0
            
            print(f"\n🔄 对比分析:")
            print(f"   片段数量比: {len(main_track)} : {len(second_track)} ({len(main_track)/len(second_track):.2f} : 1)")
            print(f"   总时长比: {main_total:.2f} : {second_total:.2f} ({ratio:.2f} : 1)")
            print(f"   推测加速比例: {ratio:.2f}x")
    
    def _generate_reports(self, main_track: List[SegmentTimestamp], second_track: List[SegmentTimestamp]):
        """生成简洁的轨道时间戳文件"""
        try:
            # 创建报告文件夹
            report_dir = "timestamp_reports"
            if not os.path.exists(report_dir):
                os.makedirs(report_dir)
                self.logger.info(f"📁 创建报告文件夹: {os.path.abspath(report_dir)}")
            
            # 生成主轨道时间戳文件
            if main_track:
                self._generate_simple_track_file(main_track, os.path.join(report_dir, "主轨道.txt"))
            
            # 生成第二轨道时间戳文件
            if second_track:
                self._generate_simple_track_file(second_track, os.path.join(report_dir, "第二轨道.txt"))
            
            self.logger.info(f"\n📂 轨道时间戳文件已保存到: {os.path.abspath(report_dir)}")
            
        except Exception as e:
            self.logger.error(f"生成时间戳文件失败: {e}")
    
    def _generate_simple_track_file(self, track: List[SegmentTimestamp], file_name: str):
        """生成简洁的轨道时间戳文件"""
        try:
            with open(file_name, 'w', encoding='utf-8') as f:
                for ts in track:
                    # 格式: 序号 开始时间-结束时间
                    f.write(f"{ts.sequence_number:03d} {ts.format_time(ts.start_time)}-{ts.format_time(ts.end_time)}\n")
            
            self.logger.info(f"📄 {os.path.basename(file_name)} 已生成: {len(track)}个片段")
            
        except Exception as e:
            self.logger.error(f"生成文件失败 {file_name}: {e}")

    def _extract_all_video_tracks(self, draft_data: dict) -> dict:
        """提取所有视频轨道的片段信息"""
        all_tracks = {}

        try:
            tracks = draft_data.get('tracks', [])

            # 找到所有视频轨道
            video_tracks = []
            for i, track in enumerate(tracks):
                if track.get('type') == 'video':
                    segments = track.get('segments', [])
                    if segments:  # 只处理有片段的轨道
                        video_tracks.append((i, track))

            self.logger.info(f"📹 找到 {len(video_tracks)} 个有片段的视频轨道")

            # 提取每个轨道的片段
            for track_index, (real_index, track) in enumerate(video_tracks):
                track_name = f"轨道{track_index + 1}"
                segments = track.get('segments', [])
                timestamps = []

                self.logger.info(f"🎬 处理{track_name}（实际索引: {real_index}）: {len(segments)} 个片段")

                for i, segment in enumerate(segments):
                    try:
                        # 提取时间信息（微秒转秒）
                        target_timerange = segment.get('target_timerange', {})
                        start_time = target_timerange.get('start', 0) / 1000000.0
                        duration = target_timerange.get('duration', 0) / 1000000.0
                        end_time = start_time + duration

                        # 跳过时长为0的片段
                        if duration <= 0:
                            continue

                        # 生成文件名
                        file_name = f"{i+1:03d}_{start_time:.3f}s_{end_time:.3f}s_{duration:.3f}s"

                        timestamp = SegmentTimestamp(
                            file_name=file_name,
                            sequence_number=i + 1,
                            start_time=start_time,
                            end_time=end_time,
                            duration=duration,
                            track_name=track_name
                        )
                        timestamps.append(timestamp)

                    except Exception as e:
                        self.logger.error(f"解析{track_name}片段{i+1}失败: {e}")

                if timestamps:
                    all_tracks[track_name] = timestamps
                    self.logger.info(f"✅ {track_name}: 成功提取 {len(timestamps)} 个有效片段")
                else:
                    self.logger.warning(f"⚠️ {track_name}: 没有有效片段")

        except Exception as e:
            self.logger.error(f"❌ 提取所有轨道失败: {e}")

        return all_tracks

def main():
    """测试时间戳提取功能"""
    extractor = TimestampExtractor()
    main_track, second_track = extractor.extract_timestamps()

    if main_track or second_track:
        print("\n✅ 时间戳提取测试完成")
    else:
        print("\n⚠️ 未找到时间戳数据")

if __name__ == "__main__":
    main()
