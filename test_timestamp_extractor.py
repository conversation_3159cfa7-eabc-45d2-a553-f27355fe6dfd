#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试时间戳提取功能
"""

from timestamp_extractor import TimestampExtractor
import os

def test_timestamp_extractor():
    """测试时间戳提取功能"""
    print("=" * 60)
    print("测试时间戳提取功能")
    print("=" * 60)
    
    extractor = TimestampExtractor()
    
    # 测试草稿文件路径
    test_draft_path = "测试项目文件夹/测试项目1/draft_content.json"
    
    print(f"📝 测试草稿文件: {test_draft_path}")
    
    if os.path.exists(test_draft_path):
        print("✅ 测试文件存在，开始提取...")
        
        # 执行提取
        main_track, second_track = extractor.extract_timestamps(
            project_path="测试项目文件夹/测试项目1",
            draft_path=test_draft_path
        )
        
        print(f"\n📊 提取结果:")
        print(f"主轨道: {len(main_track)} 个片段")
        print(f"第二轨道: {len(second_track)} 个片段")
        
        # 显示前几个片段
        if main_track:
            print(f"\n🎬 主轨道前3个片段:")
            for i, ts in enumerate(main_track[:3]):
                print(f"  {i+1}. {ts}")
        
        if second_track:
            print(f"\n🎥 第二轨道前3个片段:")
            for i, ts in enumerate(second_track[:3]):
                print(f"  {i+1}. {ts}")
        
        # 检查报告文件
        reports_folder = "timestamp_reports"
        if os.path.exists(reports_folder):
            print(f"\n📂 报告文件夹: {reports_folder}")
            files = os.listdir(reports_folder)
            for file in files:
                print(f"  📄 {file}")
        
    else:
        print("❌ 测试文件不存在")
        print("💡 请先运行GUI并选择一个真实的剪映项目进行测试")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_timestamp_extractor()
    input("\n按回车键退出...")
