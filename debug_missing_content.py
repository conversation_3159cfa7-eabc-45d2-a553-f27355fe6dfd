#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试遗漏内容
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_missing_content():
    """调试遗漏内容"""
    print("🔍 调试遗漏内容")
    print("=" * 60)
    
    try:
        from subtitle_matcher import SubtitleMatcher
        
        # 创建匹配器
        matcher = SubtitleMatcher()
        
        # 文件路径
        file_a_path = "字幕与时间戳合并/二次剪辑合并结果.txt"
        file_b_path = "字幕与时间戳合并/原素材合并结果.txt"
        
        # 读取二次剪辑文件
        print("📋 读取二次剪辑文件...")
        with open(file_a_path, 'r', encoding='utf-8') as f:
            lines_a = f.readlines()
        
        # 解析二次剪辑
        entries_a = []
        current_seq = None
        current_timestamp = None
        current_text = None
        
        for line in lines_a:
            line = line.strip()
            if line.isdigit():
                current_seq = int(line)
            elif ' --> ' in line:
                current_timestamp = line
            elif line and current_seq is not None and current_timestamp is not None:
                current_text = line
                entries_a.append({
                    'seq': current_seq,
                    'timestamp': current_timestamp,
                    'text': current_text
                })
                current_seq = None
                current_timestamp = None
                current_text = None
        
        print(f"✅ 二次剪辑共{len(entries_a)}个条目")
        
        # 读取原素材文件
        print("📋 读取原素材文件...")
        with open(file_b_path, 'r', encoding='utf-8') as f:
            lines_b = f.readlines()
        
        # 解析原素材
        entries_b = []
        current_seq = None
        current_timestamp = None
        current_text = None
        
        for line in lines_b:
            line = line.strip()
            if line.isdigit():
                current_seq = int(line)
            elif ' --> ' in line:
                current_timestamp = line
            elif line and current_seq is not None and current_timestamp is not None:
                current_text = line
                entries_b.append({
                    'seq': current_seq,
                    'timestamp': current_timestamp,
                    'text': current_text
                })
                current_seq = None
                current_timestamp = None
                current_text = None
        
        print(f"✅ 原素材共{len(entries_b)}个条目")
        
        # 查找您提到的"出去"
        print(f"\n🔍 查找'出去'在二次剪辑中的位置:")
        found_in_a = []
        for entry in entries_a:
            if '出去' in entry['text']:
                found_in_a.append(entry)
                print(f"  二次剪辑序号{entry['seq']}: {entry['timestamp']} - {entry['text']}")
        
        print(f"\n🔍 查找'出去'在原素材中的位置:")
        found_in_b = []
        for entry in entries_b:
            if '出去' in entry['text']:
                found_in_b.append(entry)
                print(f"  原素材序号{entry['seq']}: {entry['timestamp']} - {entry['text']}")
        
        # 查找序号137和138
        print(f"\n🔍 查找原素材序号137和138:")
        for entry in entries_b:
            if entry['seq'] in [137, 138]:
                print(f"  原素材序号{entry['seq']}: {entry['timestamp']} - {entry['text']}")
        
        # 执行匹配并检查结果
        print(f"\n🔍 执行匹配并检查结果:")
        
        success, timestamps, error_msg = matcher.process_files(
            file_a_path=file_a_path,
            file_b_path=file_b_path,
            auto_save=False,  # 不保存，只检查
            max_time_gap=8.0
        )
        
        if success:
            print(f"✅ 匹配成功，共{len(timestamps)}个时间戳")
            
            # 检查27-28分钟区域
            print(f"\n🔍 检查27-28分钟区域:")
            minute_27_28_timestamps = []
            for i, ts in enumerate(timestamps):
                if '00:27:' in ts or '00:28:' in ts:
                    minute_27_28_timestamps.append((i+1, ts))
            
            for idx, ts in minute_27_28_timestamps:
                print(f"  {idx}. {ts}")
            
            # 检查是否有6分钟区域的时间戳
            print(f"\n🔍 检查是否有6分钟区域的时间戳:")
            minute_6_timestamps = []
            for i, ts in enumerate(timestamps):
                if '00:06:' in ts:
                    minute_6_timestamps.append((i+1, ts))
            
            if minute_6_timestamps:
                print(f"  找到{len(minute_6_timestamps)}个6分钟区域时间戳:")
                for idx, ts in minute_6_timestamps:
                    print(f"    {idx}. {ts}")
            else:
                print(f"  ❌ 没有找到6分钟区域的时间戳")
        
        # 检查匹配过程
        print(f"\n🔍 检查匹配过程中是否遗漏了'出去':")
        
        # 手动检查匹配
        if found_in_a and found_in_b:
            print(f"  二次剪辑中的'出去': {found_in_a[0]['text']}")
            print(f"  原素材中的'出去': {found_in_b[0]['text']}")
            
            # 检查是否应该匹配
            a_text = found_in_a[0]['text'].strip()
            b_text = found_in_b[0]['text'].strip()
            
            if a_text == b_text:
                print(f"  ✅ 文本完全匹配，应该能够匹配")
            elif a_text in b_text or b_text in a_text:
                print(f"  ✅ 文本包含匹配，应该能够匹配")
            else:
                print(f"  ❌ 文本不匹配")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 调试遗漏内容")
    print("=" * 80)
    
    success = debug_missing_content()
    
    if success:
        print(f"\n✅ 调试完成")
    else:
        print(f"\n❌ 调试失败")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
