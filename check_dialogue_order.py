#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查台词顺序对应关系
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dialogue_order():
    """检查台词顺序对应关系"""
    print("🔍 检查台词顺序对应关系")
    print("=" * 60)
    
    try:
        # 读取二次剪辑文件
        with open("字幕与时间戳合并/二次剪辑合并结果.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 提取台词
        dialogues = []
        current_seq = None
        current_timestamp = None
        
        for line in lines:
            line = line.strip()
            if line.isdigit():
                current_seq = int(line)
            elif ' --> ' in line:
                current_timestamp = line
            elif line and current_seq is not None and current_timestamp is not None:
                dialogues.append({
                    'seq': current_seq,
                    'timestamp': current_timestamp,
                    'text': line
                })
                current_seq = None
                current_timestamp = None
        
        print(f"📋 二次剪辑的前20个台词:")
        for i, dialogue in enumerate(dialogues[:20]):
            print(f"  {i+1:2d}. [{dialogue['seq']:3d}] {dialogue['timestamp']} - {dialogue['text']}")
        
        # 读取匹配结果
        with open("匹配结果.txt", 'r', encoding='utf-8') as f:
            result_lines = f.readlines()
        
        # 提取匹配结果中的时间戳
        result_timestamps = []
        for line in result_lines:
            line = line.strip()
            if ' --> ' in line:
                result_timestamps.append(line)
        
        print(f"\n📋 匹配结果的前20个时间戳:")
        for i, timestamp in enumerate(result_timestamps[:20]):
            print(f"  {i+1:2d}. {timestamp}")
        
        # 分析对应关系
        print(f"\n🔍 分析对应关系:")
        print(f"二次剪辑台词数量: {len(dialogues)}")
        print(f"匹配结果时间戳数量: {len(result_timestamps)}")
        
        # 检查前10个的对应关系
        print(f"\n📊 前10个台词与时间戳的对应关系:")
        for i in range(min(10, len(dialogues), len(result_timestamps))):
            dialogue = dialogues[i]
            result_timestamp = result_timestamps[i]
            
            # 解析时间戳的分钟数
            if ' --> ' in result_timestamp:
                start_str = result_timestamp.split(' --> ')[0]
                # 解析时间戳 HH:MM:SS,mmm
                time_parts = start_str.split(':')
                if len(time_parts) >= 3:
                    hours = int(time_parts[0])
                    minutes = int(time_parts[1])
                    total_minutes = hours * 60 + minutes
                else:
                    total_minutes = 0
            else:
                total_minutes = 0
            
            print(f"  台词{i+1:2d}: {dialogue['text'][:30]}...")
            print(f"         二次剪辑时间: {dialogue['timestamp']}")
            print(f"         匹配结果时间: {result_timestamp} ({total_minutes}分钟)")
            
            # 检查是否合理
            if i <= 2 and total_minutes >= 32:  # 前3个应该在33分钟附近
                print(f"         ✅ 合理：核心故事")
            elif i >= 3 and total_minutes < 5:   # 后面的应该在视频开头或其他位置
                print(f"         ✅ 合理：视频开头")
            elif i >= 3 and 25 <= total_minutes <= 35:  # 后面的在25-35分钟
                print(f"         ❓ 需要确认：是否应该在这个位置")
            else:
                print(f"         ❓ 需要确认")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 台词顺序对应关系检查")
    print("=" * 80)
    
    success = check_dialogue_order()
    
    if success:
        print(f"\n✅ 检查完成")
    else:
        print(f"\n❌ 检查失败")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
