#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON导入修复
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_json_import():
    """测试JSON导入是否修复"""
    try:
        from unified_gui import UnifiedGUI
        import tkinter as tk
        
        # 创建隐藏的测试窗口
        root = tk.Tk()
        root.withdraw()
        
        # 模拟创建GUI实例
        class TestGUI(UnifiedGUI):
            def __init__(self):
                self.root = root
                self.setup_variables()
                self.setup_logger()
        
        app = TestGUI()
        
        # 测试轨道分析方法中的JSON使用
        test_draft_data = {
            'tracks': [
                {
                    'type': 'video',
                    'segments': [
                        {
                            'target_timerange': {
                                'start': 0,
                                'duration': 2300000
                            }
                        },
                        {
                            'target_timerange': {
                                'start': 2300000,
                                'duration': 1600000
                            }
                        }
                    ]
                }
            ]
        }
        
        # 测试_calculate_track_gaps方法
        segments = test_draft_data['tracks'][0]['segments']
        gaps = app._calculate_track_gaps(segments)
        
        print("✅ JSON导入修复成功")
        print(f"✅ 测试数据处理成功，发现 {len(gaps)} 个间隙")
        
        root.destroy()
        return True
        
    except NameError as e:
        if "json" in str(e):
            print(f"❌ JSON导入仍有问题: {e}")
            return False
        else:
            print(f"❌ 其他NameError: {e}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 测试JSON导入修复")
    print("=" * 30)
    
    if test_json_import():
        print("\n🎉 JSON导入修复验证成功！")
        print("💡 现在可以正常使用轨道左对齐功能了")
    else:
        print("\n❌ JSON导入修复验证失败")

if __name__ == "__main__":
    main()
