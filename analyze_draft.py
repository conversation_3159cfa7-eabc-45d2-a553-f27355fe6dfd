#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剪映草稿分析工具 - 分析轨道结构和片段间隙
"""

import json
import os
from typing import List, Dict, Any, Tuple

class DraftAnalyzer:
    """剪映草稿分析器"""
    
    def __init__(self):
        self.draft_data = None
        self.tracks = []
        
    def load_draft(self, draft_path: str) -> bool:
        """加载剪映草稿文件"""
        try:
            draft_file = os.path.join(draft_path, "draft_content.json")
            if not os.path.exists(draft_file):
                print(f"❌ 草稿文件不存在: {draft_file}")
                return False
                
            with open(draft_file, 'r', encoding='utf-8') as f:
                self.draft_data = json.load(f)
                
            print(f"✅ 成功加载草稿: {draft_path}")
            return True
            
        except Exception as e:
            print(f"❌ 加载草稿失败: {e}")
            return False
    
    def analyze_tracks(self) -> Dict[str, Any]:
        """分析轨道结构"""
        if not self.draft_data:
            return {}
            
        try:
            tracks = self.draft_data.get('tracks', [])
            video_tracks = [track for track in tracks if track.get('type') == 'video']
            
            analysis = {
                'total_tracks': len(tracks),
                'video_tracks': len(video_tracks),
                'tracks_info': []
            }
            
            for i, track in enumerate(video_tracks):
                segments = track.get('segments', [])
                track_info = {
                    'track_index': i,
                    'segment_count': len(segments),
                    'segments': []
                }
                
                # 分析每个片段
                for j, segment in enumerate(segments):
                    target_timerange = segment.get('target_timerange', {})
                    start_time = target_timerange.get('start', 0) / 1000000.0  # 微秒转秒
                    duration = target_timerange.get('duration', 0) / 1000000.0
                    end_time = start_time + duration
                    
                    segment_info = {
                        'index': j,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': duration
                    }
                    track_info['segments'].append(segment_info)
                
                # 计算间隙
                gaps = self._calculate_gaps(track_info['segments'])
                track_info['gaps'] = gaps
                track_info['has_gaps'] = len(gaps) > 0
                
                analysis['tracks_info'].append(track_info)
            
            return analysis
            
        except Exception as e:
            print(f"❌ 分析轨道失败: {e}")
            return {}
    
    def _calculate_gaps(self, segments: List[Dict]) -> List[Dict]:
        """计算片段间的间隙"""
        gaps = []
        
        if len(segments) < 2:
            return gaps
            
        # 按开始时间排序
        sorted_segments = sorted(segments, key=lambda x: x['start_time'])
        
        for i in range(len(sorted_segments) - 1):
            current_end = sorted_segments[i]['end_time']
            next_start = sorted_segments[i + 1]['start_time']
            
            if next_start > current_end:
                gap = {
                    'after_segment': i,
                    'before_segment': i + 1,
                    'gap_start': current_end,
                    'gap_end': next_start,
                    'gap_duration': next_start - current_end
                }
                gaps.append(gap)
        
        return gaps
    
    def print_analysis(self, analysis: Dict[str, Any]):
        """打印分析结果"""
        if not analysis:
            print("❌ 没有分析数据")
            return
            
        print(f"\n📊 草稿分析结果:")
        print(f"   总轨道数: {analysis['total_tracks']}")
        print(f"   视频轨道数: {analysis['video_tracks']}")
        
        for track_info in analysis['tracks_info']:
            track_idx = track_info['track_index']
            segment_count = track_info['segment_count']
            gaps = track_info['gaps']
            
            print(f"\n🎬 轨道 {track_idx + 1}:")
            print(f"   片段数量: {segment_count}")
            print(f"   是否有间隙: {'是' if track_info['has_gaps'] else '否'}")
            
            if gaps:
                print(f"   间隙数量: {len(gaps)}")
                for gap in gaps:
                    print(f"     间隙 {gap['after_segment']+1}-{gap['before_segment']+1}: "
                          f"{gap['gap_start']:.3f}s - {gap['gap_end']:.3f}s "
                          f"(时长: {gap['gap_duration']:.3f}s)")
            
            # 显示片段详情
            print(f"   片段详情:")
            for seg in track_info['segments']:
                print(f"     片段{seg['index']+1}: {seg['start_time']:.3f}s - {seg['end_time']:.3f}s "
                      f"(时长: {seg['duration']:.3f}s)")

def main():
    """主函数"""
    draft_path = r"D:\JianyingPro Drafts\8月1日"
    
    analyzer = DraftAnalyzer()
    
    if analyzer.load_draft(draft_path):
        analysis = analyzer.analyze_tracks()
        analyzer.print_analysis(analysis)
        
        # 保存分析结果
        with open("draft_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        print(f"\n💾 分析结果已保存到: draft_analysis.json")

if __name__ == "__main__":
    main()
