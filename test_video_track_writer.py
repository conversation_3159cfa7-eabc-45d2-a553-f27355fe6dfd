#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的视频轨道写入器功能
"""

from video_track_writer import VideoTrackWriter, MatchResult
import os

def test_video_track_writer():
    """测试视频轨道写入器功能"""
    print("=" * 60)
    print("测试视频轨道写入器功能（按照原项目逻辑）")
    print("=" * 60)
    
    # 创建测试匹配结果 - 按照原项目TestVideoTrackWriter.java的格式
    test_matches = []
    for i in range(5):
        match = MatchResult(
            main_segment_name=f"主轨道片段{i + 1}",
            main_start_time=i * 3.0,
            main_end_time=(i + 1) * 3.0,
            second_segment_name=f"第二轨道片段{i + 1}",
            second_start_time=i * 5.0 + 10.0,
            second_end_time=(i + 1) * 5.0 + 10.0,
            confidence=0.85 + i * 0.02,
            method="DINOv2特征匹配"
        )
        test_matches.append(match)
    
    print(f"📊 测试匹配结果: {len(test_matches)} 个")
    for i, match in enumerate(test_matches, 1):
        print(f"  {i}. {match}")
    
    # 测试项目路径
    test_project_path = "D:/JianyingPro Drafts/7月11日"
    
    print(f"\n📁 测试项目路径: {test_project_path}")
    
    if os.path.exists(test_project_path):
        print("✅ 测试项目存在，开始写入测试...")
        
        # 执行写入
        success = VideoTrackWriter.write_matches_to_jianying_project(test_project_path, test_matches)
        
        if success:
            print("✅ 写入测试成功！")
            print("🎬 请在剪映中重新打开项目查看第三视频轨道")
        else:
            print("❌ 写入测试失败")
    else:
        print("❌ 测试项目不存在")
        print("💡 请使用真实的剪映项目路径进行测试")

def test_material_id_extraction():
    """测试材料ID提取功能"""
    print("\n" + "=" * 60)
    print("测试材料ID提取功能")
    print("=" * 60)
    
    # 创建简单的测试JSON
    test_json = """{
  "tracks": [
    {
      "type": "video",
      "segments": [
        {
          "material_id": "first_track_material_123"
        }
      ]
    },
    {
      "type": "video", 
      "segments": [
        {
          "material_id": "second_track_material_456"
        }
      ]
    }
  ]
}"""
    
    print("📋 测试JSON:")
    print(test_json[:200] + "..." if len(test_json) > 200 else test_json)
    
    # 测试材料ID提取
    material_id = VideoTrackWriter._get_second_track_material_id(test_json)
    
    print(f"\n🔍 提取结果:")
    print(f"  第二轨道材料ID: {material_id}")
    
    if material_id == "second_track_material_456":
        print("✅ 材料ID提取测试成功")
        return True
    else:
        print("❌ 材料ID提取测试失败")
        return False

def test_uuid_generation():
    """测试UUID生成功能"""
    print("\n" + "=" * 60)
    print("测试UUID生成功能")
    print("=" * 60)
    
    # 测试UUID生成
    uuid1 = VideoTrackWriter._generate_uuid()
    uuid2 = VideoTrackWriter._generate_uuid()
    
    print(f"📋 UUID生成测试:")
    print(f"  UUID1: {uuid1}")
    print(f"  UUID2: {uuid2}")
    print(f"  长度: {len(uuid1)} 字符")
    print(f"  是否不同: {uuid1 != uuid2}")
    print(f"  是否无连字符: {'-' not in uuid1}")
    
    if len(uuid1) == 32 and '-' not in uuid1 and uuid1 != uuid2:
        print("✅ UUID生成测试成功")
        return True
    else:
        print("❌ UUID生成测试失败")
        return False

def test_json_structure_generation():
    """测试JSON结构生成"""
    print("\n" + "=" * 60)
    print("测试JSON结构生成")
    print("=" * 60)
    
    # 创建测试匹配结果
    test_matches = [
        MatchResult(
            main_segment_name="test_segment",
            main_start_time=10.0,
            main_end_time=20.0,
            second_segment_name="source_segment",
            second_start_time=100.0,
            second_end_time=110.0,
            confidence=0.95,
            method="测试"
        )
    ]
    
    test_material_id = "test_material_id_123"
    
    # 生成第三轨道JSON
    track_json = VideoTrackWriter._generate_third_video_track_json(test_matches, test_material_id)
    
    print("📋 生成的轨道JSON:")
    print(track_json[:300] + "..." if len(track_json) > 300 else track_json)
    
    # 检查关键字段
    checks = [
        ('"type": "video"' in track_json, "轨道类型为video"),
        (f'"material_id": "{test_material_id}"' in track_json, "材料ID正确"),
        ('"source_timerange"' in track_json, "包含源时间范围"),
        ('"target_timerange"' in track_json, "包含目标时间范围"),
        ('"start": 100000000' in track_json, "源开始时间正确（100秒=100000000微秒）"),
        ('"start": 10000000' in track_json, "目标开始时间正确（10秒=10000000微秒）")
    ]
    
    print(f"\n🔍 JSON结构检查:")
    all_passed = True
    for check, description in checks:
        status = "✅" if check else "❌"
        print(f"  {status} {description}")
        if not check:
            all_passed = False
    
    if all_passed:
        print("✅ JSON结构生成测试成功")
        return True
    else:
        print("❌ JSON结构生成测试失败")
        return False

def main():
    """主测试函数"""
    print("🧪 视频轨道写入器功能测试")
    print("=" * 60)
    
    # 测试各个组件
    material_id_success = test_material_id_extraction()
    uuid_success = test_uuid_generation()
    json_success = test_json_structure_generation()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 材料ID提取: {'通过' if material_id_success else '失败'}")
    print(f"✅ UUID生成: {'通过' if uuid_success else '失败'}")
    print(f"✅ JSON结构生成: {'通过' if json_success else '失败'}")
    
    if material_id_success and uuid_success and json_success:
        print("\n🎉 所有测试通过！")
        print("📋 功能说明:")
        print("1. 新的视频轨道写入器已按照原项目逻辑实现")
        print("2. 创建真正的视频轨道，而不是文本轨道")
        print("3. 正确获取第二轨道的材料ID并复用")
        print("4. 使用原始素材，但调整时间戳位置")
        print("5. 完全兼容剪映的视频轨道格式")
        
        # 进行完整写入测试
        test_video_track_writer()
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
