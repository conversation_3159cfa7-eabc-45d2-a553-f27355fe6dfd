#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试双组字幕合并功能
"""

import os
from subtitle_timestamp_merger import SubtitleTimestampMerger

def create_test_files():
    """创建测试文件"""
    print("📋 创建测试文件...")
    
    # 创建测试目录
    os.makedirs("二次剪辑素材", exist_ok=True)
    os.makedirs("原视频匹配素材", exist_ok=True)
    os.makedirs("字幕与时间戳合并", exist_ok=True)
    
    # 二次剪辑组测试文件
    edited_subtitle_content = """1
00:00:00,000 --> 00:00:03,000
二次剪辑字幕第一条

2
00:00:03,000 --> 00:00:06,000
二次剪辑字幕第二条

3
00:00:06,000 --> 00:00:09,000
二次剪辑字幕第三条"""

    edited_timestamp_content = """1
00:00:00,000 --> 00:00:03,000

2
00:00:03,000 --> 00:00:06,000

3
00:00:06,000 --> 00:00:09,000
"""

    # 原素材组测试文件
    original_subtitle_content = """1
00:00:10,000 --> 00:00:13,000
原素材字幕第一条

2
00:00:13,000 --> 00:00:16,000
原素材字幕第二条

3
00:00:16,000 --> 00:00:19,000
原素材字幕第三条"""

    original_timestamp_content = """1
00:00:10,000 --> 00:00:13,000

2
00:00:13,000 --> 00:00:16,000

3
00:00:16,000 --> 00:00:19,000
"""

    # 保存测试文件
    test_files = [
        ("二次剪辑素材/二次剪辑OCR字幕.txt", edited_subtitle_content),
        ("二次剪辑素材/二次剪辑分镜时间戳.txt", edited_timestamp_content),
        ("原视频匹配素材/原素材OCR字幕.txt", original_subtitle_content),
        ("原视频匹配素材/原素材分镜时间戳.txt", original_timestamp_content),
    ]
    
    for file_path, content in test_files:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ 创建: {file_path}")
    
    return test_files

def test_dual_merge():
    """测试双组合并"""
    print("\n" + "=" * 60)
    print("测试双组字幕合并功能")
    print("=" * 60)
    
    # 二次剪辑组合并
    print("📝 处理二次剪辑组...")
    merger_edited = SubtitleTimestampMerger()
    
    edited_subtitle_file = "二次剪辑素材/二次剪辑OCR字幕.txt"
    edited_timestamp_file = "二次剪辑素材/二次剪辑分镜时间戳.txt"
    edited_output_file = "字幕与时间戳合并/二次剪辑合并结果.txt"
    
    success_edited, result_edited = merger_edited.process_files(
        edited_subtitle_file,
        edited_timestamp_file,
        edited_output_file
    )
    
    print(f"📊 二次剪辑组合并:")
    print(f"  成功: {'✅' if success_edited else '❌'}")
    print(f"  结果: {result_edited}")
    
    # 原素材组合并
    print("\n🎬 处理原素材组...")
    merger_original = SubtitleTimestampMerger()
    
    original_subtitle_file = "原视频匹配素材/原素材OCR字幕.txt"
    original_timestamp_file = "原视频匹配素材/原素材分镜时间戳.txt"
    original_output_file = "字幕与时间戳合并/原素材合并结果.txt"
    
    success_original, result_original = merger_original.process_files(
        original_subtitle_file,
        original_timestamp_file,
        original_output_file
    )
    
    print(f"📊 原素材组合并:")
    print(f"  成功: {'✅' if success_original else '❌'}")
    print(f"  结果: {result_original}")
    
    # 检查输出结果
    overall_success = success_edited and success_original
    
    print(f"\n📊 整体结果:")
    print(f"  双组合并: {'✅ 成功' if overall_success else '❌ 失败'}")
    
    if overall_success:
        # 显示输出文件内容
        print(f"\n📁 输出文件:")
        
        if os.path.exists(edited_output_file):
            with open(edited_output_file, 'r', encoding='utf-8') as f:
                edited_content = f.read()
            print(f"\n📝 二次剪辑组输出 ({edited_output_file}):")
            print(edited_content[:200] + "..." if len(edited_content) > 200 else edited_content)
            
            # 统计字幕数量
            lines = edited_content.split('\n')
            edited_count = sum(1 for line in lines if line.strip() and not line.strip().isdigit() and ' --> ' not in line)
            print(f"  字幕数量: {edited_count} 条")
        
        if os.path.exists(original_output_file):
            with open(original_output_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            print(f"\n🎬 原素材组输出 ({original_output_file}):")
            print(original_content[:200] + "..." if len(original_content) > 200 else original_content)
            
            # 统计字幕数量
            lines = original_content.split('\n')
            original_count = sum(1 for line in lines if line.strip() and not line.strip().isdigit() and ' --> ' not in line)
            print(f"  字幕数量: {original_count} 条")
            
            print(f"\n📊 总统计:")
            print(f"  二次剪辑组: {edited_count} 条字幕")
            print(f"  原素材组: {original_count} 条字幕")
            print(f"  总计: {edited_count + original_count} 条字幕")
    
    return overall_success

def test_smart_fill():
    """测试智能填充功能"""
    print("\n" + "=" * 60)
    print("测试智能填充功能")
    print("=" * 60)
    
    # 模拟智能填充逻辑
    filled_count = 0
    
    # 二次剪辑组智能填充
    edited_subtitle_candidates = [
        "二次剪辑素材/二次剪辑OCR字幕.txt",
        "二次剪辑素材/二次剪辑.txt",
        "提取时间戳/二次剪辑字幕.txt"
    ]
    
    edited_timestamp_candidates = [
        "二次剪辑素材/二次剪辑分镜时间戳.txt",
        "二次剪辑素材/二次剪辑时间戳.txt",
        "提取时间戳/二次剪辑时间戳.txt"
    ]
    
    print("📝 二次剪辑组智能填充:")
    for candidate in edited_subtitle_candidates:
        if os.path.exists(candidate):
            print(f"  ✅ 找到字幕文件: {candidate}")
            filled_count += 1
            break
    else:
        print(f"  ❌ 未找到字幕文件")
    
    for candidate in edited_timestamp_candidates:
        if os.path.exists(candidate):
            print(f"  ✅ 找到时间戳文件: {candidate}")
            filled_count += 1
            break
    else:
        print(f"  ❌ 未找到时间戳文件")
    
    # 原素材组智能填充
    original_subtitle_candidates = [
        "原视频匹配素材/原素材OCR字幕.txt",
        "原视频匹配素材/原素材.txt",
        "提取时间戳/原素材字幕.txt"
    ]
    
    original_timestamp_candidates = [
        "原视频匹配素材/原素材分镜时间戳.txt",
        "原视频匹配素材/原素材时间戳.txt",
        "提取时间戳/原素材时间戳.txt"
    ]
    
    print("\n🎬 原素材组智能填充:")
    for candidate in original_subtitle_candidates:
        if os.path.exists(candidate):
            print(f"  ✅ 找到字幕文件: {candidate}")
            filled_count += 1
            break
    else:
        print(f"  ❌ 未找到字幕文件")
    
    for candidate in original_timestamp_candidates:
        if os.path.exists(candidate):
            print(f"  ✅ 找到时间戳文件: {candidate}")
            filled_count += 1
            break
    else:
        print(f"  ❌ 未找到时间戳文件")
    
    print(f"\n📊 智能填充结果:")
    print(f"  成功填充: {filled_count} 个文件")
    print(f"  填充状态: {'✅ 成功' if filled_count >= 4 else '⚠️ 部分成功' if filled_count > 0 else '❌ 失败'}")
    
    return filled_count >= 4

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    test_files = [
        "二次剪辑素材/二次剪辑OCR字幕.txt",
        "二次剪辑素材/二次剪辑分镜时间戳.txt",
        "原视频匹配素材/原素材OCR字幕.txt",
        "原视频匹配素材/原素材分镜时间戳.txt",
        "字幕与时间戳合并/二次剪辑合并结果.txt",
        "字幕与时间戳合并/原素材合并结果.txt",
    ]
    
    for file_path in test_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"  ✅ 删除: {file_path}")
        except:
            print(f"  ❌ 删除失败: {file_path}")

def main():
    """主测试函数"""
    print("🧪 双组字幕合并功能测试")
    print("=" * 60)
    
    # 创建测试文件
    create_test_files()
    
    # 测试智能填充
    smart_fill_success = test_smart_fill()
    
    # 测试双组合并
    dual_merge_success = test_dual_merge()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 智能填充: {'通过' if smart_fill_success else '失败'}")
    print(f"✅ 双组合并: {'通过' if dual_merge_success else '失败'}")
    
    if smart_fill_success and dual_merge_success:
        print("\n🎉 双组字幕合并功能测试成功！")
        print("📋 功能特点:")
        print("1. ✅ 区分二次剪辑与原素材两组")
        print("2. ✅ 分别独立输出，不合并")
        print("3. ✅ 智能填充功能自动选择文件")
        print("4. ✅ 完整的错误处理和验证")
        print("5. ✅ 清晰的界面区分和统计信息")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    # 询问是否清理测试文件
    response = input("\n是否清理测试文件？(y/n): ")
    if response.lower() == 'y':
        cleanup_test_files()
    
    print("=" * 60)

if __name__ == "__main__":
    main()
