# 剪映轨道左对齐功能说明

## 功能概述

本功能已**完全集成到字幕工具.py**中，专门用于处理剪映草稿中轨道片段间的间隙问题，实现**左对齐吸附**效果。当轨道中的片段之间存在空隙时，该功能会自动将后续片段向左移动，消除间隙，使所有片段紧密连接。

## ✅ 集成状态

- ✅ 已集成到统一GUI界面（unified_gui.py）
- ✅ 通过"字幕工具.py"启动器访问
- ✅ 新增"⬅️ 轨道左对齐"标签页
- ✅ 完整的错误处理和日志记录
- ✅ 自动备份和安全保护机制

## 问题背景

在剪映编辑过程中，经常会遇到以下情况：
- 删除某些片段后，轨道中出现空隙
- 手动调整片段位置时产生不必要的间隔
- 需要将分散的片段整理成连续的时间线

**示例场景：**
- 主轨道：片段连续对齐 ✅
- 第二轨道：片段间有间隙，需要左对齐 ❌

## 功能特点

### 🎯 智能分析
- 自动检测轨道中的所有间隙
- 精确计算每个间隙的时长
- 显示详细的轨道状态报告

### 🔧 精准对齐
- 保持片段原有时长不变
- 只调整片段的时间轴位置
- 累积计算偏移量，确保精确对齐

### 🛡️ 安全保护
- 自动备份原始草稿文件
- 防止时间轴移动到负数位置
- 操作前确认提示

### 📊 详细日志
- 实时显示处理进度
- 记录每个片段的移动详情
- 提供操作结果统计

## 实现原理

### 1. 草稿文件解析
```python
# 读取剪映草稿的JSON结构
draft_data = json.load(draft_file)
tracks = draft_data.get('tracks', [])
video_tracks = [track for track in tracks if track.get('type') == 'video']
```

### 2. 间隙检测算法
```python
def _calculate_gaps(segments):
    gaps = []
    sorted_segments = sorted(segments, key=lambda x: x['start_time'])
    
    for i in range(len(sorted_segments) - 1):
        current_end = sorted_segments[i]['end_time']
        next_start = sorted_segments[i + 1]['start_time']
        
        if next_start > current_end:
            gap_duration = next_start - current_end
            gaps.append({
                'gap_duration': gap_duration,
                'after_segment': i,
                'before_segment': i + 1
            })
    
    return gaps
```

### 3. 左对齐处理
```python
def left_align_track(segments, gaps):
    cumulative_offset = 0
    
    for i, segment in enumerate(segments):
        # 应用累积偏移量
        if cumulative_offset > 0:
            original_start = segment['target_timerange']['start']
            new_start = max(0, original_start - cumulative_offset)
            segment['target_timerange']['start'] = new_start
        
        # 检查是否有间隙在此片段后
        for gap in gaps:
            if gap['after_segment'] == i:
                cumulative_offset += gap['gap_duration']
                break
```

## 使用方法

### 集成版本（推荐）
```bash
python 字幕工具.py
```

### GUI操作步骤
1. **启动工具**：双击运行"字幕工具.py"或使用启动脚本
2. **选择标签页**：点击"⬅️ 轨道左对齐"标签页
3. **选择项目**：
   - 点击"浏览"选择剪映项目文件夹
   - 从下拉列表中选择具体的草稿项目
4. **分析轨道**：点击"📊 分析轨道"查看当前状态
5. **执行对齐**：点击"⬅️ 执行左对齐"开始处理
6. **查看结果**：在剪映中重新打开项目查看效果

### 独立版本（备用）
```bash
python track_left_align.py      # 命令行版本
python track_align_gui.py       # 独立GUI版本
```

## 处理示例

### 处理前（第二轨道）
```
片段1: 0.000s - 2.300s
片段2: 2.300s - 3.900s
[间隙: 1.767s]
片段3: 5.667s - 6.767s
片段4: 6.767s - 8.667s
[间隙: 2.867s]
片段5: 8.667s - 12.800s
```

### 处理后（第二轨道）
```
片段1: 0.000s - 2.300s
片段2: 2.300s - 3.900s
片段3: 3.900s - 5.000s  ← 向左移动1.767s
片段4: 5.000s - 6.900s  ← 向左移动1.767s
片段5: 6.900s - 11.033s ← 向左移动4.633s
```

## 技术细节

### 时间单位处理
- 剪映内部使用微秒（μs）作为时间单位
- 显示时转换为秒（s）便于理解
- 计算时保持微秒精度确保准确性

### 文件备份机制
- 自动创建带时间戳的备份文件
- 格式：`draft_content_backup_YYYYMMDD_HHMMSS.json`
- 确保原始数据安全

### 错误处理
- 文件不存在检查
- JSON格式验证
- 轨道结构完整性验证
- 异常情况回滚机制

## 集成到项目

该功能已完全集成到现有的视频编辑工具项目中：

### 文件结构
```
├── track_left_align.py      # 命令行版本
├── track_align_gui.py       # GUI版本
├── analyze_draft.py         # 草稿分析工具
└── 轨道左对齐功能说明.md    # 本说明文档
```

### 与现有功能的关系
- **时间戳提取**：可配合使用，先提取再对齐
- **字幕匹配**：对齐后的轨道更便于字幕匹配
- **视频轨道写入**：为后续轨道写入提供整齐的基础

## 注意事项

1. **备份重要性**：虽然工具会自动备份，但建议手动备份重要项目
2. **剪映版本**：适用于支持JSON格式草稿的剪映版本
3. **文件路径**：确保草稿路径中包含中文字符时的正确编码
4. **操作顺序**：建议在完成基础剪辑后再执行左对齐

## 扩展功能

### 可能的增强
- 右对齐功能
- 居中对齐功能
- 自定义间隙保留
- 批量处理多个草稿
- 轨道选择性对齐

### 性能优化
- 大型项目的处理优化
- 内存使用优化
- 处理进度显示

## 总结

轨道左对齐功能为剪映用户提供了一个强大而安全的工具，用于整理和优化视频轨道布局。通过智能的间隙检测和精确的位置调整，帮助用户快速实现专业的时间线管理。

该功能的设计充分考虑了用户体验和数据安全，提供了直观的GUI界面和详细的操作反馈，是视频编辑工作流程中的有力助手。
