#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试时间戳导入功能
"""

from timestamp_importer import TimestampImporter

def test_timestamp_importer():
    """测试时间戳导入功能"""
    print("=" * 60)
    print("测试时间戳导入功能")
    print("=" * 60)
    
    importer = TimestampImporter()
    
    # 测试数据
    test_data = """00:33:33,531 --> 00:33:46,567
00:33:55,133 --> 00:34:26,132
00:02:20,900 --> 00:02:51,000
001 33:32.130-33:33.530
002 34:26.132-34:55.133
1. 33:33.530-33:42.670
2. 34:55.133-35:26.132"""
    
    print("📝 测试数据：")
    print(test_data)
    print()
    
    # 解析时间戳
    timestamps = importer.parse_timestamps(test_data)
    
    print(f"✅ 解析完成，共 {len(timestamps)} 个时间戳")
    print()
    
    # 验证时间戳
    importer.validate_timestamps(timestamps)
    print()
    
    # 显示统计信息
    stats = importer.display_statistics(timestamps)
    print(stats)
    print()
    
    # 显示解析结果
    print("📋 解析结果：")
    print("-" * 40)
    for entry in timestamps:
        print(entry)
    print()
    
    # 转换为SRT格式
    srt_content = importer.convert_to_srt_format(timestamps)
    print("📄 SRT格式输出：")
    print("-" * 40)
    print(srt_content[:500] + "..." if len(srt_content) > 500 else srt_content)

if __name__ == "__main__":
    test_timestamp_importer()
    input("\n按回车键退出...")
