#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能字幕匹配程序 - 基于预训练语言模型
使用sentence-transformers进行语义匹配
"""

import re
import os
import logging
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import datetime

try:
    from sentence_transformers import SentenceTransformer, util
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("警告: sentence-transformers未安装，请运行: pip install sentence-transformers")

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("警告: torch未安装，请运行: pip install torch")


@dataclass
class SubtitleEntry:
    """字幕条目数据结构"""
    sequence: int
    start_time: str
    end_time: str
    text: str
    normalized_text: str = ""


@dataclass
class MatchResult:
    """匹配结果数据结构"""
    final_text: str
    matched_sequences: List[int]
    confidence_score: float = 0.0
    match_method: str = ""


class AISubtitleMatcher:
    """基于AI模型的智能字幕匹配器"""
    
    def __init__(self, model_name: str = "paraphrase-multilingual-MiniLM-L12-v2"):
        """
        初始化AI字幕匹配器
        
        Args:
            model_name: 使用的sentence-transformers模型名称
        """
        self.file_a_entries: List[str] = []
        self.file_b_entries: Dict[int, SubtitleEntry] = {}
        self.match_results: List[MatchResult] = []
        self.logger = self._setup_logger()
        
        # AI模型相关
        self.model_name = model_name
        self.model = None
        self.original_embeddings = None
        self.original_sequences = []
        
        # 匹配参数
        self.semantic_threshold = 0.4  # 语义相似度阈值
        self.context_window = 10  # 上下文窗口大小
        self.max_jump_distance = 100  # 最大跳跃距离
        
        # 初始化AI模型
        self._initialize_model()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        log_filename = "AI匹配日志.log"
        
        logger = logging.getLogger('AISubtitleMatcher')
        logger.setLevel(logging.DEBUG)
        
        if logger.handlers:
            logger.handlers.clear()
        
        # 文件处理器
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        logger.info("=" * 60)
        logger.info("AI智能字幕匹配程序启动")
        logger.info(f"日志文件: {log_filename}")
        logger.info("=" * 60)
        
        return logger
    
    def _initialize_model(self):
        """初始化AI模型"""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            self.logger.error("sentence-transformers未安装，无法使用AI匹配功能")
            return False
        
        try:
            self.logger.info(f"正在加载AI模型: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            
            # 检查是否有GPU可用
            device = 'cuda' if TORCH_AVAILABLE and torch.cuda.is_available() else 'cpu'
            self.model = self.model.to(device)
            
            self.logger.info(f"AI模型加载成功，使用设备: {device}")
            return True
            
        except Exception as e:
            self.logger.error(f"AI模型加载失败: {e}")
            self.model = None
            return False
    
    def normalize_text(self, text: str) -> str:
        """文本规范化处理"""
        if not text:
            return ""
        # 去除首尾空格，将多个连续空格或换行符压缩成一个空格
        normalized = re.sub(r'\s+', ' ', text.strip())
        return normalized
    
    def parse_subtitle_file(self, file_path: str, is_file_a: bool = True) -> bool:
        """
        解析字幕文件
        
        Args:
            file_path: 字幕文件路径
            is_file_a: 是否为文件A（二次剪辑）
        """
        try:
            file_type = "最终剪辑" if is_file_a else "原始素材"
            self.logger.info(f"开始解析文件{file_type}: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if is_file_a:
                self._parse_file_a(lines)
                self.logger.info(f"成功解析文件A（{file_type}），共{len(self.file_a_entries)}个台词条目")
            else:
                self._parse_file_b(lines)
                self.logger.info(f"成功解析文件B（{file_type}），共{len(self.file_b_entries)}个镜头条目")
            
            return True
            
        except Exception as e:
            self.logger.error(f"解析文件失败: {e}")
            return False
    
    def _parse_file_a(self, lines: List[str]):
        """解析文件A（二次剪辑）"""
        self.file_a_entries = []
        i = 0
        
        while i < len(lines):
            line = lines[i].strip()
            
            if line.isdigit():  # 序号行
                # 跳过时间戳行
                i += 2
                if i < len(lines):
                    text = lines[i].strip()
                    if text:  # 只添加非空台词
                        self.file_a_entries.append(text)
            i += 1
    
    def _parse_file_b(self, lines: List[str]):
        """解析文件B（原素材）"""
        self.file_b_entries = {}
        i = 0
        
        while i < len(lines):
            line = lines[i].strip()
            
            if line.isdigit():  # 序号行
                sequence = int(line)
                i += 1
                
                if i < len(lines) and ' --> ' in lines[i]:
                    timestamp = lines[i].strip()
                    start_time, end_time = timestamp.split(' --> ')
                    i += 1
                    
                    if i < len(lines):
                        text = lines[i].strip()
                        normalized_text = self.normalize_text(text)
                        
                        entry = SubtitleEntry(
                            sequence=sequence,
                            start_time=start_time,
                            end_time=end_time,
                            text=text,
                            normalized_text=normalized_text
                        )
                        self.file_b_entries[sequence] = entry
            i += 1
    
    def prepare_embeddings(self):
        """预计算原素材的嵌入向量"""
        if self.model is None:
            self.logger.error("AI模型未初始化，无法计算嵌入向量")
            return False
        
        try:
            self.logger.info("正在计算原素材的嵌入向量...")
            
            # 准备文本列表
            texts = []
            sequences = []
            
            for seq, entry in sorted(self.file_b_entries.items()):
                texts.append(entry.text)
                sequences.append(seq)
            
            # 计算嵌入向量
            self.original_embeddings = self.model.encode(texts, convert_to_tensor=True)
            self.original_sequences = sequences
            
            self.logger.info(f"嵌入向量计算完成，处理了{len(texts)}个文本")
            return True
            
        except Exception as e:
            self.logger.error(f"嵌入向量计算失败: {e}")
            return False

    def semantic_search(self, query_text: str, last_matched_seq: int = -1, top_k: int = 10) -> Optional[Tuple[int, float]]:
        """
        基于语义相似度的搜索

        Args:
            query_text: 查询文本
            last_matched_seq: 上一个匹配的序号
            top_k: 返回前k个最相似的结果

        Returns:
            (序号, 相似度分数) 或 None
        """
        if self.model is None or self.original_embeddings is None:
            return None

        try:
            # 计算查询文本的嵌入向量
            query_embedding = self.model.encode([query_text], convert_to_tensor=True)

            # 计算与所有原素材的相似度
            similarities = util.cos_sim(query_embedding, self.original_embeddings)[0]

            # 获取top-k最相似的结果
            top_results = torch.topk(similarities, k=min(top_k, len(similarities)))

            # 应用上下文约束和阈值过滤
            valid_candidates = []

            for score, idx in zip(top_results.values, top_results.indices):
                score = score.item()
                idx = idx.item()
                sequence = self.original_sequences[idx]

                # 相似度阈值过滤
                if score < self.semantic_threshold:
                    continue

                # 上下文约束：避免大幅跳跃
                if last_matched_seq != -1:
                    jump_distance = abs(sequence - last_matched_seq)
                    if jump_distance > self.max_jump_distance:
                        continue

                    # 避免大幅回退
                    if sequence < last_matched_seq - 20:
                        score *= 0.5  # 降低回退的权重

                valid_candidates.append((sequence, score))

            if not valid_candidates:
                return None

            # 选择最佳候选（综合相似度和位置）
            best_candidate = self._select_best_candidate(valid_candidates, last_matched_seq)

            if best_candidate:
                seq, score = best_candidate
                self.logger.debug(f"语义搜索: '{query_text[:20]}...' -> 序号{seq} (相似度: {score:.3f})")
                return best_candidate

            return None

        except Exception as e:
            self.logger.error(f"语义搜索失败: {e}")
            return None

    def _select_best_candidate(self, candidates: List[Tuple[int, float]], last_matched_seq: int) -> Optional[Tuple[int, float]]:
        """
        选择最佳候选，综合考虑相似度和位置连续性
        """
        if not candidates:
            return None

        if last_matched_seq == -1:
            # 如果没有前一个匹配，选择相似度最高的
            return max(candidates, key=lambda x: x[1])

        # 计算综合得分
        scored_candidates = []

        for seq, similarity in candidates:
            # 位置连续性得分
            distance = abs(seq - last_matched_seq)

            if distance <= 5:
                position_score = 1.0
            elif distance <= 20:
                position_score = 0.8
            elif distance <= 50:
                position_score = 0.6
            else:
                position_score = 0.3

            # 综合得分 (70% 相似度 + 30% 位置)
            final_score = 0.7 * similarity + 0.3 * position_score
            scored_candidates.append((seq, similarity, final_score))

        # 选择综合得分最高的
        best = max(scored_candidates, key=lambda x: x[2])
        return (best[0], best[1])  # 返回 (序号, 原始相似度)

    def perform_ai_matching(self) -> List[MatchResult]:
        """
        执行AI语义匹配
        """
        self.logger.info("开始执行AI语义匹配算法")

        if self.model is None:
            self.logger.error("AI模型未初始化，无法执行匹配")
            return []

        # 预计算嵌入向量
        if not self.prepare_embeddings():
            self.logger.error("嵌入向量计算失败，无法执行匹配")
            return []

        self.match_results = []
        last_matched_seq = -1
        successful_matches = 0
        failed_matches = 0

        for i, target_text in enumerate(self.file_a_entries):
            # 执行语义搜索
            match_result = self.semantic_search(target_text, last_matched_seq)

            if match_result:
                seq, confidence = match_result
                matched_sequences = [seq]
                last_matched_seq = seq
                successful_matches += 1

                result = MatchResult(
                    final_text=target_text,
                    matched_sequences=matched_sequences,
                    confidence_score=confidence,
                    match_method="AI语义匹配"
                )

                self.logger.debug(f"台词{i+1}: '{target_text[:20]}...' -> 序号{seq} (置信度: {confidence:.3f})")

            else:
                # 匹配失败
                result = MatchResult(
                    final_text=target_text,
                    matched_sequences=[],
                    confidence_score=0.0,
                    match_method="匹配失败"
                )
                failed_matches += 1

                self.logger.debug(f"台词{i+1}: '{target_text[:20]}...' -> 匹配失败")

            self.match_results.append(result)

        # 记录统计信息
        self.logger.info(f"AI匹配完成统计:")
        self.logger.info(f"  总台词数: {len(self.file_a_entries)}")
        self.logger.info(f"  成功匹配: {successful_matches}")
        self.logger.info(f"  匹配失败: {failed_matches}")
        self.logger.info(f"  匹配成功率: {successful_matches/len(self.file_a_entries)*100:.1f}%")

        return self.match_results

    def generate_timestamps(self) -> List[str]:
        """
        根据匹配结果生成时间戳
        """
        if not self.match_results:
            self.logger.warning("没有匹配结果，无法生成时间戳")
            return []

        timestamps = []

        for i, result in enumerate(self.match_results):
            if result.matched_sequences:
                seq = result.matched_sequences[0]  # AI匹配通常是单个序号

                if seq in self.file_b_entries:
                    entry = self.file_b_entries[seq]
                    timestamp = f"{entry.start_time} --> {entry.end_time}"
                    timestamps.append(timestamp)

                    self.logger.debug(f"时间戳{i+1}: 序号{seq} -> {timestamp}")
                else:
                    self.logger.warning(f"序号{seq}不存在于原素材中")

        self.logger.info(f"时间戳生成完成，共{len(timestamps)}个时间戳")
        return timestamps

    def save_results(self, output_file: str = "AI匹配结果.txt") -> bool:
        """
        保存匹配结果到文件
        """
        try:
            timestamps = self.generate_timestamps()

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("AI智能字幕匹配结果\n")
                f.write("=" * 50 + "\n")
                f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"匹配算法: AI语义匹配 ({self.model_name})\n")
                f.write(f"总台词数: {len(self.file_a_entries)}\n")
                f.write(f"成功匹配: {len(timestamps)}\n")
                f.write(f"匹配成功率: {len(timestamps)/len(self.file_a_entries)*100:.1f}%\n")
                f.write("=" * 50 + "\n\n")

                for i, timestamp in enumerate(timestamps, 1):
                    f.write(f"{i}. {timestamp}\n")

            self.logger.info(f"匹配结果已保存到: {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
            return False

    def process_files(self, file_a_path: str, file_b_path: str, output_file: str = "AI匹配结果.txt") -> Tuple[bool, List[str], str]:
        """
        处理字幕文件的完整流程

        Args:
            file_a_path: 二次剪辑字幕文件路径
            file_b_path: 原素材字幕文件路径
            output_file: 输出文件路径

        Returns:
            (是否成功, 时间戳列表, 结果消息)
        """
        try:
            self.logger.info("开始处理文件")
            self.logger.info(f"文件A路径: {file_a_path}")
            self.logger.info(f"文件B路径: {file_b_path}")

            # 解析文件
            if not self.parse_subtitle_file(file_a_path, is_file_a=True):
                return False, [], "文件A解析失败"

            if not self.parse_subtitle_file(file_b_path, is_file_a=False):
                return False, [], "文件B解析失败"

            # 执行匹配
            match_results = self.perform_ai_matching()
            if not match_results:
                return False, [], "AI匹配失败"

            # 生成时间戳
            timestamps = self.generate_timestamps()

            # 保存结果
            if self.save_results(output_file):
                success_rate = len(timestamps) / len(self.file_a_entries) * 100
                message = f"AI匹配完成！成功率: {success_rate:.1f}% ({len(timestamps)}/{len(self.file_a_entries)})"
                self.logger.info(message)
                return True, timestamps, message
            else:
                return False, timestamps, "结果保存失败"

        except Exception as e:
            error_msg = f"处理过程中发生错误: {e}"
            self.logger.error(error_msg)
            return False, [], error_msg


# 使用示例
if __name__ == "__main__":
    # 创建AI匹配器
    ai_matcher = AISubtitleMatcher()

    # 处理文件
    success, timestamps, message = ai_matcher.process_files(
        "字幕与时间戳合并/二次剪辑合并结果.txt",
        "字幕与时间戳合并/原素材合并结果.txt",
        "AI匹配结果.txt"
    )

    if success:
        print(f"✅ {message}")
        print(f"生成了 {len(timestamps)} 个时间戳")
    else:
        print(f"❌ {message}")
