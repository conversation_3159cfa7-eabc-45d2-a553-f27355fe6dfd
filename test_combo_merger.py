#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试组合字幕合并功能
"""

import os
from subtitle_timestamp_merger import SubtitleTimestampMerger

def create_test_files():
    """创建测试文件"""
    print("📋 创建测试文件...")
    
    # 创建测试目录
    os.makedirs("提取时间戳", exist_ok=True)
    os.makedirs("字幕与时间戳合并", exist_ok=True)
    
    # 第一组测试文件
    subtitle1_content = """1
00:00:00,000 --> 00:00:03,000
第一组字幕第一条

2
00:00:03,000 --> 00:00:06,000
第一组字幕第二条

3
00:00:06,000 --> 00:00:09,000
第一组字幕第三条"""

    timestamp1_content = """1
00:00:00,000 --> 00:00:03,000

2
00:00:03,000 --> 00:00:06,000

3
00:00:06,000 --> 00:00:09,000
"""

    # 第二组测试文件
    subtitle2_content = """1
00:00:10,000 --> 00:00:13,000
第二组字幕第一条

2
00:00:13,000 --> 00:00:16,000
第二组字幕第二条

3
00:00:16,000 --> 00:00:19,000
第二组字幕第三条"""

    timestamp2_content = """1
00:00:10,000 --> 00:00:13,000

2
00:00:13,000 --> 00:00:16,000

3
00:00:16,000 --> 00:00:19,000
"""

    # 保存测试文件
    test_files = [
        ("提取时间戳/第一组字幕文件.txt", subtitle1_content),
        ("提取时间戳/第一组时间戳文件.txt", timestamp1_content),
        ("提取时间戳/第二组字幕文件.txt", subtitle2_content),
        ("提取时间戳/第二组时间戳文件.txt", timestamp2_content),
    ]
    
    for file_path, content in test_files:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ 创建: {file_path}")
    
    return test_files

def test_single_merge():
    """测试单组合并"""
    print("\n" + "=" * 60)
    print("测试单组合并功能")
    print("=" * 60)
    
    # 使用第一组文件进行单组合并测试
    merger = SubtitleTimestampMerger()
    
    subtitle_file = "提取时间戳/第一组字幕文件.txt"
    timestamp_file = "提取时间戳/第一组时间戳文件.txt"
    output_file = "字幕与时间戳合并/单组合并测试结果.txt"
    
    print(f"📁 字幕文件: {subtitle_file}")
    print(f"📁 时间戳文件: {timestamp_file}")
    print(f"📁 输出文件: {output_file}")
    
    success, result = merger.process_files(subtitle_file, timestamp_file, output_file)
    
    print(f"\n📊 合并结果:")
    print(f"  成功: {'✅' if success else '❌'}")
    print(f"  结果: {result}")
    
    if success and os.path.exists(output_file):
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"\n📋 输出内容:")
        print(content[:300] + "..." if len(content) > 300 else content)
    
    return success

def test_combo_merge():
    """测试组合合并"""
    print("\n" + "=" * 60)
    print("测试组合合并功能")
    print("=" * 60)
    
    # 第一组合并
    merger1 = SubtitleTimestampMerger()
    temp_output1 = "temp_combo_result1.txt"
    
    success1, result1 = merger1.process_files(
        "提取时间戳/第一组字幕文件.txt",
        "提取时间戳/第一组时间戳文件.txt",
        temp_output1
    )
    
    print(f"📊 第一组合并:")
    print(f"  成功: {'✅' if success1 else '❌'}")
    print(f"  结果: {result1}")
    
    if not success1:
        return False
    
    # 第二组合并
    merger2 = SubtitleTimestampMerger()
    temp_output2 = "temp_combo_result2.txt"
    
    success2, result2 = merger2.process_files(
        "提取时间戳/第二组字幕文件.txt",
        "提取时间戳/第二组时间戳文件.txt",
        temp_output2
    )
    
    print(f"\n📊 第二组合并:")
    print(f"  成功: {'✅' if success2 else '❌'}")
    print(f"  结果: {result2}")
    
    if not success2:
        return False
    
    # 组合两个结果
    final_output = "字幕与时间戳合并/组合合并测试结果.txt"
    success_combo = combine_subtitle_files(temp_output1, temp_output2, final_output)
    
    print(f"\n📊 组合结果:")
    print(f"  成功: {'✅' if success_combo else '❌'}")
    
    if success_combo and os.path.exists(final_output):
        with open(final_output, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"\n📋 最终输出内容:")
        print(content[:500] + "..." if len(content) > 500 else content)
        
        # 统计信息
        lines = content.split('\n')
        subtitle_count = sum(1 for line in lines if line.strip() and not line.strip().isdigit() and ' --> ' not in line)
        print(f"\n📊 统计信息: 共 {subtitle_count} 条字幕")
    
    # 清理临时文件
    try:
        os.remove(temp_output1)
        os.remove(temp_output2)
        print(f"\n🧹 临时文件已清理")
    except:
        pass
    
    return success_combo

def combine_subtitle_files(file1: str, file2: str, output_file: str) -> bool:
    """组合两个字幕文件"""
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as out_f:
            # 读取第一个文件
            if os.path.exists(file1):
                with open(file1, 'r', encoding='utf-8') as f1:
                    content1 = f1.read().strip()
                    if content1:
                        out_f.write(content1)
                        out_f.write("\n\n")
            
            # 读取第二个文件
            if os.path.exists(file2):
                with open(file2, 'r', encoding='utf-8') as f2:
                    content2 = f2.read().strip()
                    if content2:
                        # 需要重新编号第二个文件的序号
                        lines = content2.split('\n')
                        current_index = 1
                        
                        # 计算第一个文件的最大序号
                        if os.path.exists(file1):
                            with open(file1, 'r', encoding='utf-8') as f1:
                                file1_content = f1.read()
                                file1_lines = file1_content.split('\n')
                                for line in file1_lines:
                                    if line.strip().isdigit():
                                        current_index = max(current_index, int(line.strip()) + 1)
                        
                        # 重新编号第二个文件
                        i = 0
                        while i < len(lines):
                            line = lines[i].strip()
                            if line.isdigit():
                                out_f.write(f"{current_index}\n")
                                current_index += 1
                            else:
                                out_f.write(f"{line}\n")
                            i += 1
        
        return True
        
    except Exception as e:
        print(f"❌ 组合文件失败: {e}")
        return False

def test_auto_fill():
    """测试智能填充功能"""
    print("\n" + "=" * 60)
    print("测试智能填充功能")
    print("=" * 60)
    
    extract_dir = "提取时间戳"
    
    if not os.path.exists(extract_dir):
        print("❌ 提取时间戳目录不存在")
        return False
    
    # 查找可能的文件
    subtitle_files = []
    timestamp_files = []
    
    for file in os.listdir(extract_dir):
        if file.endswith('.txt'):
            file_path = os.path.join(extract_dir, file)
            if '字幕' in file or 'subtitle' in file.lower():
                subtitle_files.append(file_path)
            elif '时间戳' in file or 'timestamp' in file.lower():
                timestamp_files.append(file_path)
    
    print(f"📊 发现的文件:")
    print(f"  字幕文件: {len(subtitle_files)} 个")
    for f in subtitle_files:
        print(f"    - {f}")
    print(f"  时间戳文件: {len(timestamp_files)} 个")
    for f in timestamp_files:
        print(f"    - {f}")
    
    # 模拟智能填充
    if len(subtitle_files) >= 2 and len(timestamp_files) >= 2:
        print(f"\n✅ 智能填充成功:")
        print(f"  第一组字幕: {subtitle_files[0]}")
        print(f"  第一组时间戳: {timestamp_files[0]}")
        print(f"  第二组字幕: {subtitle_files[1]}")
        print(f"  第二组时间戳: {timestamp_files[1]}")
        return True
    else:
        print(f"\n❌ 智能填充失败: 文件数量不足")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    test_files = [
        "提取时间戳/第一组字幕文件.txt",
        "提取时间戳/第一组时间戳文件.txt",
        "提取时间戳/第二组字幕文件.txt",
        "提取时间戳/第二组时间戳文件.txt",
        "字幕与时间戳合并/单组合并测试结果.txt",
        "字幕与时间戳合并/组合合并测试结果.txt",
    ]
    
    for file_path in test_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"  ✅ 删除: {file_path}")
        except:
            print(f"  ❌ 删除失败: {file_path}")

def main():
    """主测试函数"""
    print("🧪 组合字幕合并功能测试")
    print("=" * 60)
    
    # 创建测试文件
    create_test_files()
    
    # 测试智能填充
    auto_fill_success = test_auto_fill()
    
    # 测试单组合并
    single_success = test_single_merge()
    
    # 测试组合合并
    combo_success = test_combo_merge()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 智能填充: {'通过' if auto_fill_success else '失败'}")
    print(f"✅ 单组合并: {'通过' if single_success else '失败'}")
    print(f"✅ 组合合并: {'通过' if combo_success else '失败'}")
    
    if auto_fill_success and single_success and combo_success:
        print("\n🎉 组合字幕合并功能测试成功！")
        print("📋 功能特点:")
        print("1. ✅ 支持单组和组合两种合并模式")
        print("2. ✅ 智能填充功能可自动选择文件")
        print("3. ✅ 组合模式可合并两组文件")
        print("4. ✅ 自动重新编号，避免序号冲突")
        print("5. ✅ 完整的错误处理和验证")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    # 询问是否清理测试文件
    response = input("\n是否清理测试文件？(y/n): ")
    if response.lower() == 'y':
        cleanup_test_files()
    
    print("=" * 60)

if __name__ == "__main__":
    main()
