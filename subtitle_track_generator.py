#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
剪映字幕轨道生成器
基于分析的7月11日项目字幕格式
"""

import json
import uuid
import os
from datetime import datetime
from typing import List, Dict, Any

class SubtitleTrackGenerator:
    """剪映字幕轨道生成器"""
    
    def __init__(self):
        self.default_font_path = "D:/JianyingPro/5.9.0.11632/Resources/Font/SystemFont/zh-hans.ttf"
        self.default_font_size = 5.0
        self.default_text_size = 30
        self.default_color = "#FFFFFF"
    
    def create_subtitle_material(self, text: str, material_id: str = None) -> Dict[str, Any]:
        """创建字幕素材"""
        if material_id is None:
            material_id = str(uuid.uuid4()).upper()
        
        # 创建富文本内容
        rich_text_content = {
            "styles": [{
                "fill": {
                    "alpha": 1.0,
                    "content": {
                        "render_type": "solid",
                        "solid": {
                            "alpha": 1.0,
                            "color": [1.0, 1.0, 1.0]
                        }
                    }
                },
                "font": {
                    "id": "",
                    "path": self.default_font_path
                },
                "range": [0, len(text)],
                "size": self.default_font_size
            }],
            "text": text
        }
        
        # 字幕素材完整结构
        material = {
            "add_type": 1,
            "alignment": 1,
            "background_alpha": 1.0,
            "background_color": "",
            "background_height": 0.14,
            "background_horizontal_offset": 0.0,
            "background_round_radius": 0.0,
            "background_style": 0,
            "background_vertical_offset": 0.0,
            "background_width": 0.14,
            "base_content": "",
            "bold_width": 0.0,
            "border_alpha": 1.0,
            "border_color": "",
            "border_width": 0.08,
            "caption_template_info": {
                "category_id": "",
                "category_name": "",
                "effect_id": "",
                "is_new": False,
                "path": "",
                "request_id": "",
                "resource_id": "",
                "resource_name": "",
                "source_platform": 0
            },
            "check_flag": 7,
            "combo_info": {
                "text_templates": []
            },
            "content": json.dumps(rich_text_content, ensure_ascii=False),
            "fixed_height": -1.0,
            "fixed_width": -1.0,
            "font_category_id": "",
            "font_category_name": "",
            "font_id": "",
            "font_name": "",
            "font_path": self.default_font_path,
            "font_resource_id": "",
            "font_size": self.default_font_size,
            "font_source_platform": 0,
            "font_team_id": "",
            "font_title": "none",
            "font_url": "",
            "fonts": [],
            "force_apply_line_max_width": False,
            "global_alpha": 1.0,
            "group_id": "",
            "has_shadow": False,
            "id": material_id,
            "initial_scale": 1.0,
            "inner_padding": -1.0,
            "is_rich_text": False,
            "italic_degree": 0,
            "ktv_color": "",
            "language": "",
            "layer_weight": 1,
            "letter_spacing": 0.0,
            "line_feed": 1,
            "line_max_width": 0.82,
            "line_spacing": 0.02,
            "multi_language_current": "none",
            "name": "",
            "original_size": [],
            "preset_category": "",
            "preset_category_id": "",
            "preset_has_set_alignment": False,
            "preset_id": "",
            "preset_index": 0,
            "preset_name": "",
            "recognize_task_id": str(uuid.uuid4()),
            "recognize_type": 0,
            "relevance_segment": [],
            "shadow_alpha": 0.9,
            "shadow_angle": -45.0,
            "shadow_color": "",
            "shadow_distance": 5.0,
            "shadow_point": {
                "x": 0.6363961030678928,
                "y": -0.6363961030678928
            },
            "shadow_smoothing": 0.45,
            "shape_clip_x": False,
            "shape_clip_y": False,
            "source_from": "",
            "style_name": "",
            "sub_type": 0,
            "subtitle_keywords": None,
            "subtitle_template_original_fontsize": 0.0,
            "text_alpha": 1.0,
            "text_color": self.default_color,
            "text_curve": None,
            "text_preset_resource_id": "",
            "text_size": self.default_text_size,
            "text_to_audio_ids": [],
            "tts_auto_update": False,
            "type": "subtitle",
            "typesetting": 0,
            "underline": False,
            "underline_offset": 0.22,
            "underline_width": 0.05,
            "use_effect_default_color": True,
            "words": {
                "end_time": [],
                "start_time": [],
                "text": []
            }
        }
        
        return material
    
    def create_subtitle_segment(self, start_time_us: int, duration_us: int, material_id: str) -> Dict[str, Any]:
        """创建字幕片段"""
        segment = {
            "cartoon": False,
            "clip": {
                "alpha": 1.0,
                "flip": {
                    "horizontal": False,
                    "vertical": False
                },
                "rotation": 0.0,
                "scale": {
                    "x": 1.0,
                    "y": 1.0
                },
                "transform": {
                    "x": 0.0,
                    "y": 0.0
                }
            },
            "common_keyframes": [],
            "enable_adjust": True,
            "enable_color_curves": True,
            "enable_color_match_adjust": False,
            "enable_color_wheels": True,
            "enable_lut": True,
            "enable_smart_color_adjust": False,
            "extra_material_refs": [],
            "group_id": "",
            "hdr_settings": {
                "intensity": 1.0,
                "mode": 1,
                "nits": 1000
            },
            "id": str(uuid.uuid4()).upper(),
            "intensifies_audio": False,
            "is_placeholder": False,
            "is_tone_modify": False,
            "keyframe_refs": [],
            "last_nonzero_volume": 1.0,
            "material_id": material_id,
            "render_index": 4000000,
            "reverse": False,
            "source_timerange": {
                "duration": duration_us,
                "start": 0
            },
            "speed": 1.0,
            "target_timerange": {
                "duration": duration_us,
                "start": start_time_us
            },
            "template_id": "",
            "template_scene": "default",
            "track_attribute": 0,
            "track_render_index": 0,
            "uniform_scale": {
                "on": True,
                "value": 1.0
            },
            "visible": True,
            "volume": 1.0
        }
        
        return segment
    
    def create_subtitle_track(self, track_id: str = None) -> Dict[str, Any]:
        """创建字幕轨道"""
        if track_id is None:
            track_id = str(uuid.uuid4()).upper()
        
        track = {
            "attribute": 0,
            "flag": 1,
            "id": track_id,
            "is_default_name": True,
            "name": "",
            "segments": [],
            "type": "text"
        }
        
        return track
    
    def add_subtitle_to_project(self, project_path: str, subtitle_entries: List[Dict], backup: bool = True) -> bool:
        """将字幕添加到剪映项目"""
        try:
            # 备份原文件
            if backup:
                backup_path = f"{project_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                with open(project_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"💾 备份文件: {backup_path}")
            
            # 读取项目文件
            with open(project_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            # 创建字幕素材
            materials = project_data.setdefault('materials', {})
            texts = materials.setdefault('texts', [])
            
            # 创建字幕轨道
            tracks = project_data.setdefault('tracks', [])
            subtitle_track = self.create_subtitle_track()
            
            print(f"📝 开始创建字幕轨道...")
            print(f"📊 字幕条目数量: {len(subtitle_entries)}")
            
            for i, entry in enumerate(subtitle_entries):
                # 创建字幕素材
                material_id = str(uuid.uuid4()).upper()
                text_content = entry.get('text', f'字幕{i+1}')
                start_time = entry.get('start_time', 0.0)  # 秒
                duration = entry.get('duration', 1.0)  # 秒
                
                # 转换为微秒（剪映使用微秒）
                start_time_us = int(start_time * 1000000)
                duration_us = int(duration * 1000000)
                
                # 创建素材
                material = self.create_subtitle_material(text_content, material_id)
                texts.append(material)
                
                # 创建片段
                segment = self.create_subtitle_segment(start_time_us, duration_us, material_id)
                subtitle_track['segments'].append(segment)
                
                print(f"   📝 字幕{i+1}: \"{text_content}\" ({start_time:.3f}s-{start_time+duration:.3f}s)")
            
            # 添加字幕轨道到项目
            tracks.append(subtitle_track)
            
            # 更新项目时长
            if subtitle_entries:
                last_entry = subtitle_entries[-1]
                last_end_time = last_entry.get('start_time', 0) + last_entry.get('duration', 1)
                project_duration_us = int(last_end_time * 1000000)
                
                current_duration = project_data.get('duration', 0)
                if project_duration_us > current_duration:
                    project_data['duration'] = project_duration_us
                    print(f"📊 更新项目时长: {last_end_time:.3f}秒")
            
            # 保存项目文件
            with open(project_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, separators=(',', ':'))
            
            print(f"✅ 字幕轨道创建成功")
            print(f"🎬 请在剪映中重新打开项目查看字幕轨道")
            
            return True
            
        except Exception as e:
            print(f"❌ 创建字幕轨道失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """测试函数"""
    generator = SubtitleTrackGenerator()
    
    # 测试数据
    test_entries = [
        {"text": "测试字幕1", "start_time": 0.0, "duration": 2.0},
        {"text": "测试字幕2", "start_time": 2.0, "duration": 3.0},
        {"text": "测试字幕3", "start_time": 5.0, "duration": 2.5}
    ]
    
    project_path = "D:/JianyingPro Drafts/7月20日/draft_content.json"
    
    success = generator.add_subtitle_to_project(project_path, test_entries)
    
    if success:
        print("✅ 测试成功")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
