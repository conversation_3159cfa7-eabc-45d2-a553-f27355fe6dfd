#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试相同时间戳合并功能
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from subtitle_timestamp_merger import SubtitleTimestampMerger

def test_merge_same_timestamps():
    """测试相同时间戳合并功能"""
    print("=" * 60)
    print("测试相同时间戳合并功能")
    print("=" * 60)
    
    # 创建合并器实例
    merger = SubtitleTimestampMerger()
    
    # 使用指定的文件
    subtitle_file = r'D:\二次剪辑与原视频匹配\字幕与时间戳合并\原素材字幕.txt'
    timestamp_file = r'D:\二次剪辑与原视频匹配\字幕与时间戳合并\原素材时间戳.txt'
    output_file = r'D:\二次剪辑与原视频匹配\字幕与时间戳合并\合并时间戳测试结果.txt'
    
    print(f"字幕文件: {subtitle_file}")
    print(f"时间戳文件: {timestamp_file}")
    print(f"输出文件: {output_file}")
    print()
    
    # 执行处理
    print("开始处理...")
    success, result = merger.process_files(subtitle_file, timestamp_file, output_file)
    
    if success:
        print("✅ 处理成功！")
        print(f"返回结果: {result}")
        print()
        
        # 读取并显示输出文件的前30行
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print("📊 输出结果前30行：")
            for i, line in enumerate(lines[:30], 1):
                print(f"{i:2d}: {line.rstrip()}")
            
            if len(lines) > 30:
                print(f"... (共{len(lines)}行，只显示前30行)")
            
            print()
            print("📈 处理统计：")
            print(f"• 字幕条目数: {len(merger.subtitle_entries)}")
            print(f"• 时间戳条目数: {len(merger.timestamp_entries)}")
            
            # 查找相同时间戳的示例
            print()
            print("🔍 查找相同时间戳的示例：")
            content = ''.join(lines)
            if "00:32:13,967 --> 00:32:21,833" in content:
                print("找到示例时间戳 00:32:13,967 --> 00:32:21,833")
                # 查找这个时间戳在结果中的情况
                for i, line in enumerate(lines):
                    if "00:32:13,967 --> 00:32:21,833" in line:
                        print(f"第{i+1}行: {line.rstrip()}")
                        if i+1 < len(lines):
                            print(f"第{i+2}行: {lines[i+1].rstrip()}")
                        if i+2 < len(lines):
                            print(f"第{i+3}行: {lines[i+2].rstrip()}")
                        print("---")
                        
        except Exception as e:
            print(f"❌ 读取输出文件失败: {e}")
    else:
        print(f"❌ 处理失败: {result}")

if __name__ == "__main__":
    test_merge_same_timestamps()
    input("\n按回车键退出...")
