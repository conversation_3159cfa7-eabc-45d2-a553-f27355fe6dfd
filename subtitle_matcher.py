#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能字幕匹配程序 - 核心算法模块
支持中文路径和复杂的字幕匹配逻辑
"""

import re
import os
import logging
import datetime
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba


@dataclass
class SubtitleEntry:
    """字幕条目数据结构"""
    sequence: int
    start_time: str
    end_time: str
    text: str
    normalized_text: str


@dataclass
class MatchResult:
    """匹配结果数据结构"""
    final_text: str
    matched_sequences: List[int]


class SubtitleMatcher:
    """智能字幕匹配器"""

    def __init__(self):
        self.file_a_entries: List[str] = []  # 最终剪辑版台词列表
        self.file_b_entries: Dict[int, SubtitleEntry] = {}  # 原始素材镜头字典
        self.match_results: List[MatchResult] = []
        self.logger = self._setup_logger()

        # 相似度匹配相关
        self.tfidf_vectorizer = None
        self.original_tfidf_matrix = None
        self.original_texts_list = []

        # 上下文感知参数
        self.context_window = 5  # 上下文窗口大小
        self.similarity_threshold = 0.3  # 相似度阈值
        self.context_weight = 0.3  # 上下文权重

    def _setup_logger(self):
        """设置日志记录器"""
        # 使用固定的日志文件名
        log_filename = "运行日志.log"

        # 创建logger
        logger = logging.getLogger('SubtitleMatcher')
        logger.setLevel(logging.DEBUG)

        # 避免重复添加handler
        if logger.handlers:
            logger.handlers.clear()

        # 创建文件handler（覆盖模式）
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)

        # 创建控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 创建formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 添加handler到logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        logger.info("=" * 60)
        logger.info("智能字幕匹配程序启动")
        logger.info(f"日志文件: {log_filename}")
        logger.info("=" * 60)

        return logger

    def normalize_text(self, text: str) -> str:
        """空格规范化处理：去除首尾空格，压缩内部多个空格为单个空格"""
        if not text:
            return ""
        # 去除首尾空格，将多个连续空格或换行符压缩成一个空格
        normalized = re.sub(r'\s+', ' ', text.strip())
        return normalized

    def flexible_text_match(self, target: str, source: str) -> bool:
        """
        灵活的文本匹配，处理标点符号和空格差异
        """
        # 移除标点符号和多余空格进行比较
        def clean_for_match(text):
            # 移除常见标点符号
            cleaned = re.sub(r'[。，、；：！？""''（）【】《》\.\,\;\:\!\?\"\'\(\)\[\]<>]', '', text)
            # 压缩所有空格为单个空格
            cleaned = re.sub(r'\s+', ' ', cleaned.strip())
            return cleaned

        target_clean = clean_for_match(target)
        source_clean = clean_for_match(source)

        # 先尝试直接匹配
        if target_clean in source_clean:
            return True

        # 如果直接匹配失败，尝试更激进的清理：移除所有空格
        def ultra_clean(text):
            # 移除所有标点符号和空格
            cleaned = re.sub(r'[。，、；：！？""''（）【】《》\.\,\;\:\!\?\"\'\(\)\[\]<>\s]', '', text)
            return cleaned

        target_ultra = ultra_clean(target)
        source_ultra = ultra_clean(source)

        return target_ultra in source_ultra

    def initialize_similarity_matching(self):
        """初始化相似度匹配所需的TF-IDF向量"""
        try:
            # 准备原素材文本列表
            self.original_texts_list = []
            sequence_mapping = {}

            for seq, entry in sorted(self.file_b_entries.items()):
                # 使用jieba分词处理中文
                segmented_text = ' '.join(jieba.cut(entry.text))
                self.original_texts_list.append(segmented_text)
                sequence_mapping[len(self.original_texts_list) - 1] = seq

            # 创建TF-IDF向量化器
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=2000,  # 增加特征数量
                ngram_range=(1, 3),  # 增加n-gram范围
                stop_words=None,  # 中文不使用英文停用词
                min_df=1,  # 最小文档频率
                max_df=0.98,  # 最大文档频率
                token_pattern=r'(?u)\b\w+\b|\.+',  # 包含标点符号
                lowercase=False  # 保持原始大小写
            )

            # 计算原素材的TF-IDF矩阵
            self.original_tfidf_matrix = self.tfidf_vectorizer.fit_transform(self.original_texts_list)
            self.sequence_mapping = sequence_mapping

            self.logger.info(f"相似度匹配初始化完成，处理了{len(self.original_texts_list)}个原素材文本")
            return True

        except Exception as e:
            self.logger.error(f"相似度匹配初始化失败: {e}")
            return False

    def similarity_match(self, target_text: str, last_matched_seq: int = -1, top_k: int = 10) -> Optional[int]:
        """
        基于TF-IDF相似度的匹配
        """
        try:
            if self.tfidf_vectorizer is None:
                self.logger.warning("TF-IDF向量化器未初始化")
                return None

            # 对目标文本进行分词和向量化
            segmented_target = ' '.join(jieba.cut(target_text))
            target_vector = self.tfidf_vectorizer.transform([segmented_target])

            # 计算与所有原素材的相似度
            similarities = cosine_similarity(target_vector, self.original_tfidf_matrix).flatten()

            # 获取top-k最相似的候选
            top_indices = similarities.argsort()[-top_k:][::-1]
            top_similarities = similarities[top_indices]

            # 过滤低相似度的候选
            valid_candidates = []
            for idx, sim in zip(top_indices, top_similarities):
                if sim >= self.similarity_threshold:
                    original_seq = self.sequence_mapping[idx]
                    valid_candidates.append((original_seq, sim))

            if not valid_candidates:
                return None

            # 应用上下文感知选择最佳候选
            best_candidate = self.select_with_context_awareness(valid_candidates, last_matched_seq)

            if best_candidate:
                seq, sim = best_candidate
                self.logger.debug(f"相似度匹配: '{target_text[:20]}...' -> 序号{seq} (相似度: {sim:.3f})")
                return seq

            return None

        except Exception as e:
            self.logger.error(f"相似度匹配失败: {e}")
            return None

    def select_with_context_awareness(self, candidates: List[Tuple[int, float]], last_matched_seq: int) -> Optional[Tuple[int, float]]:
        """
        基于上下文感知选择最佳候选
        """
        if not candidates:
            return None

        if last_matched_seq == -1:
            # 如果没有前一个匹配，选择相似度最高的
            return max(candidates, key=lambda x: x[1])

        # 计算每个候选的综合得分（相似度 + 上下文连续性）
        scored_candidates = []

        for seq, similarity in candidates:
            # 计算位置连续性得分
            position_distance = abs(seq - last_matched_seq)

            # 位置得分：距离越近得分越高
            if position_distance <= 5:
                position_score = 1.0
            elif position_distance <= 20:
                position_score = 0.8
            elif position_distance <= 50:
                position_score = 0.5
            else:
                position_score = 0.2

            # 防止大幅回退
            if seq < last_matched_seq - 10:
                position_score *= 0.3

            # 综合得分
            final_score = (1 - self.context_weight) * similarity + self.context_weight * position_score
            scored_candidates.append((seq, similarity, position_score, final_score))

        # 选择综合得分最高的候选
        best_candidate = max(scored_candidates, key=lambda x: x[3])
        seq, similarity, position_score, final_score = best_candidate

        self.logger.debug(f"上下文感知选择: 序号{seq}, 相似度{similarity:.3f}, 位置得分{position_score:.3f}, 综合得分{final_score:.3f}")

        return (seq, similarity)

    def select_best_starting_sequence_simple(self, target_text: str) -> Optional[int]:
        """
        为第一个台词选择最佳起始序号（考虑后续台词的连续性）
        """
        target_normalized = self.normalize_text(target_text)
        candidates = []

        # 找到所有可能的匹配
        for seq, entry in self.file_b_entries.items():
            if target_normalized in entry.normalized_text:
                candidates.append(seq)

        if not candidates:
            return None

        # 如果只有一个候选，直接返回
        if len(candidates) == 1:
            return candidates[0]

        # 如果有多个候选，计算每个候选的集群得分（移除时间优先级，让算法基于上下文选择）
        candidate_scores = []

        for candidate in candidates:
            cluster_score = self.calculate_starting_cluster_score(candidate)

            candidate_scores.append({
                'seq': candidate,
                'cluster_score': cluster_score,
                'timestamp': self.file_b_entries[candidate].start_time if candidate in self.file_b_entries else "未知"
            })

            self.logger.debug(f"候选序号{candidate}: 集群得分{cluster_score:.2f}, 时间戳{self.file_b_entries[candidate].start_time if candidate in self.file_b_entries else '未知'}")

        # 按集群得分排序，选择最高分
        candidate_scores.sort(key=lambda x: x['cluster_score'], reverse=True)
        best_candidate = candidate_scores[0]
        best_seq = best_candidate['seq']

        self.logger.info(f"第一个台词选择起始序号{best_seq}（从{len(candidates)}个候选中选择）")
        self.logger.info(f"  最佳候选: 序号{best_seq}, 集群得分{best_candidate['cluster_score']:.2f}")
        self.logger.info(f"  对应时间戳: {best_candidate['timestamp']}")

        return best_seq

    def calculate_starting_cluster_score(self, start_seq: int) -> float:
        """
        计算从指定起始点开始的集群得分（简化版）
        """
        score = 0
        last_seq = start_seq
        consecutive_matches = 1

        # 向前看接下来的几个台词，计算能形成多大的连续集群
        for i in range(1, min(10, len(self.file_a_entries))):  # 最多看前10个台词
            target_text = self.file_a_entries[i]
            target_normalized = self.normalize_text(target_text)

            # 找到与last_seq最接近的匹配
            best_match_seq = None
            min_distance = float('inf')

            for seq, entry in self.file_b_entries.items():
                if target_normalized in entry.normalized_text:
                    distance = abs(seq - last_seq)
                    if distance < min_distance:
                        min_distance = distance
                        best_match_seq = seq

            if best_match_seq is None:
                break

            # 计算距离得分
            if min_distance <= 4:  # 在连锁合并范围内
                score += 10 - min_distance  # 距离越近得分越高
                consecutive_matches += 1
            elif min_distance <= 10:  # 在合理范围内
                score += 5 - (min_distance - 4) * 0.5
            else:  # 距离太远
                break

            last_seq = best_match_seq

        # 连续匹配数量的奖励
        score += consecutive_matches * 2

        # 起始位置的奖励（偏向选择较晚出现的匹配，但不是主要因素）
        position_bonus = start_seq / max(self.file_b_entries.keys()) * 2
        score += position_bonus

        return score

    def parse_timestamp_to_seconds(self, timestamp: str) -> float:
        """
        将时间戳转换为秒数
        格式: HH:MM:SS,mmm
        """
        try:
            # 分离时分秒和毫秒
            time_part, ms_part = timestamp.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)

            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
        except Exception as e:
            self.logger.error(f"解析时间戳失败: {timestamp}, 错误: {e}")
            return 0.0

    def get_time_gap_between_sequences(self, seq1: int, seq2: int) -> float:
        """
        计算两个序号之间的时间间隔（秒）
        返回seq2的开始时间 - seq1的结束时间
        """
        if seq1 not in self.file_b_entries or seq2 not in self.file_b_entries:
            return float('inf')  # 如果序号不存在，返回无穷大

        entry1 = self.file_b_entries[seq1]
        entry2 = self.file_b_entries[seq2]

        end_time1 = self.parse_timestamp_to_seconds(entry1.end_time)
        start_time2 = self.parse_timestamp_to_seconds(entry2.start_time)

        return start_time2 - end_time1

    def adjust_end_time(self, timestamp: str, milliseconds_offset: int) -> str:
        """
        调整时间戳，增加或减少指定的毫秒数
        参数:
            timestamp: 原始时间戳，格式: HH:MM:SS,mmm
            milliseconds_offset: 毫秒偏移量，正数增加，负数减少
        返回: 调整后的时间戳
        """
        try:
            # 分离时分秒和毫秒
            time_part, ms_part = timestamp.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)

            # 转换为总毫秒数
            total_ms = hours * 3600000 + minutes * 60000 + seconds * 1000 + milliseconds

            # 应用偏移量
            total_ms += milliseconds_offset

            # 确保不小于0
            if total_ms < 0:
                total_ms = 0

            # 转换回时分秒格式
            new_hours = total_ms // 3600000
            remaining_ms = total_ms % 3600000
            new_minutes = remaining_ms // 60000
            remaining_ms = remaining_ms % 60000
            new_seconds = remaining_ms // 1000
            new_milliseconds = remaining_ms % 1000

            return f"{new_hours:02d}:{new_minutes:02d}:{new_seconds:02d},{new_milliseconds:03d}"

        except Exception as e:
            self.logger.error(f"调整时间戳失败: {timestamp}, 偏移: {milliseconds_offset}, 错误: {e}")
            return timestamp

    def partial_match(self, target: str, source: str, min_ratio: float = 0.5) -> bool:
        """
        部分匹配：如果目标文本的主要部分能在源文本中找到，就认为匹配成功
        """
        # 将目标文本分成多个部分，检查是否有足够比例的部分能匹配
        target_words = target.split()
        if len(target_words) < 3:  # 太短的文本直接用灵活匹配
            return self.flexible_text_match(target, source)

        # 尝试不同长度的前缀
        for i in range(len(target_words) // 2, len(target_words)):
            prefix = ' '.join(target_words[:i])
            if self.flexible_text_match(prefix, source):
                # 计算匹配比例
                ratio = len(prefix) / len(target)
                if ratio >= min_ratio:
                    return True

        # 尝试分段匹配：检查目标文本的多个片段是否都能在源文本中找到
        return self._segment_match(target, source, min_ratio)

    def _segment_match(self, target: str, source: str, min_ratio: float = 0.5) -> bool:
        """
        分段匹配：将目标文本分成多个片段，检查是否有足够比例的片段能在源文本中找到
        """
        target_words = target.split()
        if len(target_words) < 4:
            return False

        # 将目标文本分成3-4个片段
        segment_size = max(2, len(target_words) // 4)
        segments = []

        for i in range(0, len(target_words), segment_size):
            segment = ' '.join(target_words[i:i + segment_size])
            if len(segment.strip()) > 1:  # 忽略太短的片段
                segments.append(segment)

        if len(segments) < 2:
            return False

        # 检查有多少片段能在源文本中找到
        matched_segments = 0
        total_matched_length = 0

        for segment in segments:
            if self.flexible_text_match(segment, source):
                matched_segments += 1
                total_matched_length += len(segment)

        # 计算匹配比例
        match_ratio = total_matched_length / len(target)
        segment_ratio = matched_segments / len(segments)

        # 如果匹配的片段比例 >= 50% 且 匹配的文本长度比例 >= min_ratio
        return segment_ratio >= 0.5 and match_ratio >= min_ratio
    
    def parse_subtitle_file(self, file_path: str, is_file_a: bool = True) -> bool:
        """
        解析字幕文件
        Args:
            file_path: 文件路径
            is_file_a: True表示文件A（最终剪辑），False表示文件B（原始素材）
        """
        file_type = "文件A（最终剪辑）" if is_file_a else "文件B（原始素材）"
        self.logger.info(f"开始解析{file_type}: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if is_file_a:
                result = self._parse_file_a(content)
            else:
                result = self._parse_file_b(content)

            if result:
                if is_file_a:
                    self.logger.info(f"成功解析{file_type}，共{len(self.file_a_entries)}个台词条目")
                else:
                    self.logger.info(f"成功解析{file_type}，共{len(self.file_b_entries)}个镜头条目")
            else:
                self.logger.error(f"解析{file_type}失败：内容为空或格式错误")

            return result

        except Exception as e:
            self.logger.error(f"解析{file_type}失败: {e}")
            return False
    
    def _parse_file_a(self, content: str) -> bool:
        """解析文件A（最终剪辑版字幕）"""
        lines = content.strip().split('\n')
        self.file_a_entries = []
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # 跳过空行
            if not line:
                i += 1
                continue
                
            # 检查是否是序号行
            if line.isdigit():
                # 跳过序号和时间戳行，直接获取台词
                i += 2  # 跳过时间戳行
                if i < len(lines):
                    text = lines[i].strip()
                    if text:  # 只添加非空台词
                        self.file_a_entries.append(text)
                i += 1
            else:
                i += 1
        
        return len(self.file_a_entries) > 0
    
    def _parse_file_b(self, content: str) -> bool:
        """解析文件B（原始素材分镜时间戳）"""
        lines = content.strip().split('\n')
        self.file_b_entries = {}
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # 跳过空行
            if not line:
                i += 1
                continue
                
            # 检查是否是序号行
            if line.isdigit():
                sequence = int(line)
                i += 1
                
                # 获取时间戳
                if i < len(lines):
                    timestamp_line = lines[i].strip()
                    if ' --> ' in timestamp_line:
                        start_time, end_time = timestamp_line.split(' --> ')
                        i += 1
                        
                        # 获取台词（支持多行）
                        text_lines = []
                        while i < len(lines):
                            current_line = lines[i].strip()

                            # 如果遇到空行，停止读取台词
                            if not current_line:
                                break

                            # 如果遇到下一个序号（数字），停止读取台词
                            if current_line.isdigit():
                                break

                            # 如果遇到时间戳格式，停止读取台词
                            if ' --> ' in current_line:
                                break

                            text_lines.append(current_line)
                            i += 1

                        # 合并多行台词
                        if text_lines:
                            text = " ".join(text_lines)  # 用空格连接多行
                        else:
                            text = ""  # 空字幕

                        normalized_text = self.normalize_text(text)

                        entry = SubtitleEntry(
                            sequence=sequence,
                            start_time=start_time.strip(),
                            end_time=end_time.strip(),
                            text=text,
                            normalized_text=normalized_text
                        )
                        self.file_b_entries[sequence] = entry
                    else:
                        i += 1
                else:
                    i += 1
            else:
                i += 1
        
        return len(self.file_b_entries) > 0

    def find_closest_sequence(self, candidates: List[int], last_matched_seq: int) -> int:
        """找到与上一个匹配序号最接近的候选序号"""
        if not candidates:
            return -1

        if last_matched_seq == -1:
            return min(candidates)  # 如果没有上一个匹配，返回最小的序号

        # 找到与last_matched_seq差值最小的序号
        closest = candidates[0]
        min_diff = abs(candidates[0] - last_matched_seq)

        for seq in candidates[1:]:
            diff = abs(seq - last_matched_seq)
            if diff < min_diff:
                min_diff = diff
                closest = seq

        return closest

    def match_substring_search(self, target_text: str, last_matched_seq: int) -> Optional[int]:
        """
        规则1：子字符串搜索
        在文件B的所有台词中查找包含目标台词作为子字符串的镜头
        """
        target_normalized = self.normalize_text(target_text)
        candidates = []

        for seq, entry in self.file_b_entries.items():
            # 尝试多种匹配方式
            if (target_normalized in entry.normalized_text or
                self.flexible_text_match(target_text, entry.text) or
                self.partial_match(target_text, entry.text)):
                candidates.append(seq)

        if candidates:


            return self.find_closest_sequence(candidates, last_matched_seq)

        return None

    def find_fuzzy_match_nearby(self, target_text: str, center_seq: int, max_range: int = 100) -> Optional[int]:
        """
        在指定序号附近寻找模糊匹配

        Args:
            target_text: 目标文本
            center_seq: 中心序号
            max_range: 搜索范围

        Returns:
            匹配的序号，如果没有找到返回None
        """
        target_normalized = self.normalize_text(target_text)
        target_words = set(target_normalized.split())

        best_match = None
        best_score = 0

        # 在指定范围内搜索
        for seq in range(max(1, center_seq - max_range), center_seq + max_range + 1):
            if seq in self.file_b_entries:
                entry = self.file_b_entries[seq]
                entry_words = set(entry.normalized_text.split())

                # 计算词汇重叠度
                if target_words and entry_words:
                    overlap = len(target_words & entry_words)
                    score = overlap / len(target_words)

                    # 要求至少50%的词汇重叠
                    if score >= 0.5 and score > best_score:
                        best_score = score
                        best_match = seq

        if best_match:
            self.logger.debug(f"模糊匹配: '{target_text}' -> 序号{best_match} (重叠度: {best_score:.2f})")

        return best_match



    def match_simple_multi_shot(self, target_text: str, last_matched_seq: int, max_window: int = 3) -> Optional[List[int]]:
        """
        简化的多镜头合并搜索（只使用窗口大小2-3）
        将连续镜头的文本合并后进行子字符串匹配
        """
        target_normalized = self.normalize_text(target_text)
        sequences = sorted(self.file_b_entries.keys())

        # 从窗口大小2开始，最多到max_window
        for window_size in range(2, max_window + 1):
            for i in range(len(sequences) - window_size + 1):
                window_sequences = sequences[i:i + window_size]

                # 检查窗口中的序号是否连续
                is_consecutive = True
                for j in range(len(window_sequences) - 1):
                    if window_sequences[j + 1] - window_sequences[j] != 1:
                        is_consecutive = False
                        break

                if not is_consecutive:
                    continue

                # 合并窗口内所有镜头的规范化文本
                combined_texts = []
                for seq in window_sequences:
                    if seq in self.file_b_entries:
                        combined_texts.append(self.file_b_entries[seq].normalized_text)
                combined_text = " ".join(combined_texts)

                # 检查目标文本是否在合并文本中
                if (target_normalized in combined_text or
                    self.flexible_text_match(target_normalized, combined_text) or
                    self.partial_match(target_normalized, combined_text)):

                    # 如果没有上一个匹配，返回第一个匹配的窗口
                    if last_matched_seq == -1:
                        return window_sequences

                    # 选择与上一个匹配最接近的窗口
                    window_start = window_sequences[0]
                    distance = abs(window_start - last_matched_seq)

                    # 只接受合理距离内的匹配
                    if distance <= 50:  # 限制跳跃距离
                        return window_sequences

        return None





    def perform_cluster_analysis(self) -> Dict[int, List[int]]:
        """
        执行集群分析，为每个台词找到所有可能的匹配
        返回: {台词索引: [可能的序号列表], ...}
        """
        self.logger.info("开始集群分析")
        all_possible_matches = {}

        for i, target_text in enumerate(self.file_a_entries):
            possible_sequences = []

            # 规则1：纯粹的子字符串搜索（只使用精确匹配）
            target_normalized = self.normalize_text(target_text)
            for seq, entry in self.file_b_entries.items():
                if target_normalized in entry.normalized_text:
                    possible_sequences.append(seq)

            # 如果规则1没有找到匹配，尝试简化的多镜头合并
            if not possible_sequences:
                sequences = sorted(self.file_b_entries.keys())
                for window_size in range(2, 4):  # 只使用窗口大小2-3
                    for j in range(len(sequences) - window_size + 1):
                        window_sequences = sequences[j:j + window_size]

                        # 检查连续性
                        is_consecutive = True
                        for k in range(len(window_sequences) - 1):
                            if window_sequences[k + 1] - window_sequences[k] != 1:
                                is_consecutive = False
                                break

                        if not is_consecutive:
                            continue

                        # 合并文本
                        combined_texts = []
                        for seq in window_sequences:
                            if seq in self.file_b_entries:
                                combined_texts.append(self.file_b_entries[seq].normalized_text)
                        combined_text = " ".join(combined_texts)

                        # 多镜头匹配：精确匹配、灵活匹配和部分匹配
                        if (target_normalized in combined_text or
                            self.flexible_text_match(target_normalized, combined_text) or
                            self.partial_match(target_normalized, combined_text)):
                            possible_sequences.extend(window_sequences)
                            break  # 找到第一个匹配就停止

                    if possible_sequences:
                        break

            all_possible_matches[i] = possible_sequences

        self.logger.info(f"集群分析完成，找到{sum(1 for matches in all_possible_matches.values() if matches)}个有匹配的台词")
        return all_possible_matches

    def find_best_cluster_path(self, all_possible_matches: Dict[int, List[int]]) -> List[int]:
        """
        找到最佳的集群路径
        返回: 每个台词的最佳匹配序号列表
        """
        self.logger.info("开始寻找最佳集群路径")

        # 为每个台词选择最佳匹配
        best_matches = []
        last_matched_seq = -1

        for i in range(len(self.file_a_entries)):
            possible_sequences = all_possible_matches.get(i, [])

            if not possible_sequences:
                best_matches.append([])
                continue

            if last_matched_seq == -1:
                # 第一个匹配：选择能形成最大前向集群的序号
                best_seq = self.select_best_starting_sequence(i, all_possible_matches)
                if isinstance(best_seq, list):
                    best_matches.append(best_seq)
                    last_matched_seq = max(best_seq)
                else:
                    best_matches.append([best_seq] if best_seq is not None else [])
                    last_matched_seq = best_seq if best_seq is not None else -1
            else:
                # 后续匹配：选择与前一个匹配最接近的序号
                if len(possible_sequences) == 1:
                    best_seq = possible_sequences[0]
                    best_matches.append([best_seq])
                    last_matched_seq = best_seq
                elif all(isinstance(seq, int) for seq in possible_sequences):
                    # 单镜头匹配
                    best_seq = self.find_closest_sequence(possible_sequences, last_matched_seq)
                    best_matches.append([best_seq])
                    last_matched_seq = best_seq
                else:
                    # 多镜头匹配
                    best_matches.append(possible_sequences)
                    last_matched_seq = max(possible_sequences)

        return best_matches

    def select_best_starting_sequence(self, start_index: int, all_possible_matches: Dict[int, List[int]]) -> int:
        """
        选择最佳的起始序号，基于能形成的前向集群大小
        """
        possible_sequences = all_possible_matches[start_index]
        if not possible_sequences:
            return None

        best_seq = None
        max_cluster_score = -1

        for candidate_seq in possible_sequences:
            # 计算以这个序号开始能形成的集群得分
            cluster_score = self.calculate_cluster_score(start_index, candidate_seq, all_possible_matches)

            self.logger.debug(f"候选序号{candidate_seq}的集群得分: {cluster_score}")

            if cluster_score > max_cluster_score:
                max_cluster_score = cluster_score
                best_seq = candidate_seq

        self.logger.info(f"选择起始序号{best_seq}，集群得分: {max_cluster_score}")
        return best_seq

    def calculate_cluster_score(self, start_index: int, start_seq: int, all_possible_matches: Dict[int, List[int]]) -> float:
        """
        计算从指定起始点开始的集群得分
        """
        score = 0
        last_seq = start_seq
        consecutive_matches = 1

        # 向前看接下来的台词，计算能形成多大的连续集群
        for i in range(start_index + 1, min(start_index + 20, len(self.file_a_entries))):  # 最多看前20个台词
            possible_sequences = all_possible_matches.get(i, [])
            if not possible_sequences:
                break

            # 找到与last_seq最接近的序号
            closest_seq = self.find_closest_sequence(possible_sequences, last_seq)
            if closest_seq == -1:
                break

            # 计算距离得分
            distance = abs(closest_seq - last_seq)
            if distance <= 4:  # 在连锁合并范围内
                score += 10 - distance  # 距离越近得分越高
                consecutive_matches += 1
            elif distance <= 10:  # 在合理范围内
                score += 5 - (distance - 4) * 0.5
            else:  # 距离太远
                break

            last_seq = closest_seq

        # 连续匹配数量的奖励
        score += consecutive_matches * 2

        # 起始位置的奖励（偏向选择较晚出现的匹配，符合故事发展逻辑）
        position_bonus = start_seq / max(self.file_b_entries.keys()) * 5
        score += position_bonus

        return score

    def perform_advanced_matching(self) -> List[MatchResult]:
        """执行高级匹配算法 - 集成相似度匹配和上下文感知"""
        self.logger.info("开始执行高级匹配算法（集成相似度匹配和上下文感知）")

        # 初始化相似度匹配
        similarity_initialized = self.initialize_similarity_matching()
        if similarity_initialized:
            self.logger.info("相似度匹配功能已启用")
        else:
            self.logger.warning("相似度匹配功能初始化失败，将使用传统方法")

        self.match_results = []
        self.matched_pairs = []  # 存储匹配的配对信息
        last_matched_seq = -1
        successful_matches = 0
        rule1_matches = 0
        rule2_matches = 0
        rule3_matches = 0  # 相似度匹配
        failed_matches = 0

        # 添加区域一致性约束
        expected_region_center = None  # 期望的匹配区域中心
        region_tolerance = 100  # 区域容差（序号范围）

        for i, target_text in enumerate(self.file_a_entries):
            matched_sequences = []
            match_method = ""

            # 规则1：子字符串搜索（纯粹的精确匹配）
            if last_matched_seq == -1:
                # 第一个匹配：使用集群分析选择最佳起始点
                matched_seq = self.select_best_starting_sequence_simple(target_text)
            else:
                # 后续匹配：使用常规的最接近匹配
                matched_seq = self.match_substring_search(target_text, last_matched_seq)

            if matched_seq is not None:
                matched_sequences = [matched_seq]
                last_matched_seq = matched_seq
                match_method = "规则1（子字符串搜索）"
                rule1_matches += 1
                successful_matches += 1
                # 记录匹配配对（单镜头）
                self.matched_pairs.append((target_text, matched_seq))
            else:
                # 规则2：简化的多镜头合并搜索（只有规则1失败时才触发）
                multi_match = self.match_simple_multi_shot(target_text, last_matched_seq)
                if multi_match is not None:
                    matched_sequences = multi_match
                    last_matched_seq = max(multi_match)
                    match_method = f"规则2（简化多镜头合并，窗口大小{len(multi_match)}）"
                    rule2_matches += 1
                    successful_matches += 1
                    # 记录匹配配对（多镜头）
                    self.matched_pairs.append((target_text, multi_match))
                else:
                    # 规则3：相似度匹配（最后的尝试）
                    if similarity_initialized:
                        similarity_match = self.similarity_match(target_text, last_matched_seq)
                        if similarity_match is not None:
                            matched_sequences = [similarity_match]
                            last_matched_seq = similarity_match
                            match_method = "规则3（相似度匹配）"
                            rule3_matches += 1
                            successful_matches += 1
                            # 记录匹配配对（相似度）
                            self.matched_pairs.append((target_text, [similarity_match]))
                        else:
                            # 所有方法都失败
                            matched_sequences = []
                            match_method = "匹配失败"
                            failed_matches += 1
                    else:
                        # 相似度匹配未初始化，直接失败
                        matched_sequences = []
                        match_method = "匹配失败"
                        failed_matches += 1

            # 记录详细匹配信息
            text_preview = target_text[:20] + "..." if len(target_text) > 20 else target_text
            self.logger.debug(f"台词{i+1}: '{text_preview}' -> {match_method} -> 序号{matched_sequences}")

            # 记录匹配结果
            result = MatchResult(
                final_text=target_text,
                matched_sequences=matched_sequences
            )
            self.match_results.append(result)

        # 记录匹配统计
        self.logger.info(f"匹配完成统计:")
        self.logger.info(f"  总台词数: {len(self.file_a_entries)}")
        self.logger.info(f"  成功匹配: {successful_matches}")
        self.logger.info(f"  规则1匹配: {rule1_matches}")
        self.logger.info(f"  规则2匹配: {rule2_matches}")
        self.logger.info(f"  规则3匹配: {rule3_matches}")
        self.logger.info(f"  匹配失败: {failed_matches}")
        self.logger.info(f"  匹配成功率: {successful_matches/len(self.file_a_entries)*100:.1f}%")

        return self.match_results

    def create_blocks_and_expand(self, max_time_gap: float = 8.0) -> List[List[int]]:
        """
        根据匹配结果创建区块并进行扩展
        参数:
            max_time_gap: 最大允许的时间间隔（秒），超过此间隔的镜头不会被合并到同一区块
        返回: [[序号列表], ...]的列表，每个子列表包含一个区块的实际匹配序号
        """
        self.logger.info(f"开始区块划分与扩展（最大时间间隔: {max_time_gap}秒）")

        if not self.match_results:
            self.logger.warning("没有匹配结果，无法创建区块")
            return []

        # 收集所有匹配的序号，保持原始顺序
        all_sequences = []
        for result in self.match_results:
            all_sequences.extend(result.matched_sequences)

        if not all_sequences:
            self.logger.warning("没有成功匹配的序号，无法创建区块")
            return []

        self.logger.debug(f"收集到的所有序号: {all_sequences}")

        # 去重但保持顺序（使用dict.fromkeys()方法）
        unique_sequences = list(dict.fromkeys(all_sequences))
        self.logger.debug(f"去重后的序号: {unique_sequences}")

        # 如果去重后序号数量明显减少，记录信息
        if len(unique_sequences) < len(all_sequences):
            removed_count = len(all_sequences) - len(unique_sequences)
            self.logger.info(f"去除了{removed_count}个重复序号，剩余{len(unique_sequences)}个唯一序号")

        # 第三步：区块划分（修复版本 - 确保所有序号都被包含）
        blocks = []

        # 处理空序号列表的情况
        if not unique_sequences:
            self.logger.warning("没有唯一序号，无法创建区块")
            return []

        # 处理只有一个序号的情况
        if len(unique_sequences) == 1:
            blocks.append([unique_sequences[0]])
            self.logger.info(f"只有一个序号{unique_sequences[0]}，创建单独区块")
        else:
            # 多个序号的情况
            current_block = [unique_sequences[0]]

            for i in range(1, len(unique_sequences)):
                current_seq = unique_sequences[i]
                prev_seq = unique_sequences[i - 1]

                # 计算序号差值
                seq_diff = abs(current_seq - prev_seq)

                # 计算时间间隔
                time_gap = self.get_time_gap_between_sequences(prev_seq, current_seq)

                # 检查序号对应的字幕是否为空
                prev_has_text = prev_seq in self.file_b_entries and bool(self.file_b_entries[prev_seq].normalized_text.strip())
                current_has_text = current_seq in self.file_b_entries and bool(self.file_b_entries[current_seq].normalized_text.strip())

                # 子规则A：关联区块的"连锁合并"（根据字幕内容调整差值限制）
                # 如果是空字幕：序号差值 ≤ 4，时间间隔 ≤ 8秒
                # 如果有字幕：序号差值 ≤ 2，时间间隔 ≤ 8秒
                max_seq_diff = 4 if not prev_has_text or not current_has_text else 2
                max_time_interval = 8.0  # 统一使用8秒限制

                if seq_diff <= max_seq_diff and time_gap <= max_time_interval:
                    current_block.append(current_seq)
                    text_status = "空字幕" if not prev_has_text or not current_has_text else "有字幕"
                    self.logger.debug(f"合并序号{prev_seq}->{current_seq}: {text_status}, 序号差值={seq_diff}(≤{max_seq_diff}), 时间间隔={time_gap:.1f}秒(≤{max_time_interval})")
                else:
                    # 开始新区块
                    text_status = "空字幕" if not prev_has_text or not current_has_text else "有字幕"
                    if seq_diff > max_seq_diff:
                        self.logger.debug(f"新区块（{text_status}序号差值过大）: {prev_seq}->{current_seq}, 差值={seq_diff}(>{max_seq_diff})")
                    elif time_gap > max_time_interval:
                        self.logger.debug(f"新区块（时间间隔过大）: {prev_seq}->{current_seq}, 间隔={time_gap:.1f}秒(>{max_time_interval})")

                    blocks.append(current_block)
                    current_block = [current_seq]

            # 添加最后一个区块
            if current_block:
                blocks.append(current_block)

        self.logger.info(f"初始区块划分完成，共{len(blocks)}个区块")
        for i, block in enumerate(blocks):
            self.logger.debug(f"区块{i+1}: 序号{min(block)}-{max(block)} (包含{len(block)}个序号)")

        # 验证所有序号都被包含在区块中
        all_block_sequences = []
        for block in blocks:
            all_block_sequences.extend(block)

        missing_sequences = set(unique_sequences) - set(all_block_sequences)
        if missing_sequences:
            self.logger.warning(f"发现遗漏的序号: {sorted(missing_sequences)}")
            # 为每个遗漏的序号创建单独的区块
            for seq in sorted(missing_sequences):
                blocks.append([seq])
                self.logger.info(f"为遗漏序号{seq}创建单独区块")
        else:
            self.logger.info(f"验证通过：所有{len(unique_sequences)}个序号都被包含在区块中")

        # 删除连续序号扩展算法（避免产生超长时间戳）
        # expanded_blocks = self.apply_continuous_sequence_expansion(blocks)

        # 直接返回原始区块列表
        self.logger.info(f"最终区块数量: {len(blocks)}")

        # 记录每个区块的详细信息
        for i, block in enumerate(blocks):
            sequences_str = ",".join(map(str, sorted(block)))
            self.logger.debug(f"区块{i+1}: 序号[{sequences_str}] (包含{len(block)}个序号)")

        return blocks



    def generate_final_timestamps(self, max_time_gap: float = 8.0) -> List[str]:
        """
        生成最终的时间戳列表
        返回: ["开始时间 --> 结束时间", ...]的列表
        """
        self.logger.info("开始生成最终时间戳")
        blocks = self.create_blocks_and_expand(max_time_gap)
        final_timestamps = []

        for i, block_sequences in enumerate(blocks):
            # 现在blocks直接包含序号列表
            actual_sequences = sorted(block_sequences)

            if not actual_sequences:
                self.logger.warning(f"区块{i+1}: 没有有效的匹配序号")
                continue

            # 获取第一个序号的开始时间和最后一个序号的结束时间
            start_seq = actual_sequences[0]
            end_seq = actual_sequences[-1]

            start_time = None
            end_time = None

            # 查找开始序号的开始时间
            if start_seq in self.file_b_entries:
                start_time = self.file_b_entries[start_seq].start_time
            else:
                self.logger.warning(f"区块{i+1}: 找不到序号{start_seq}的开始时间")

            # 查找结束序号的结束时间
            if end_seq in self.file_b_entries:
                original_end_time = self.file_b_entries[end_seq].end_time
                # 将结束时间减去1毫秒，避免包含下一个序号
                end_time = self.adjust_end_time(original_end_time, -1)
            else:
                self.logger.warning(f"区块{i+1}: 找不到序号{end_seq}的结束时间")
                end_time = None

            # 如果找到了有效的时间戳，添加到结果中
            if start_time and end_time:
                timestamp = f"{start_time} --> {end_time}"
                final_timestamps.append(timestamp)
                sequences_str = ",".join(map(str, actual_sequences))
                self.logger.debug(f"区块{i+1}: 序号[{sequences_str}] -> {timestamp}")
            else:
                self.logger.error(f"区块{i+1}: 无法生成时间戳，序号{start_seq}-{end_seq}")

        self.logger.info(f"时间戳生成完成，共{len(final_timestamps)}个时间戳")

        # 应用最终时间戳扩展算法
        expanded_timestamps = self.apply_final_timestamp_expansion(blocks, final_timestamps)

        # 修复最终时间戳扩展导致的重叠问题
        fixed_timestamps = self.fix_overlapping_timestamps(expanded_timestamps)

        # 禁用时间戳智能合并算法（会产生超长时间戳，影响使用体验）
        # merged_timestamps = self.apply_timestamp_merging(fixed_timestamps)

        # 保持二次剪辑的原始台词顺序（不按时间排序）
        # 二次剪辑的逻辑：开局交代核心故事，然后回到片段开头
        # sorted_timestamps = self.sort_timestamps_by_time(fixed_timestamps)

        # 最后一步：修复1毫秒间隔问题（简单版本）
        final_timestamps = self.fix_one_millisecond_gaps(fixed_timestamps)

        # 最终步骤：强制合并所有1毫秒间隔（独立处理）
        final_timestamps = self.force_merge_one_millisecond_gaps(final_timestamps)

        return final_timestamps

    def fix_overlapping_timestamps(self, timestamps: List[str]) -> List[str]:
        """
        修复重叠时间戳：简单合并策略

        策略：只合并相邻的重叠时间戳，取最早开始时间和最晚结束时间

        Args:
            timestamps: 原始时间戳列表

        Returns:
            修复后的时间戳列表
        """
        if not timestamps:
            return timestamps

        self.logger.info("开始修复重叠时间戳（简单合并策略）")

        fixed_timestamps = []
        i = 0
        merge_count = 0

        while i < len(timestamps):
            current_timestamp = timestamps[i]

            if ' --> ' not in current_timestamp:
                fixed_timestamps.append(current_timestamp)
                i += 1
                continue

            # 解析当前时间戳
            start_str, end_str = current_timestamp.split(' --> ')
            current_start_ms = self.timestamp_to_milliseconds(start_str.strip())
            current_end_ms = self.timestamp_to_milliseconds(end_str.strip())

            # 检查是否需要与下一个时间戳合并
            merge_group = [current_timestamp]
            earliest_start_ms = current_start_ms
            latest_end_ms = current_end_ms

            # 向前查找重叠的时间戳
            j = i + 1
            while j < len(timestamps):
                next_timestamp = timestamps[j]

                if ' --> ' not in next_timestamp:
                    break

                next_start_str, next_end_str = next_timestamp.split(' --> ')
                next_start_ms = self.timestamp_to_milliseconds(next_start_str.strip())
                next_end_ms = self.timestamp_to_milliseconds(next_end_str.strip())

                # 检查是否重叠（下一个开始时间 < 当前组的结束时间）
                if next_start_ms < latest_end_ms:
                    # 计算重叠时长
                    overlap_ms = latest_end_ms - next_start_ms

                    # 只合并小幅重叠（少于15秒），避免合并不相关的时间段
                    if overlap_ms <= 15000:  # 15秒
                        # 小幅重叠，加入合并组
                        merge_group.append(next_timestamp)
                        earliest_start_ms = min(earliest_start_ms, next_start_ms)
                        latest_end_ms = max(latest_end_ms, next_end_ms)
                        j += 1
                        self.logger.debug(f"检测到小幅重叠: 时间戳{j}与时间戳{i+1}重叠{overlap_ms/1000:.1f}秒，加入合并组")
                    else:
                        # 大幅重叠，不合并，停止
                        self.logger.debug(f"检测到大幅重叠: 时间戳{j}与时间戳{i+1}重叠{overlap_ms/1000:.1f}秒，不合并")
                        break
                else:
                    # 不重叠，停止合并
                    break

            # 生成合并后的时间戳
            if len(merge_group) == 1:
                # 没有重叠，保持原样
                fixed_timestamps.append(current_timestamp)
            else:
                # 有重叠，合并为一个时间戳
                merged_start_str = self.milliseconds_to_timestamp(earliest_start_ms)
                merged_end_str = self.milliseconds_to_timestamp(latest_end_ms)
                merged_timestamp = f"{merged_start_str} --> {merged_end_str}"

                fixed_timestamps.append(merged_timestamp)
                merge_count += len(merge_group) - 1

                self.logger.info(f"合并了{len(merge_group)}个重叠时间戳:")
                for k, ts in enumerate(merge_group):
                    self.logger.debug(f"  原始{i+k+1}: {ts}")
                self.logger.info(f"  合并结果: {merged_timestamp}")

            # 移动到下一个未处理的时间戳
            i = j

        # 统计合并效果
        original_count = len(timestamps)
        merged_count = len(fixed_timestamps)

        if merge_count > 0:
            self.logger.info(f"重叠时间戳修复完成，从{original_count}个合并为{merged_count}个，减少了{merge_count}个重叠")
        else:
            self.logger.info(f"重叠时间戳修复完成，没有发现重叠时间戳")

        return fixed_timestamps

    def fix_one_millisecond_gaps(self, timestamps: List[str]) -> List[str]:
        """
        修复1毫秒间隔问题（最简单版本）

        直接在最终时间戳结果上查找所有1毫秒间隔并合并

        Args:
            timestamps: 最终时间戳列表

        Returns:
            修复后的时间戳列表
        """
        if len(timestamps) <= 1:
            return timestamps.copy()

        self.logger.info("开始修复最终结果中的1毫秒间隔问题")

        # 解析所有时间戳
        parsed_list = []
        for i, timestamp in enumerate(timestamps):
            if ' --> ' in timestamp:
                start_str, end_str = timestamp.split(' --> ')
                start_ms = self.timestamp_to_milliseconds(start_str.strip())
                end_ms = self.timestamp_to_milliseconds(end_str.strip())
                parsed_list.append({
                    'index': i,
                    'timestamp': timestamp,
                    'start_ms': start_ms,
                    'end_ms': end_ms,
                    'merged': False
                })
            else:
                parsed_list.append({
                    'index': i,
                    'timestamp': timestamp,
                    'start_ms': None,
                    'end_ms': None,
                    'merged': False
                })

        # 查找所有1毫秒间隔的时间戳对
        merge_pairs = []
        for i in range(len(parsed_list)):
            if parsed_list[i]['start_ms'] is None or parsed_list[i]['merged']:
                continue

            current = parsed_list[i]

            # 查找与当前时间戳结束时间只差1毫秒的其他时间戳
            for j in range(len(parsed_list)):
                if i == j or parsed_list[j]['start_ms'] is None or parsed_list[j]['merged']:
                    continue

                other = parsed_list[j]
                gap_ms = other['start_ms'] - current['end_ms']

                # 如果间隔正好是1毫秒，记录这个配对
                if gap_ms == 1:
                    merge_pairs.append((i, j))
                    parsed_list[i]['merged'] = True
                    parsed_list[j]['merged'] = True

                    self.logger.info(f"发现1毫秒间隔配对:")
                    self.logger.info(f"  时间戳{i+1}: {current['timestamp']}")
                    self.logger.info(f"  时间戳{j+1}: {other['timestamp']}")
                    break  # 找到配对后继续查找其他配对

        # 执行合并
        result = []
        for i, parsed in enumerate(parsed_list):
            if parsed['merged']:
                # 检查是否是合并对的第一个
                is_first_in_pair = any(pair[0] == i for pair in merge_pairs)
                if is_first_in_pair:
                    # 找到配对的第二个
                    pair_index = next(pair[1] for pair in merge_pairs if pair[0] == i)
                    second = parsed_list[pair_index]

                    # 合并
                    merged_start_ms = min(parsed['start_ms'], second['start_ms'])
                    merged_end_ms = max(parsed['end_ms'], second['end_ms'])

                    merged_start_str = self.milliseconds_to_timestamp(merged_start_ms)
                    merged_end_str = self.milliseconds_to_timestamp(merged_end_ms)
                    merged_timestamp = f"{merged_start_str} --> {merged_end_str}"

                    result.append(merged_timestamp)

                    self.logger.info(f"合并1毫秒间隔:")
                    self.logger.info(f"  合并结果: {merged_timestamp}")
                # 如果是第二个，跳过（已经在第一个时处理了）
            else:
                result.append(parsed['timestamp'])

        self.logger.info(f"1毫秒间隔修复完成，从{len(timestamps)}个合并为{len(result)}个，减少了{len(timestamps) - len(result)}个")
        return result

    def force_merge_one_millisecond_gaps(self, timestamps: List[str]) -> List[str]:
        """
        强制合并所有1毫秒间隔（最简单直接的方法）

        就像您说的：
        12. 00:26:06,967 --> 00:26:58,598
        13. 00:26:58,599 --> 00:27:12,931
        直接合成：
        12. 00:26:06,967 --> 00:27:12,931

        Args:
            timestamps: 时间戳列表

        Returns:
            强制合并后的时间戳列表
        """
        if len(timestamps) <= 1:
            return timestamps.copy()

        self.logger.info("开始强制合并所有1毫秒间隔（最简单直接方法）")

        result = []
        i = 0

        while i < len(timestamps):
            current_timestamp = timestamps[i]

            if ' --> ' not in current_timestamp:
                result.append(current_timestamp)
                i += 1
                continue

            # 解析当前时间戳
            current_start_str, current_end_str = current_timestamp.split(' --> ')
            current_start_ms = self.timestamp_to_milliseconds(current_start_str.strip())
            current_end_ms = self.timestamp_to_milliseconds(current_end_str.strip())

            # 查找所有可以连续合并的时间戳
            merged_end_ms = current_end_ms
            merged_end_str = current_end_str.strip()
            j = i + 1

            # 持续查找下一个1毫秒间隔的时间戳
            while j < len(timestamps):
                next_timestamp = timestamps[j]

                if ' --> ' not in next_timestamp:
                    break

                next_start_str, next_end_str = next_timestamp.split(' --> ')
                next_start_ms = self.timestamp_to_milliseconds(next_start_str.strip())
                next_end_ms = self.timestamp_to_milliseconds(next_end_str.strip())

                # 检查是否正好是1毫秒间隔
                gap_ms = next_start_ms - merged_end_ms

                if gap_ms == 1:
                    # 可以合并，更新结束时间
                    merged_end_ms = next_end_ms
                    merged_end_str = next_end_str.strip()

                    self.logger.info(f"强制合并1毫秒间隔:")
                    self.logger.info(f"  时间戳{i+1}: {current_timestamp}")
                    self.logger.info(f"  时间戳{j+1}: {next_timestamp}")

                    j += 1
                else:
                    # 不是1毫秒间隔，停止合并
                    break

            # 生成合并后的时间戳
            if j > i + 1:
                # 发生了合并
                merged_timestamp = f"{current_start_str.strip()} --> {merged_end_str}"
                result.append(merged_timestamp)

                self.logger.info(f"  合并结果: {merged_timestamp}")

                i = j  # 跳过所有被合并的时间戳
            else:
                # 没有合并，保持原样
                result.append(current_timestamp)
                i += 1

        self.logger.info(f"强制1毫秒间隔合并完成，从{len(timestamps)}个合并为{len(result)}个，减少了{len(timestamps) - len(result)}个")
        return result

    def fill_small_gaps(self, timestamps: List[str]) -> List[str]:
        """
        填补小间隙（≤3秒）

        检查相邻时间戳之间的小间隙，如果≤3秒，则扩展时间戳来填补间隙

        Args:
            timestamps: 时间戳列表

        Returns:
            填补间隙后的时间戳列表
        """
        if len(timestamps) <= 1:
            return timestamps.copy()

        self.logger.info("开始填补小间隙（≤3秒）")

        result = []

        for i in range(len(timestamps)):
            current_timestamp = timestamps[i]

            if ' --> ' not in current_timestamp:
                result.append(current_timestamp)
                continue

            # 解析当前时间戳
            current_start_str, current_end_str = current_timestamp.split(' --> ')
            current_start_ms = self.timestamp_to_milliseconds(current_start_str.strip())
            current_end_ms = self.timestamp_to_milliseconds(current_end_str.strip())

            # 检查与下一个时间戳的间隙
            if i + 1 < len(timestamps):
                next_timestamp = timestamps[i + 1]

                if ' --> ' in next_timestamp:
                    next_start_str, next_end_str = next_timestamp.split(' --> ')
                    next_start_ms = self.timestamp_to_milliseconds(next_start_str.strip())

                    # 计算间隙
                    gap_ms = next_start_ms - current_end_ms
                    gap_seconds = gap_ms / 1000

                    # 如果间隙≤3秒，扩展当前时间戳来填补间隙
                    if 0 < gap_ms <= 3000:
                        # 扩展当前时间戳的结束时间到下一个时间戳的开始时间
                        extended_end_str = next_start_str.strip()
                        extended_timestamp = f"{current_start_str.strip()} --> {extended_end_str}"

                        result.append(extended_timestamp)

                        self.logger.info(f"填补小间隙（{gap_seconds:.1f}秒）:")
                        self.logger.info(f"  原始: {current_timestamp}")
                        self.logger.info(f"  扩展: {extended_timestamp}")
                    else:
                        # 间隙太大，不填补
                        result.append(current_timestamp)
                else:
                    result.append(current_timestamp)
            else:
                # 最后一个时间戳
                result.append(current_timestamp)

        self.logger.info(f"小间隙填补完成")
        return result

    def merge_nearly_continuous_timestamps(self, timestamps: List[str]) -> List[str]:
        """
        合并几乎连续的时间戳

        策略：如果两个时间戳的间隔≤100毫秒，则合并为一个时间戳
        改进：不仅检查相邻时间戳，还检查所有可能的连续组合

        Args:
            timestamps: 时间戳列表

        Returns:
            合并后的时间戳列表
        """
        if len(timestamps) <= 1:
            return timestamps.copy()

        self.logger.info("开始合并几乎连续的时间戳（间隔≤100毫秒）")

        # 解析所有时间戳
        parsed_timestamps = []
        for i, timestamp in enumerate(timestamps):
            if ' --> ' in timestamp:
                start_str, end_str = timestamp.split(' --> ')
                start_ms = self.timestamp_to_milliseconds(start_str.strip())
                end_ms = self.timestamp_to_milliseconds(end_str.strip())
                parsed_timestamps.append({
                    'index': i,
                    'timestamp': timestamp,
                    'start_ms': start_ms,
                    'end_ms': end_ms,
                    'start_str': start_str.strip(),
                    'end_str': end_str.strip(),
                    'merged': False
                })
            else:
                parsed_timestamps.append({
                    'index': i,
                    'timestamp': timestamp,
                    'start_ms': None,
                    'end_ms': None,
                    'merged': False
                })

        # 查找可以合并的相邻时间戳对（更保守的策略）
        merge_groups = []

        for i in range(len(parsed_timestamps) - 1):
            if parsed_timestamps[i]['merged'] or parsed_timestamps[i]['start_ms'] is None:
                continue
            if parsed_timestamps[i + 1]['merged'] or parsed_timestamps[i + 1]['start_ms'] is None:
                continue

            current = parsed_timestamps[i]
            next_ts = parsed_timestamps[i + 1]

            # 计算间隔（只检查相邻的时间戳）
            gap_ms = next_ts['start_ms'] - current['end_ms']

            # 如果间隔≤100毫秒，可以合并
            if 0 <= gap_ms <= 100:
                merge_groups.append([i, i + 1])
                # 标记为已合并
                parsed_timestamps[i]['merged'] = True
                parsed_timestamps[i + 1]['merged'] = True

        # 执行合并
        result = []

        for i, parsed in enumerate(parsed_timestamps):
            if parsed['merged']:
                continue

            # 检查是否属于某个合并组
            in_merge_group = False
            for group in merge_groups:
                if i == group[0]:  # 是合并组的第一个
                    # 合并整个组
                    group_timestamps = [parsed_timestamps[idx] for idx in group]

                    earliest_start_ms = min(ts['start_ms'] for ts in group_timestamps)
                    latest_end_ms = max(ts['end_ms'] for ts in group_timestamps)

                    merged_start_str = self.milliseconds_to_timestamp(earliest_start_ms)
                    merged_end_str = self.milliseconds_to_timestamp(latest_end_ms)
                    merged_timestamp = f"{merged_start_str} --> {merged_end_str}"

                    result.append(merged_timestamp)

                    # 计算间隔用于日志
                    if len(group) == 2:
                        gap_ms = group_timestamps[1]['start_ms'] - group_timestamps[0]['end_ms']
                        self.logger.info(f"合并几乎连续的时间戳（间隔{gap_ms}毫秒）:")
                        self.logger.info(f"  原始: {group_timestamps[0]['timestamp']}")
                        self.logger.info(f"  原始: {group_timestamps[1]['timestamp']}")
                        self.logger.info(f"  合并结果: {merged_timestamp}")

                    in_merge_group = True
                    break

            if not in_merge_group:
                result.append(parsed['timestamp'])

        self.logger.info(f"几乎连续时间戳合并完成，从{len(timestamps)}个合并为{len(result)}个，减少了{len(timestamps) - len(result)}个")
        return result

    def sort_timestamps_by_time(self, timestamps: List[str]) -> List[str]:
        """
        按时间顺序排序时间戳

        Args:
            timestamps: 原始时间戳列表

        Returns:
            按时间排序的时间戳列表
        """
        if not timestamps:
            return timestamps

        self.logger.info("开始按时间顺序排序时间戳")

        # 解析时间戳并排序
        parsed_timestamps = []
        for i, timestamp in enumerate(timestamps):
            if ' --> ' in timestamp:
                start_str, end_str = timestamp.split(' --> ')
                start_ms = self.timestamp_to_milliseconds(start_str.strip())
                parsed_timestamps.append({
                    'original_index': i + 1,
                    'start_ms': start_ms,
                    'timestamp': timestamp
                })
            else:
                self.logger.warning(f"无效时间戳格式: {timestamp}")

        # 按开始时间排序
        parsed_timestamps.sort(key=lambda x: x['start_ms'])

        # 提取排序后的时间戳
        sorted_timestamps = [item['timestamp'] for item in parsed_timestamps]

        # 记录排序信息
        self.logger.info(f"时间戳排序完成，{len(timestamps)}个时间戳已按时间顺序重新排列")

        # 记录排序前后的对比（前5个）
        for i, item in enumerate(parsed_timestamps[:5]):
            original_index = item['original_index']
            start_time_str = item['timestamp'].split(' --> ')[0]
            self.logger.debug(f"排序后第{i+1}个: 原第{original_index}个时间戳 ({start_time_str})")

        if len(parsed_timestamps) > 5:
            self.logger.debug(f"... (还有{len(parsed_timestamps) - 5}个)")

        return sorted_timestamps

    def apply_final_timestamp_expansion(self, blocks: List[List[int]], original_timestamps: List[str]) -> List[str]:
        """
        最终时间戳扩展算法：
        给每个匹配成功的段落，前后增加空字幕的时间戳

        规则：
        - 有字幕的序号：前后各可以加1次
        - 无字幕的序号：前后各可以加3次
        - 从前往后/从后往前检查，遇到有字幕就最多加1次后停止，遇到无字幕可以继续加到3次
        - 避免序号重复使用和时间重叠

        Args:
            blocks: 原始区块列表
            original_timestamps: 原始时间戳列表

        Returns:
            扩展后的时间戳列表
        """
        self.logger.info("开始应用最终时间戳扩展算法")
        expanded_timestamps = []
        used_sequences = set()  # 记录已使用的序号，避免重复

        for i, (block_sequences, original_timestamp) in enumerate(zip(blocks, original_timestamps)):
            if not block_sequences:
                expanded_timestamps.append(original_timestamp)
                continue

            actual_sequences = sorted(block_sequences)
            min_seq = min(actual_sequences)
            max_seq = max(actual_sequences)

            # 前向扩展：有字幕的前面可以加1次，无字幕的可以加3次
            front_expansion_seqs = []
            for j in range(1, 4):  # 最多扩展3个
                candidate_seq = min_seq - j
                if candidate_seq >= 1 and candidate_seq in self.file_b_entries:
                    entry = self.file_b_entries[candidate_seq]
                    is_empty = not bool(entry.normalized_text.strip())

                    if is_empty:
                        front_expansion_seqs.append(candidate_seq)
                        self.logger.debug(f"前向扩展: 序号{candidate_seq}为空字幕，添加")
                    else:
                        # 有字幕的只能加1次
                        if j == 1:
                            front_expansion_seqs.append(candidate_seq)
                            self.logger.debug(f"前向扩展: 序号{candidate_seq}有字幕，添加1次后停止")
                        else:
                            self.logger.debug(f"前向扩展: 序号{candidate_seq}有字幕，停止扩展")
                        break
                else:
                    self.logger.debug(f"前向扩展: 序号{candidate_seq}不存在，停止扩展")
                    break

            # 后向扩展：有字幕的后面可以加1次，无字幕的可以加3次
            back_expansion_seqs = []
            for j in range(1, 4):  # 最多扩展3个
                candidate_seq = max_seq + j
                if candidate_seq in self.file_b_entries:
                    entry = self.file_b_entries[candidate_seq]
                    is_empty = not bool(entry.normalized_text.strip())

                    if is_empty:
                        back_expansion_seqs.append(candidate_seq)
                        self.logger.debug(f"后向扩展: 序号{candidate_seq}为空字幕，添加")
                    else:
                        # 有字幕的只能加1次
                        if j == 1:
                            back_expansion_seqs.append(candidate_seq)
                            self.logger.debug(f"后向扩展: 序号{candidate_seq}有字幕，添加1次后停止")
                        else:
                            self.logger.debug(f"后向扩展: 序号{candidate_seq}有字幕，停止扩展")
                        break
                else:
                    self.logger.debug(f"后向扩展: 序号{candidate_seq}不存在，停止扩展")
                    break

            # 计算扩展后的序号范围
            all_expansion_seqs = front_expansion_seqs + actual_sequences + back_expansion_seqs

            if front_expansion_seqs or back_expansion_seqs:
                # 有扩展，重新计算时间戳
                expanded_min_seq = min(all_expansion_seqs)
                expanded_max_seq = max(all_expansion_seqs)

                # 获取扩展后的开始和结束时间
                start_time = self.file_b_entries[expanded_min_seq].start_time
                original_end_time = self.file_b_entries[expanded_max_seq].end_time
                end_time = self.adjust_end_time(original_end_time, -1)

                expanded_timestamp = f"{start_time} --> {end_time}"
                expanded_timestamps.append(expanded_timestamp)

                # 记录扩展信息
                front_count = len(front_expansion_seqs)
                back_count = len(back_expansion_seqs)
                total_expansion = front_count + back_count

                self.logger.info(f"区块{i+1}: 序号{min_seq}-{max_seq}，最终扩展了{total_expansion}个空字幕序号（前{front_count}个，后{back_count}个）")
                self.logger.debug(f"  原始时间戳: {original_timestamp}")
                self.logger.debug(f"  扩展时间戳: {expanded_timestamp}")

                if front_expansion_seqs:
                    self.logger.debug(f"  前向扩展序号: {sorted(front_expansion_seqs)}")
                if back_expansion_seqs:
                    self.logger.debug(f"  后向扩展序号: {sorted(back_expansion_seqs)}")
            else:
                # 无扩展，保持原时间戳
                expanded_timestamps.append(original_timestamp)
                self.logger.debug(f"区块{i+1}: 序号{min_seq}-{max_seq}，无法进行最终扩展")

        # 统计扩展效果
        expanded_count = sum(1 for orig, exp in zip(original_timestamps, expanded_timestamps) if orig != exp)
        expansion_rate = (expanded_count / len(original_timestamps)) * 100 if original_timestamps else 0

        self.logger.info(f"最终时间戳扩展完成，{len(original_timestamps)}个区块中有{expanded_count}个被扩展（扩展率: {expansion_rate:.1f}%）")

        return expanded_timestamps

    def apply_timestamp_merging(self, timestamps: List[str]) -> List[str]:
        """
        时间戳智能合并算法：
        检测相邻或重叠的时间戳，将它们合并成更大的连续区间

        合并条件：
        1. 时间戳重叠（A的结束时间 >= B的开始时间）
        2. 时间戳相邻（A的结束时间 + 容差 >= B的开始时间）

        Args:
            timestamps: 原始时间戳列表

        Returns:
            合并后的时间戳列表
        """
        if not timestamps:
            return timestamps

        self.logger.info("开始应用时间戳智能合并算法")

        # 解析时间戳并排序
        parsed_timestamps = []
        for i, timestamp in enumerate(timestamps):
            if ' --> ' in timestamp:
                start_str, end_str = timestamp.split(' --> ')
                start_ms = self.timestamp_to_milliseconds(start_str.strip())
                end_ms = self.timestamp_to_milliseconds(end_str.strip())
                parsed_timestamps.append({
                    'index': i,
                    'start_ms': start_ms,
                    'end_ms': end_ms,
                    'start_str': start_str.strip(),
                    'end_str': end_str.strip(),
                    'original': timestamp
                })

        # 保持原始顺序，不按时间排序（保持台词的逻辑顺序）
        # parsed_timestamps.sort(key=lambda x: x['start_ms'])

        # 合并重叠或相邻的时间戳
        merged_groups = []
        current_group = [parsed_timestamps[0]]

        tolerance_ms = 500  # 500毫秒容差，认为是相邻的
        max_duration_ms = 2 * 60 * 1000  # 最大合并时长：2分钟

        for i in range(1, len(parsed_timestamps)):
            current = parsed_timestamps[i]
            last_in_group = current_group[-1]

            # 检查是否可以合并
            # 条件1：重叠（当前开始时间 <= 上一个结束时间）
            # 条件2：相邻（当前开始时间 <= 上一个结束时间 + 容差）
            # 条件3：合并后的总时长不超过最大限制

            # 计算合并后的总时长
            group_start_ms = min(item['start_ms'] for item in current_group)
            potential_end_ms = max(current['end_ms'], max(item['end_ms'] for item in current_group))
            potential_duration_ms = potential_end_ms - group_start_ms

            can_merge_by_time = current['start_ms'] <= last_in_group['end_ms'] + tolerance_ms
            can_merge_by_duration = potential_duration_ms <= max_duration_ms

            if can_merge_by_time and can_merge_by_duration:
                # 可以合并
                current_group.append(current)
                self.logger.debug(f"将时间戳{current['index']+1}合并到组{len(merged_groups)+1}: {current['original']}")
            else:
                # 不能合并，开始新组
                if not can_merge_by_time:
                    self.logger.debug(f"时间戳{current['index']+1}因时间间隔过大无法合并")
                elif not can_merge_by_duration:
                    self.logger.debug(f"时间戳{current['index']+1}因合并后时长过长无法合并（{potential_duration_ms/1000:.1f}秒 > {max_duration_ms/1000}秒）")

                merged_groups.append(current_group)
                current_group = [current]

        # 添加最后一组
        merged_groups.append(current_group)

        # 生成合并后的时间戳
        merged_timestamps = []
        for group in merged_groups:
            if len(group) == 1:
                # 单个时间戳，保持原样
                merged_timestamps.append(group[0]['original'])
            else:
                # 多个时间戳，合并为一个
                earliest_start = min(item['start_ms'] for item in group)
                latest_end = max(item['end_ms'] for item in group)

                start_str = self.milliseconds_to_timestamp(earliest_start)
                end_str = self.milliseconds_to_timestamp(latest_end)

                merged_timestamp = f"{start_str} --> {end_str}"
                merged_timestamps.append(merged_timestamp)

                # 记录合并信息
                original_indices = [item['index'] for item in group]
                original_timestamps = [item['original'] for item in group]

                self.logger.info(f"合并了{len(group)}个时间戳:")
                for j, orig in enumerate(original_timestamps):
                    self.logger.debug(f"  原始{original_indices[j]+1}: {orig}")
                self.logger.info(f"  合并结果: {merged_timestamp}")

        # 统计合并效果
        original_count = len(timestamps)
        merged_count = len(merged_timestamps)
        reduction = original_count - merged_count

        if reduction > 0:
            self.logger.info(f"时间戳智能合并完成，从{original_count}个减少到{merged_count}个，合并了{reduction}个重叠/相邻时间戳")
        else:
            self.logger.info(f"时间戳智能合并完成，没有发现可合并的时间戳")

        return merged_timestamps

    def timestamp_to_milliseconds(self, timestamp_str: str) -> int:
        """
        将时间戳字符串转换为毫秒数

        Args:
            timestamp_str: 时间戳字符串，格式如 "00:01:23,456"

        Returns:
            毫秒数
        """
        try:
            # 分离时间和毫秒部分
            if ',' in timestamp_str:
                time_part, ms_part = timestamp_str.split(',')
                ms = int(ms_part)
            else:
                time_part = timestamp_str
                ms = 0

            # 解析时:分:秒
            time_components = time_part.split(':')
            hours = int(time_components[0])
            minutes = int(time_components[1])
            seconds = int(time_components[2])

            # 转换为总毫秒数
            total_ms = (hours * 3600 + minutes * 60 + seconds) * 1000 + ms
            return total_ms

        except Exception as e:
            self.logger.error(f"时间戳解析错误: {timestamp_str} - {e}")
            return 0

    def milliseconds_to_timestamp(self, ms: int) -> str:
        """
        将毫秒数转换为时间戳字符串

        Args:
            ms: 毫秒数

        Returns:
            时间戳字符串，格式如 "00:01:23,456"
        """
        try:
            # 提取毫秒部分
            milliseconds = ms % 1000
            total_seconds = ms // 1000

            # 提取秒、分、时
            seconds = total_seconds % 60
            total_minutes = total_seconds // 60
            minutes = total_minutes % 60
            hours = total_minutes // 60

            # 格式化为字符串
            return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

        except Exception as e:
            self.logger.error(f"毫秒转换错误: {ms} - {e}")
            return "00:00:00,000"

    def process_files(self, file_a_path: str, file_b_path: str, auto_save: bool = True, max_time_gap: float = 8.0) -> Tuple[bool, List[str], str]:
        """
        处理两个文件并生成最终结果
        返回: (成功标志, 时间戳列表, 错误信息)
        """
        self.logger.info("开始处理文件")
        self.logger.info(f"文件A路径: {file_a_path}")
        self.logger.info(f"文件B路径: {file_b_path}")

        try:
            # 解析文件A
            if not self.parse_subtitle_file(file_a_path, is_file_a=True):
                error_msg = "解析文件A失败"
                self.logger.error(error_msg)
                return False, [], error_msg

            # 解析文件B
            if not self.parse_subtitle_file(file_b_path, is_file_a=False):
                error_msg = "解析文件B失败"
                self.logger.error(error_msg)
                return False, [], error_msg

            # 执行匹配
            self.perform_advanced_matching()

            # 生成最终时间戳
            timestamps = self.generate_final_timestamps(max_time_gap)

            # 自动保存结果
            if auto_save and timestamps:
                saved_file = self.auto_save_timestamps(timestamps)
                if saved_file:
                    self.logger.info(f"结果已自动保存到: {saved_file}")

            self.logger.info("文件处理完成")
            self.logger.info("=" * 60)

            return True, timestamps, ""

        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.logger.exception("详细错误信息:")
            return False, [], error_msg

    def auto_save_timestamps(self, timestamps: List[str]) -> str:
        """自动保存时间戳到文件"""
        try:
            filename = "匹配结果.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("\n".join(timestamps))

            self.logger.info(f"时间戳结果已保存到: {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"自动保存时间戳失败: {e}")
            return ""

    def get_detailed_results(self) -> Dict:
        """获取详细的匹配结果，用于调试和分析"""
        return {
            'file_a_count': len(self.file_a_entries),
            'file_b_count': len(self.file_b_entries),
            'match_results': [
                {
                    'text': result.final_text,
                    'matched_sequences': result.matched_sequences,
                    'match_type': 'single' if len(result.matched_sequences) <= 1 else 'multi'
                }
                for result in self.match_results
            ],
            'blocks': self.create_blocks_and_expand()
        }
