#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
字幕与时间戳合并工具
功能：
1. 合并带起始字幕的文件和分镜时间戳文件
2. 根据时间戳匹配对应的字幕内容
3. 处理一个字幕占用多个分镜的情况（合并时间戳）
4. 删除没有文字的时间戳，并将时间平均分配给前后时间戳
"""

import re
import logging
import datetime
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass


@dataclass
class SubtitleEntry:
    """字幕条目"""
    index: int
    start_time: float  # 改为浮点数（秒）
    end_time: float    # 改为浮点数（秒）
    text: str


@dataclass
class TimestampEntry:
    """时间戳条目"""
    index: int
    start_time: str
    end_time: str
    text: str


class SubtitleTimestampMerger:
    """字幕与时间戳合并器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.subtitle_entries: List[SubtitleEntry] = []
        self.timestamp_entries: List[TimestampEntry] = []
        self.merged_results: List[SubtitleEntry] = []
    
    def _setup_logger(self):
        """设置日志记录器"""
        log_filename = "字幕合并日志.log"
        
        logger = logging.getLogger('SubtitleTimestampMerger')
        logger.setLevel(logging.DEBUG)
        
        if logger.handlers:
            logger.handlers.clear()
        
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        logger.info("=" * 60)
        logger.info("字幕与时间戳合并工具启动")
        logger.info(f"日志文件: {log_filename}")
        logger.info("=" * 60)
        
        return logger
    
    def parse_subtitle_file(self, file_path: str) -> bool:
        """解析带起始字幕的文件"""
        try:
            self.logger.info(f"开始解析字幕文件: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 分割字幕条目
            entries = re.split(r'\n\s*\n', content.strip())
            
            for entry in entries:
                lines = entry.strip().split('\n')
                if len(lines) >= 3:
                    try:
                        index = int(lines[0])
                        time_line = lines[1]
                        text = '\n'.join(lines[2:]).strip()
                        
                        # 解析时间戳
                        time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
                        if time_match:
                            start_time_str = time_match.group(1)
                            end_time_str = time_match.group(2)

                            # 转换为秒数
                            start_time = self.parse_timestamp_to_seconds(start_time_str)
                            end_time = self.parse_timestamp_to_seconds(end_time_str)

                            subtitle_entry = SubtitleEntry(
                                index=index,
                                start_time=start_time,
                                end_time=end_time,
                                text=text
                            )
                            self.subtitle_entries.append(subtitle_entry)
                    
                    except (ValueError, IndexError) as e:
                        self.logger.warning(f"跳过无效的字幕条目: {entry[:50]}... 错误: {e}")
            
            self.logger.info(f"成功解析字幕文件，共{len(self.subtitle_entries)}个条目")
            return True
            
        except Exception as e:
            self.logger.error(f"解析字幕文件失败: {e}")
            return False
    
    def parse_timestamp_file(self, file_path: str) -> bool:
        """解析分镜时间戳文件（支持多种格式）"""
        try:
            self.logger.info(f"开始解析时间戳文件: {file_path}")

            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 检测文件格式
            first_line = lines[0].strip() if lines else ""

            if re.match(r'\d{3}\s+\d+:\d{2}\.\d{3}-\d+:\d{2}\.\d{3}', first_line):
                # 格式：001 0:00.000-0:02.233
                self.logger.info("检测到简化时间戳格式")
                return self._parse_simple_timestamp_format(lines)
            else:
                # 标准SRT格式
                self.logger.info("检测到标准SRT格式")
                return self._parse_srt_timestamp_format(lines)

        except Exception as e:
            self.logger.error(f"解析时间戳文件失败: {e}")
            return False

    def _parse_simple_timestamp_format(self, lines: list) -> bool:
        """解析简化时间戳格式：001 0:00.000-0:02.233"""
        try:
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 解析格式：001 0:00.000-0:02.233 或 1000 39:14.200-39:15.767
                match = re.match(r'(\d+)\s+(\d+):(\d{2})\.(\d{3})-(\d+):(\d{2})\.(\d{3})', line)
                if match:
                    index = int(match.group(1))

                    # 转换时间格式
                    start_min, start_sec, start_ms = int(match.group(2)), int(match.group(3)), int(match.group(4))
                    end_min, end_sec, end_ms = int(match.group(5)), int(match.group(6)), int(match.group(7))

                    start_time = start_min * 60 + start_sec + start_ms / 1000.0
                    end_time = end_min * 60 + end_sec + end_ms / 1000.0

                    # 创建时间戳条目（没有文本内容）
                    timestamp_entry = TimestampEntry(
                        index=index,
                        start_time=start_time,
                        end_time=end_time,
                        text=""  # 简化格式没有文本内容
                    )

                    self.timestamp_entries.append(timestamp_entry)
                else:
                    self.logger.warning(f"跳过无效的时间戳条目: {line}")

            self.logger.info(f"成功解析时间戳文件，共{len(self.timestamp_entries)}个条目")
            return True

        except Exception as e:
            self.logger.error(f"解析简化时间戳格式失败: {e}")
            return False

    def _parse_srt_timestamp_format(self, lines: list) -> bool:
        """解析标准SRT时间戳格式"""
        try:
            content = ''.join(lines)
            # 分割时间戳条目
            entries = re.split(r'\n\s*\n', content.strip())

            for entry in entries:
                entry_lines = entry.strip().split('\n')
                if len(entry_lines) >= 2:
                    try:
                        index = int(entry_lines[0])
                        time_line = entry_lines[1]
                        text = '\n'.join(entry_lines[2:]).strip() if len(entry_lines) > 2 else ""

                        # 解析时间戳
                        time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
                        if time_match:
                            start_time_str = time_match.group(1)
                            end_time_str = time_match.group(2)

                            # 转换为秒数
                            start_time = self.parse_timestamp_to_seconds(start_time_str)
                            end_time = self.parse_timestamp_to_seconds(end_time_str)

                            timestamp_entry = TimestampEntry(
                                index=index,
                                start_time=start_time,
                                end_time=end_time,
                                text=text
                            )
                            self.timestamp_entries.append(timestamp_entry)

                    except (ValueError, IndexError) as e:
                        self.logger.warning(f"跳过无效的时间戳条目: {entry[:50]}... 错误: {e}")

            self.logger.info(f"成功解析时间戳文件，共{len(self.timestamp_entries)}个条目")
            return True

        except Exception as e:
            self.logger.error(f"解析SRT时间戳格式失败: {e}")
            return False
    
    def parse_timestamp_to_seconds(self, timestamp: str) -> float:
        """将时间戳转换为秒数"""
        try:
            time_part, ms_part = timestamp.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
        except Exception as e:
            self.logger.error(f"解析时间戳失败: {timestamp}, 错误: {e}")
            return 0.0
    
    def seconds_to_timestamp(self, seconds: float) -> str:
        """将秒数转换为时间戳格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def remove_empty_timestamps(self) -> List[TimestampEntry]:
        """删除没有文字的时间戳，并将时间平均分配给前后时间戳"""
        self.logger.info("开始处理空白时间戳")

        filtered_entries = []
        empty_durations = []  # 记录空白时间戳的持续时间

        for i, entry in enumerate(self.timestamp_entries):
            if not entry.text.strip():
                # 计算空白时间戳的持续时间
                start_seconds = self.parse_timestamp_to_seconds(entry.start_time)
                end_seconds = self.parse_timestamp_to_seconds(entry.end_time)
                duration = end_seconds - start_seconds
                empty_durations.append((i, duration))
                self.logger.debug(f"发现空白时间戳: 序号{entry.index}, 持续时间{duration:.3f}秒")
            else:
                filtered_entries.append(entry)

        self.logger.info(f"删除了{len(empty_durations)}个空白时间戳")

        # 将空白时间戳的时间分配给前后的时间戳
        if empty_durations and filtered_entries:
            self._redistribute_empty_time(filtered_entries, empty_durations)

        return filtered_entries

    def _redistribute_empty_time(self, entries: List[TimestampEntry], empty_durations: List[Tuple[int, float]]):
        """重新分配空白时间戳的时间"""
        total_empty_time = sum(duration for _, duration in empty_durations)

        if not entries:
            return

        # 平均分配给所有非空时间戳
        time_per_entry = total_empty_time / len(entries)

        self.logger.info(f"将{total_empty_time:.3f}秒空白时间平均分配给{len(entries)}个时间戳")

        for entry in entries:
            start_seconds = self.parse_timestamp_to_seconds(entry.start_time)
            end_seconds = self.parse_timestamp_to_seconds(entry.end_time)

            # 延长结束时间
            new_end_seconds = end_seconds + time_per_entry
            entry.end_time = self.seconds_to_timestamp(new_end_seconds)

            self.logger.debug(f"时间戳{entry.index}: {entry.start_time} --> {entry.end_time} (延长{time_per_entry:.3f}秒)")

    def find_matching_timestamps(self, subtitle: SubtitleEntry, timestamps: List[TimestampEntry]) -> List[TimestampEntry]:
        """找到与字幕时间重叠的时间戳"""
        subtitle_start = self.parse_timestamp_to_seconds(subtitle.start_time)
        subtitle_end = self.parse_timestamp_to_seconds(subtitle.end_time)

        matching_timestamps = []

        for timestamp in timestamps:
            timestamp_start = self.parse_timestamp_to_seconds(timestamp.start_time)
            timestamp_end = self.parse_timestamp_to_seconds(timestamp.end_time)

            # 检查时间重叠
            if self._time_overlap(subtitle_start, subtitle_end, timestamp_start, timestamp_end):
                matching_timestamps.append(timestamp)

        return matching_timestamps

    def _time_overlap(self, start1: float, end1: float, start2: float, end2: float) -> bool:
        """检查两个时间段是否重叠"""
        return not (end1 <= start2 or end2 <= start1)

    def merge_timestamps(self, timestamps: List[TimestampEntry]) -> Tuple[str, str]:
        """合并多个时间戳，返回合并后的开始和结束时间"""
        if not timestamps:
            return "", ""

        if len(timestamps) == 1:
            return timestamps[0].start_time, timestamps[0].end_time

        # 找到最早的开始时间和最晚的结束时间
        start_times = [self.parse_timestamp_to_seconds(ts.start_time) for ts in timestamps]
        end_times = [self.parse_timestamp_to_seconds(ts.end_time) for ts in timestamps]

        earliest_start = min(start_times)
        latest_end = max(end_times)

        return self.seconds_to_timestamp(earliest_start), self.seconds_to_timestamp(latest_end)

    def merge_subtitles_and_timestamps(self) -> bool:
        """执行字幕与时间戳的合并"""
        try:
            self.logger.info("开始合并字幕与时间戳")

            # 1. 删除空白时间戳
            filtered_timestamps = self.remove_empty_timestamps()

            # 2. 为每个字幕找到匹配的时间戳
            self.merged_results = []

            for i, subtitle in enumerate(self.subtitle_entries):
                matching_timestamps = self.find_matching_timestamps(subtitle, filtered_timestamps)

                if matching_timestamps:
                    # 合并时间戳
                    merged_start, merged_end = self.merge_timestamps(matching_timestamps)

                    merged_entry = SubtitleEntry(
                        index=i + 1,
                        start_time=merged_start,
                        end_time=merged_end,
                        text=subtitle.text
                    )
                    self.merged_results.append(merged_entry)

                    timestamp_indices = [ts.index for ts in matching_timestamps]
                    self.logger.debug(f"字幕{subtitle.index}: '{subtitle.text[:20]}...' 匹配到时间戳{timestamp_indices}")
                else:
                    # 没有匹配的时间戳，保持原始时间
                    merged_entry = SubtitleEntry(
                        index=i + 1,
                        start_time=subtitle.start_time,
                        end_time=subtitle.end_time,
                        text=subtitle.text
                    )
                    self.merged_results.append(merged_entry)
                    self.logger.warning(f"字幕{subtitle.index}: '{subtitle.text[:20]}...' 没有找到匹配的时间戳")

            self.logger.info(f"合并完成，生成{len(self.merged_results)}个合并条目")
            return True

        except Exception as e:
            self.logger.error(f"合并过程中发生错误: {e}")
            return False

    def save_merged_result(self, output_path: str = "合并结果.txt") -> bool:
        """保存合并结果"""
        try:
            self.logger.info(f"开始保存合并结果到: {output_path}")

            with open(output_path, 'w', encoding='utf-8') as f:
                for entry in self.merged_results:
                    f.write(f"{entry.index}\n")
                    f.write(f"{entry.start_time} --> {entry.end_time}\n")
                    # 将多行字幕合并为单行，用空格连接
                    text = entry.text.replace('\n', ' ').strip()
                    f.write(f"{text}\n\n")

            self.logger.info(f"合并结果已保存到: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存合并结果失败: {e}")
            return False

    def process_files(self, subtitle_file: str, timestamp_file: str, output_file: str = "合并结果.txt") -> Tuple[bool, str]:
        """处理文件的主要方法"""
        try:
            self.logger.info("开始处理文件")
            self.logger.info(f"字幕文件: {subtitle_file}")
            self.logger.info(f"时间戳文件: {timestamp_file}")
            self.logger.info(f"输出文件: {output_file}")

            # 1. 解析字幕文件
            if not self.parse_subtitle_file(subtitle_file):
                return False, "解析字幕文件失败"

            # 2. 解析时间戳文件
            if not self.parse_timestamp_file(timestamp_file):
                return False, "解析时间戳文件失败"

            # 3. 保存结果（直接保存，不进行匹配和合并）
            if not self.save_simple_result(output_file):
                return False, "保存结果失败"

            self.logger.info("文件处理完成")
            self.logger.info("=" * 60)

            return True, "处理完成"

        except Exception as e:
            error_msg = f"处理文件时发生错误: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def collision_merge(self) -> Tuple[int, int]:
        """执行碰撞合并逻辑"""
        merged_count = 0
        multi_shot_count = 0

        try:
            for subtitle in self.subtitle_entries:
                # 查找与字幕起始时间碰撞的分镜时间戳
                colliding_timestamps = []

                for timestamp in self.timestamp_entries:
                    # 检查字幕的起始时间是否在分镜时间戳范围内
                    if timestamp.start_time <= subtitle.start_time <= timestamp.end_time:
                        colliding_timestamps.append(timestamp)

                if colliding_timestamps:
                    merged_count += 1
                    if len(colliding_timestamps) > 1:
                        multi_shot_count += 1
                        self.logger.debug(f"字幕 '{subtitle.text[:20]}...' 碰撞到 {len(colliding_timestamps)} 个分镜")

            return merged_count, multi_shot_count

        except Exception as e:
            self.logger.error(f"碰撞合并失败: {e}")
            return 0, 0

    def save_simple_result(self, output_file: str) -> bool:
        """保存碰撞合并结果，纯净格式输出"""
        try:
            # 执行碰撞合并并获取详细结果
            merged_results, merged_count, multi_shot_count = self.collision_merge_with_details()

            with open(output_file, 'w', encoding='utf-8') as f:
                # 写入纯净的合并结果（标准SRT格式，无统计信息）
                for i, result in enumerate(merged_results, 1):
                    f.write(f"{i}\n")
                    f.write(f"{result['start_time']} --> {result['end_time']}\n")
                    # 将多行字幕合并为单行，用空格连接
                    text = result['text'].replace('\n', ' ').strip()
                    f.write(f"{text}\n")
                    f.write("\n")

            self.logger.info(f"碰撞合并完成")
            self.logger.info(f"合并结果数量：{merged_count}")
            self.logger.info(f"多个镜头的数量：{multi_shot_count}")
            self.logger.info(f"总输出条目数：{len(merged_results)}")
            self.logger.info(f"结果已保存到: {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
            return False

    def collision_merge_with_details(self) -> Tuple[list, int, int]:
        """执行碰撞合并逻辑并返回详细结果"""
        merged_results = []
        merged_count = 0
        multi_shot_count = 0
        used_timestamp_indices = set()

        try:
            # 第一步：以字幕为驱动，找到每个字幕碰撞的时间戳
            for subtitle in self.subtitle_entries:
                colliding_timestamps = []
                colliding_indices = []

                # 找到与这个字幕碰撞的所有时间戳（检查时间范围重叠）
                for i, timestamp in enumerate(self.timestamp_entries):
                    # 检查字幕时间范围与时间戳范围是否有重叠
                    # 重叠条件：字幕开始时间 < 时间戳结束时间 AND 字幕结束时间 > 时间戳开始时间
                    if (subtitle.start_time < timestamp.end_time and
                        subtitle.end_time > timestamp.start_time):
                        colliding_timestamps.append(timestamp)
                        colliding_indices.append(i)

                if colliding_timestamps:
                    merged_count += 1

                    # 标记这些时间戳已被使用
                    used_timestamp_indices.update(colliding_indices)

                    # 如果一个字幕碰撞到多个时间戳，合并时间戳范围
                    min_start = min(ts.start_time for ts in colliding_timestamps)
                    max_end = max(ts.end_time for ts in colliding_timestamps)

                    # 格式化时间戳
                    start_time_str = self.seconds_to_timestamp(min_start)
                    end_time_str = self.seconds_to_timestamp(max_end)

                    if len(colliding_timestamps) > 1:
                        multi_shot_count += 1

                    merged_results.append({
                        'start_time': start_time_str,
                        'end_time': end_time_str,
                        'text': subtitle.text,
                        'type': 'subtitle',
                        'original_start': subtitle.start_time  # 用于排序
                    })

                    self.logger.debug(f"字幕 '{subtitle.text[:20]}...' 碰撞到{len(colliding_timestamps)}个时间戳")

            # 第二步：添加没有被字幕碰撞的时间戳
            for i, timestamp in enumerate(self.timestamp_entries):
                if i not in used_timestamp_indices:
                    start_time_str = self.seconds_to_timestamp(timestamp.start_time)
                    end_time_str = self.seconds_to_timestamp(timestamp.end_time)

                    merged_results.append({
                        'start_time': start_time_str,
                        'end_time': end_time_str,
                        'text': '',
                        'type': 'empty',
                        'original_start': timestamp.start_time  # 用于排序
                    })

            # 按原始时间排序
            merged_results.sort(key=lambda x: x['original_start'])

            # 移除排序用的字段
            for result in merged_results:
                del result['original_start']

            # 合并相同时间戳的条目（处理多个字幕碰撞到相同时间戳范围的情况）
            merged_results = self.merge_same_timestamps(merged_results)

            # 合并重叠的时间戳范围
            merged_results = self.merge_overlapping_timestamps(merged_results)

            return merged_results, merged_count, multi_shot_count

        except Exception as e:
            self.logger.error(f"碰撞合并失败: {e}")
            return [], 0, 0

    def timestamp_to_seconds(self, timestamp_str: str) -> float:
        """将时间戳字符串转换为秒数"""
        try:
            # 格式：HH:MM:SS,mmm
            time_part, ms_part = timestamp_str.split(',')
            h, m, s = map(int, time_part.split(':'))
            ms = int(ms_part)
            return h * 3600 + m * 60 + s + ms / 1000.0
        except:
            return 0.0

    def merge_same_timestamps(self, results: list) -> list:
        """合并相同时间戳和连续时间戳的字幕"""
        if not results:
            return results

        merged = []
        current_group = [results[0]]

        for i in range(1, len(results)):
            current = results[i]
            previous = results[i-1]

            # 只合并完全相同的时间戳
            should_merge = (
                current['start_time'] == previous['start_time'] and
                current['end_time'] == previous['end_time']
            )

            if should_merge:
                current_group.append(current)
            else:
                # 处理当前组
                if len(current_group) > 1:
                    # 合并多个字幕
                    merged_text = []
                    for item in current_group:
                        if item['text'].strip():  # 只添加非空字幕
                            merged_text.append(item['text'].strip())

                    # 计算合并后的时间范围
                    start_time = current_group[0]['start_time']
                    end_time = current_group[-1]['end_time']

                    merged_item = {
                        'start_time': start_time,
                        'end_time': end_time,
                        'text': ' '.join(merged_text) if merged_text else '',
                        'type': current_group[0]['type']
                    }
                    merged.append(merged_item)
                else:
                    # 单个字幕直接添加
                    merged.append(current_group[0])

                # 开始新组
                current_group = [current]

        # 处理最后一组
        if len(current_group) > 1:
            merged_text = []
            for item in current_group:
                if item['text'].strip():
                    merged_text.append(item['text'].strip())

            # 计算合并后的时间范围
            start_time = current_group[0]['start_time']
            end_time = current_group[-1]['end_time']

            merged_item = {
                'start_time': start_time,
                'end_time': end_time,
                'text': ' '.join(merged_text) if merged_text else '',
                'type': current_group[0]['type']
            }
            merged.append(merged_item)
        else:
            merged.append(current_group[0])

        return merged

    def merge_overlapping_timestamps(self, results: list) -> list:
        """合并重叠的时间戳范围"""
        if not results:
            return results

        # 只处理有字幕内容的条目，空白条目不参与重叠合并
        subtitle_results = [r for r in results if r['type'] == 'subtitle']
        empty_results = [r for r in results if r['type'] == 'empty']

        if not subtitle_results:
            return results

        # 按开始时间排序
        subtitle_results.sort(key=lambda x: self.timestamp_to_seconds(x['start_time']))

        merged = []
        current_group = [subtitle_results[0]]

        for i in range(1, len(subtitle_results)):
            current = subtitle_results[i]
            previous = subtitle_results[i-1]

            # 检查时间戳范围是否重叠
            current_start = self.timestamp_to_seconds(current['start_time'])
            current_end = self.timestamp_to_seconds(current['end_time'])
            previous_start = self.timestamp_to_seconds(previous['start_time'])
            previous_end = self.timestamp_to_seconds(previous['end_time'])

            # 重叠条件：当前开始时间 < 前一个结束时间
            if current_start < previous_end:
                current_group.append(current)
            else:
                # 处理当前组
                if len(current_group) > 1:
                    # 合并重叠的时间戳
                    all_texts = []
                    min_start_seconds = float('inf')
                    max_end_seconds = 0

                    for item in current_group:
                        if item['text'].strip():
                            all_texts.append(item['text'].strip())
                        start_sec = self.timestamp_to_seconds(item['start_time'])
                        end_sec = self.timestamp_to_seconds(item['end_time'])
                        min_start_seconds = min(min_start_seconds, start_sec)
                        max_end_seconds = max(max_end_seconds, end_sec)

                    # 去重文本（保持顺序）
                    unique_texts = []
                    for text in all_texts:
                        if text not in unique_texts:
                            unique_texts.append(text)

                    merged_item = {
                        'start_time': self.seconds_to_timestamp(min_start_seconds),
                        'end_time': self.seconds_to_timestamp(max_end_seconds),
                        'text': ' '.join(unique_texts),
                        'type': 'subtitle'
                    }
                    merged.append(merged_item)
                else:
                    # 单个条目直接添加
                    merged.append(current_group[0])

                # 开始新组
                current_group = [current]

        # 处理最后一组
        if len(current_group) > 1:
            all_texts = []
            min_start_seconds = float('inf')
            max_end_seconds = 0

            for item in current_group:
                if item['text'].strip():
                    all_texts.append(item['text'].strip())
                start_sec = self.timestamp_to_seconds(item['start_time'])
                end_sec = self.timestamp_to_seconds(item['end_time'])
                min_start_seconds = min(min_start_seconds, start_sec)
                max_end_seconds = max(max_end_seconds, end_sec)

            # 去重文本（保持顺序）
            unique_texts = []
            for text in all_texts:
                if text not in unique_texts:
                    unique_texts.append(text)

            merged_item = {
                'start_time': self.seconds_to_timestamp(min_start_seconds),
                'end_time': self.seconds_to_timestamp(max_end_seconds),
                'text': ' '.join(unique_texts),
                'type': 'subtitle'
            }
            merged.append(merged_item)
        else:
            merged.append(current_group[0])

        # 合并有字幕的条目和空白条目，然后按时间排序
        all_results = merged + empty_results
        all_results.sort(key=lambda x: self.timestamp_to_seconds(x['start_time']))

        return all_results

    def seconds_to_timestamp(self, seconds: float) -> str:
        """将秒数转换为时间戳格式 HH:MM:SS,mmm"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def _generate_statistics(self) -> str:
        """生成统计信息"""
        stats = []
        stats.append("=" * 40)
        stats.append("合并统计信息")
        stats.append("=" * 40)
        stats.append(f"原始字幕条目数: {len(self.subtitle_entries)}")
        stats.append(f"原始时间戳条目数: {len(self.timestamp_entries)}")
        stats.append(f"合并后条目数: {len(self.merged_results)}")

        # 计算空白时间戳数量
        empty_count = sum(1 for ts in self.timestamp_entries if not ts.text.strip())
        stats.append(f"删除的空白时间戳: {empty_count}")

        # 计算匹配统计
        matched_count = 0
        for subtitle in self.subtitle_entries:
            filtered_timestamps = [ts for ts in self.timestamp_entries if ts.text.strip()]
            matching_timestamps = self.find_matching_timestamps(subtitle, filtered_timestamps)
            if matching_timestamps:
                matched_count += 1

        match_rate = (matched_count / len(self.subtitle_entries)) * 100 if self.subtitle_entries else 0
        stats.append(f"匹配成功率: {match_rate:.1f}%")
        stats.append("=" * 40)

        return "\n".join(stats)


if __name__ == "__main__":
    # 测试代码
    merger = SubtitleTimestampMerger()

    # 示例文件路径
    subtitle_file = "字幕与时间戳合并/带起始字幕.txt"
    timestamp_file = "字幕与时间戳合并/分镜时间戳.txt"
    output_file = "字幕与时间戳合并/合并结果.txt"

    success, result = merger.process_files(subtitle_file, timestamp_file, output_file)

    if success:
        print("处理成功！")
        print(result)
    else:
        print(f"处理失败: {result}")
