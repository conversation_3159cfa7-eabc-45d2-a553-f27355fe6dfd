#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的字幕合并修复测试
"""

from subtitle_timestamp_merger import SubtitleTimestampMerger
import os

def test_multiline_fix():
    """测试多行字幕修复"""
    print("=" * 60)
    print("测试多行字幕修复")
    print("=" * 60)
    
    # 创建测试字幕文件（标准SRT格式）
    test_subtitle_content = """1
00:00:00,000 --> 00:00:03,000
单行字幕测试

2
00:00:03,000 --> 00:00:06,000
多行字幕测试第一行
多行字幕测试第二行

3
00:00:06,000 --> 00:00:10,000
三行字幕测试第一行
三行字幕测试第二行
三行字幕测试第三行"""
    
    # 创建测试时间戳文件（标准SRT格式）
    test_timestamp_content = """1
00:00:00,000 --> 00:00:03,000

2
00:00:03,000 --> 00:00:06,000

3
00:00:06,000 --> 00:00:10,000
"""
    
    # 保存测试文件
    test_subtitle_file = "test_subtitle.srt"
    test_timestamp_file = "test_timestamp.srt"
    
    with open(test_subtitle_file, 'w', encoding='utf-8') as f:
        f.write(test_subtitle_content)
    
    with open(test_timestamp_file, 'w', encoding='utf-8') as f:
        f.write(test_timestamp_content)
    
    print("📋 测试文件已创建")
    
    # 创建合并器并执行合并
    merger = SubtitleTimestampMerger()
    
    # 解析文件
    subtitle_success = merger.parse_subtitle_file(test_subtitle_file)
    timestamp_success = merger.parse_timestamp_file(test_timestamp_file)
    
    print(f"\n📊 解析结果:")
    print(f"  字幕文件解析: {'✅' if subtitle_success else '❌'}")
    print(f"  时间戳文件解析: {'✅' if timestamp_success else '❌'}")
    
    if subtitle_success and timestamp_success:
        print(f"  解析的字幕数量: {len(merger.subtitle_entries)}")
        print(f"  解析的时间戳数量: {len(merger.timestamp_entries)}")
        
        # 显示解析的字幕内容
        print(f"\n📋 解析的字幕内容:")
        for i, entry in enumerate(merger.subtitle_entries, 1):
            has_newline = '\n' in entry.text
            print(f"  {i}. '{entry.text}' (包含换行符: {'是' if has_newline else '否'})")
        
        # 执行合并
        output_file = "test_merged_output.txt"
        merge_success = merger.save_simple_result(output_file)
        
        print(f"\n📊 合并结果:")
        print(f"  合并执行: {'✅' if merge_success else '❌'}")
        
        if merge_success and os.path.exists(output_file):
            # 检查输出结果
            with open(output_file, 'r', encoding='utf-8') as f:
                result_content = f.read()
            
            print(f"\n📋 输出内容:")
            print(result_content)
            
            # 验证是否还有多行字幕
            lines = result_content.split('\n')
            multiline_found = False
            subtitle_lines = []
            
            for line in lines:
                # 跳过序号行和时间戳行
                if line.strip() and not line.strip().isdigit() and ' --> ' not in line:
                    subtitle_lines.append(line)
                    if '\n' in line:
                        multiline_found = True
            
            print(f"\n🔍 验证结果:")
            print(f"  找到字幕行数: {len(subtitle_lines)}")
            print(f"  包含多行字幕: {'是' if multiline_found else '否'}")
            
            for i, line in enumerate(subtitle_lines, 1):
                print(f"  字幕 {i}: '{line}'")
            
            # 清理测试文件
            try:
                os.remove(test_subtitle_file)
                os.remove(test_timestamp_file)
                os.remove(output_file)
                print(f"\n🧹 测试文件已清理")
            except:
                pass
            
            return not multiline_found
        else:
            print("❌ 合并失败或输出文件不存在")
            return False
    else:
        print("❌ 文件解析失败")
        return False

def test_direct_text_processing():
    """直接测试文本处理逻辑"""
    print("\n" + "=" * 60)
    print("直接测试文本处理逻辑")
    print("=" * 60)
    
    # 测试文本处理
    test_texts = [
        "单行文本",
        "多行文本第一行\n多行文本第二行",
        "三行文本第一行\n三行文本第二行\n三行文本第三行",
        "包含空行的文本\n\n第三行",
        "\n开头有换行符",
        "结尾有换行符\n"
    ]
    
    print("📋 测试文本处理:")
    all_correct = True
    
    for i, original_text in enumerate(test_texts, 1):
        # 应用修复后的处理逻辑
        processed_text = original_text.replace('\n', ' ').strip()
        
        print(f"\n  测试 {i}:")
        print(f"    原文: '{original_text}'")
        print(f"    处理后: '{processed_text}'")
        
        # 验证处理结果
        has_newline = '\n' in processed_text
        if has_newline:
            print(f"    结果: ❌ 仍包含换行符")
            all_correct = False
        else:
            print(f"    结果: ✅ 已移除换行符")
    
    return all_correct

def main():
    """主测试函数"""
    print("🧪 字幕合并修复简化测试")
    print("=" * 60)
    
    # 测试直接文本处理
    text_processing_success = test_direct_text_processing()
    
    # 测试完整合并流程
    merger_success = test_multiline_fix()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 文本处理逻辑: {'通过' if text_processing_success else '失败'}")
    print(f"✅ 完整合并流程: {'通过' if merger_success else '失败'}")
    
    if text_processing_success and merger_success:
        print("\n🎉 字幕合并修复成功！")
        print("📋 修复效果:")
        print("1. ✅ 文本处理逻辑正确：使用 text.replace('\\n', ' ').strip()")
        print("2. ✅ 输出格式正确：所有字幕都合并为单行")
        print("3. ✅ 内容完整性：多行内容用空格连接")
        print("4. ✅ 符合标准：输出符合SRT格式要求")
    elif text_processing_success:
        print("\n⚠️ 文本处理逻辑正确，但合并流程有问题")
        print("💡 可能是测试文件格式或解析逻辑的问题")
    else:
        print("\n❌ 修复失败，需要进一步调试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
