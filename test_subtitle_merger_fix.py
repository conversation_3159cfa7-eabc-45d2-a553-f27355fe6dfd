#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字幕与时间戳合并功能修复
"""

from subtitle_timestamp_merger import SubtitleTimestampMerger
import os

def test_multiline_subtitle_merging():
    """测试多行字幕合并修复"""
    print("=" * 60)
    print("测试多行字幕合并修复")
    print("=" * 60)
    
    # 创建测试字幕文件（包含多行字幕）
    test_subtitle_content = """1
00:00:00,000 --> 00:00:03,000
单行字幕测试

2
00:00:03,000 --> 00:00:06,000
多行字幕测试第一行
多行字幕测试第二行

3
00:00:06,000 --> 00:00:10,000
三行字幕测试第一行
三行字幕测试第二行
三行字幕测试第三行

4
00:00:10,000 --> 00:00:12,000
包含特殊符号的多行字幕
如：引号"测试"、括号（测试）等"""
    
    # 创建测试时间戳文件
    test_timestamp_content = """1 0:00.000-0:03.000
2 0:03.000-0:06.000
3 0:06.000-0:10.000
4 0:10.000-0:12.000"""
    
    # 保存测试文件
    test_subtitle_file = "test_subtitle_multiline.txt"
    test_timestamp_file = "test_timestamp_multiline.txt"
    
    with open(test_subtitle_file, 'w', encoding='utf-8') as f:
        f.write(test_subtitle_content)
    
    with open(test_timestamp_file, 'w', encoding='utf-8') as f:
        f.write(test_timestamp_content)
    
    print("📋 测试文件已创建")
    print(f"  字幕文件: {test_subtitle_file}")
    print(f"  时间戳文件: {test_timestamp_file}")
    
    # 创建合并器并执行合并
    merger = SubtitleTimestampMerger()
    
    # 解析文件
    subtitle_success = merger.parse_subtitle_file(test_subtitle_file)
    timestamp_success = merger.parse_timestamp_file(test_timestamp_file)
    
    print(f"\n📊 解析结果:")
    print(f"  字幕文件解析: {'✅' if subtitle_success else '❌'}")
    print(f"  时间戳文件解析: {'✅' if timestamp_success else '❌'}")
    
    if subtitle_success and timestamp_success:
        # 执行合并
        output_file = "test_merged_result.txt"
        merge_success = merger.save_simple_result(output_file)
        
        print(f"  合并执行: {'✅' if merge_success else '❌'}")
        
        if merge_success and os.path.exists(output_file):
            # 检查输出结果
            with open(output_file, 'r', encoding='utf-8') as f:
                result_content = f.read()
            
            print(f"\n📋 合并结果:")
            print(result_content[:500] + "..." if len(result_content) > 500 else result_content)
            
            # 验证是否还有多行字幕
            lines = result_content.split('\n')
            multiline_found = False
            
            for i, line in enumerate(lines):
                # 跳过序号行和时间戳行
                if line.strip() and not line.strip().isdigit() and ' --> ' not in line:
                    # 这是字幕内容行
                    if '\n' in line:
                        multiline_found = True
                        print(f"❌ 发现多行字幕: 第{i+1}行")
                        break
            
            if not multiline_found:
                print("✅ 所有字幕都已合并为单行")
                
                # 检查具体的合并效果
                expected_results = [
                    "单行字幕测试",
                    "多行字幕测试第一行 多行字幕测试第二行",
                    "三行字幕测试第一行 三行字幕测试第二行 三行字幕测试第三行",
                    "包含特殊符号的多行字幕 如：引号\"测试\"、括号（测试）等"
                ]
                
                print(f"\n🔍 验证合并效果:")
                all_correct = True
                
                for i, expected in enumerate(expected_results, 1):
                    # 在结果中查找对应的字幕
                    found = False
                    for line in lines:
                        if line.strip() == expected:
                            found = True
                            break
                    
                    if found:
                        print(f"  字幕 {i}: ✅")
                    else:
                        print(f"  字幕 {i}: ❌ 期望: '{expected}'")
                        all_correct = False
                
                # 清理测试文件
                try:
                    os.remove(test_subtitle_file)
                    os.remove(test_timestamp_file)
                    os.remove(output_file)
                    print(f"\n🧹 测试文件已清理")
                except:
                    pass
                
                return all_correct
            else:
                print("❌ 仍然存在多行字幕")
                return False
        else:
            print("❌ 合并失败或输出文件不存在")
            return False
    else:
        print("❌ 文件解析失败")
        return False

def test_real_subtitle_merger():
    """测试真实字幕文件的合并"""
    print("\n" + "=" * 60)
    print("测试真实字幕文件的合并")
    print("=" * 60)
    
    # 检查是否有真实的测试文件
    test_files = [
        ("测试字幕文件.txt", "测试时间戳文件.txt"),
        ("字幕文件.srt", "时间戳文件.txt"),
    ]
    
    found_files = False
    for subtitle_file, timestamp_file in test_files:
        if os.path.exists(subtitle_file) and os.path.exists(timestamp_file):
            found_files = True
            print(f"📁 找到测试文件:")
            print(f"  字幕文件: {subtitle_file}")
            print(f"  时间戳文件: {timestamp_file}")
            
            # 创建合并器
            merger = SubtitleTimestampMerger()
            
            # 解析文件
            subtitle_success = merger.parse_subtitle_file(subtitle_file)
            timestamp_success = merger.parse_timestamp_file(timestamp_file)
            
            print(f"\n📊 解析结果:")
            print(f"  字幕文件解析: {'✅' if subtitle_success else '❌'}")
            print(f"  时间戳文件解析: {'✅' if timestamp_success else '❌'}")
            
            if subtitle_success and timestamp_success:
                # 执行合并
                output_file = f"real_merged_result_{subtitle_file.replace('.', '_')}.txt"
                merge_success = merger.save_simple_result(output_file)
                
                print(f"  合并执行: {'✅' if merge_success else '❌'}")
                
                if merge_success:
                    print(f"  输出文件: {output_file}")
                    return True
            
            break
    
    if not found_files:
        print("📋 未找到真实测试文件，跳过此测试")
        return True  # 跳过不算失败
    
    return False

def test_edge_cases():
    """测试边缘情况"""
    print("\n" + "=" * 60)
    print("测试边缘情况")
    print("=" * 60)
    
    # 测试空字幕
    test_cases = [
        {
            "name": "空字幕处理",
            "subtitle": """1
00:00:00,000 --> 00:00:02,000

2
00:00:02,000 --> 00:00:04,000
正常字幕""",
            "timestamp": "1 0:00.000-0:02.000\n2 0:02.000-0:04.000"
        },
        {
            "name": "只有换行符的字幕",
            "subtitle": """1
00:00:00,000 --> 00:00:02,000


2
00:00:02,000 --> 00:00:04,000
正常字幕""",
            "timestamp": "1 0:00.000-0:02.000\n2 0:02.000-0:04.000"
        },
        {
            "name": "包含多个空行的字幕",
            "subtitle": """1
00:00:00,000 --> 00:00:02,000
第一行

第三行

第五行""",
            "timestamp": "1 0:00.000-0:02.000"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {test_case['name']}")
        
        # 创建临时文件
        subtitle_file = f"test_edge_subtitle_{i}.txt"
        timestamp_file = f"test_edge_timestamp_{i}.txt"
        output_file = f"test_edge_result_{i}.txt"
        
        with open(subtitle_file, 'w', encoding='utf-8') as f:
            f.write(test_case['subtitle'])
        
        with open(timestamp_file, 'w', encoding='utf-8') as f:
            f.write(test_case['timestamp'])
        
        # 执行合并
        merger = SubtitleTimestampMerger()
        subtitle_success = merger.parse_subtitle_file(subtitle_file)
        timestamp_success = merger.parse_timestamp_file(timestamp_file)
        
        if subtitle_success and timestamp_success:
            merge_success = merger.save_simple_result(output_file)
            
            if merge_success and os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    result = f.read()
                
                # 检查是否有多行字幕
                has_multiline = any('\n' in line for line in result.split('\n') 
                                  if line.strip() and not line.strip().isdigit() and ' --> ' not in line)
                
                if not has_multiline:
                    print(f"  ✅ 通过")
                else:
                    print(f"  ❌ 仍有多行字幕")
                    all_passed = False
            else:
                print(f"  ❌ 合并失败")
                all_passed = False
        else:
            print(f"  ❌ 解析失败")
            all_passed = False
        
        # 清理临时文件
        try:
            os.remove(subtitle_file)
            os.remove(timestamp_file)
            if os.path.exists(output_file):
                os.remove(output_file)
        except:
            pass
    
    return all_passed

def main():
    """主测试函数"""
    print("🧪 字幕与时间戳合并功能修复验证")
    print("=" * 60)
    
    # 测试多行字幕合并
    multiline_success = test_multiline_subtitle_merging()
    
    # 测试真实文件合并
    real_file_success = test_real_subtitle_merger()
    
    # 测试边缘情况
    edge_case_success = test_edge_cases()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 多行字幕合并: {'通过' if multiline_success else '失败'}")
    print(f"✅ 真实文件合并: {'通过' if real_file_success else '失败'}")
    print(f"✅ 边缘情况处理: {'通过' if edge_case_success else '失败'}")
    
    if multiline_success and real_file_success and edge_case_success:
        print("\n🎉 字幕与时间戳合并功能修复成功！")
        print("📋 修复效果:")
        print("1. 解决了多行字幕输出的问题")
        print("2. 现在所有字幕都会合并为单行输出")
        print("3. 使用空格连接多行内容")
        print("4. 保持字幕内容的完整性")
        print("5. 输出格式符合标准SRT格式")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
