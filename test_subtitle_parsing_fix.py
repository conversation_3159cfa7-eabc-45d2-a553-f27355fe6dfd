#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字幕解析修复
"""

from subtitle_matcher import SubtitleMatcher

def test_multiline_parsing():
    """测试多行字幕解析修复"""
    print("=" * 60)
    print("测试多行字幕解析修复")
    print("=" * 60)
    
    # 创建测试字幕内容（包含多行台词）
    test_content = """1
00:00:00,000 --> 00:00:02,000
单行台词测试

2
00:00:02,000 --> 00:00:05,000
多行台词测试第一行
多行台词测试第二行

3
00:00:05,000 --> 00:00:08,000
三行台词测试第一行
三行台词测试第二行
三行台词测试第三行

4
00:00:08,000 --> 00:00:10,000

5
00:00:10,000 --> 00:00:12,000
空台词后的正常台词"""
    
    print("📋 测试字幕内容:")
    print(test_content)
    
    # 创建匹配器并解析
    matcher = SubtitleMatcher()
    
    # 直接调用解析方法
    success = matcher._parse_file_b(test_content)
    
    print(f"\n📊 解析结果:")
    print(f"  解析成功: {'✅' if success else '❌'}")
    print(f"  解析条目数: {len(matcher.file_b_entries)}")
    
    # 显示解析的条目
    for seq, entry in sorted(matcher.file_b_entries.items()):
        print(f"\n  序号 {seq}:")
        print(f"    时间: {entry.start_time} --> {entry.end_time}")
        print(f"    原文: '{entry.text}'")
        print(f"    规范化: '{entry.normalized_text}'")
    
    # 验证多行合并是否正确
    expected_results = {
        1: "单行台词测试",
        2: "多行台词测试第一行 多行台词测试第二行",
        3: "三行台词测试第一行 三行台词测试第二行 三行台词测试第三行",
        5: "空台词后的正常台词"
    }
    
    print(f"\n🔍 验证结果:")
    all_correct = True
    for seq, expected_text in expected_results.items():
        if seq in matcher.file_b_entries:
            actual_text = matcher.file_b_entries[seq].text
            is_correct = actual_text == expected_text
            print(f"  序号 {seq}: {'✅' if is_correct else '❌'}")
            if not is_correct:
                print(f"    期望: '{expected_text}'")
                print(f"    实际: '{actual_text}'")
                all_correct = False
        else:
            print(f"  序号 {seq}: ❌ (未找到)")
            all_correct = False
    
    return all_correct

def test_real_subtitle_files():
    """测试真实字幕文件的解析"""
    print("\n" + "=" * 60)
    print("测试真实字幕文件的解析")
    print("=" * 60)
    
    # 创建匹配器
    matcher = SubtitleMatcher()
    
    # 解析原始素材文件
    file_b_path = "D:/二次剪辑与原视频匹配/原视频匹配素材/原素材.txt"
    
    print(f"📁 解析文件: {file_b_path}")
    
    import os
    if not os.path.exists(file_b_path):
        print("❌ 文件不存在")
        return False
    
    success = matcher.parse_subtitle_file(file_b_path, is_file_a=False)
    
    print(f"\n📊 解析结果:")
    print(f"  解析成功: {'✅' if success else '❌'}")
    print(f"  解析条目数: {len(matcher.file_b_entries)}")
    
    # 检查之前失败的台词是否现在能找到
    test_cases = [
        ("妈妈答应过你再也不让你受这种离别之苦", "序号640"),
        ("我魂穿到了这同名同姓的少女身上", "序号21"),
        ("现在最核心的保险柜密码应该是", "序号181"),
    ]
    
    print(f"\n🔍 检查之前失败的台词:")
    found_count = 0
    
    for target_text, description in test_cases:
        found_sequences = []
        
        for seq, entry in matcher.file_b_entries.items():
            if target_text in entry.text:
                found_sequences.append(seq)
        
        if found_sequences:
            print(f"  ✅ '{target_text[:20]}...' 找到于序号: {found_sequences}")
            found_count += 1
            
            # 显示找到的完整内容
            for seq in found_sequences[:1]:  # 只显示第一个
                print(f"      完整内容: '{matcher.file_b_entries[seq].text}'")
        else:
            print(f"  ❌ '{target_text[:20]}...' 未找到")
    
    print(f"\n📊 检查结果: {found_count}/{len(test_cases)} 个台词找到")
    
    return found_count == len(test_cases)

def test_matching_improvement():
    """测试匹配改进效果"""
    print("\n" + "=" * 60)
    print("测试匹配改进效果")
    print("=" * 60)
    
    # 创建匹配器
    matcher = SubtitleMatcher()
    
    # 文件路径
    file_a_path = "D:/二次剪辑与原视频匹配/二次剪辑素材/二次剪辑.txt"
    file_b_path = "D:/二次剪辑与原视频匹配/原视频匹配素材/原素材.txt"
    
    print(f"📁 文件A: {file_a_path}")
    print(f"📁 文件B: {file_b_path}")
    
    # 检查文件是否存在
    import os
    if not os.path.exists(file_a_path) or not os.path.exists(file_b_path):
        print("❌ 文件不存在")
        return False
    
    # 执行匹配
    try:
        success, timestamps, error_msg = matcher.process_files(
            file_a_path, 
            file_b_path, 
            auto_save=False,
            max_time_gap=30.0
        )
        
        if success:
            print(f"\n✅ 匹配成功！")
            
            # 统计匹配结果
            successful_matches = sum(1 for result in matcher.match_results if result.matched_sequences)
            total_entries = len(matcher.file_a_entries)
            match_rate = (successful_matches / total_entries) * 100 if total_entries > 0 else 0
            
            print(f"📊 匹配统计:")
            print(f"  总台词数: {total_entries}")
            print(f"  成功匹配: {successful_matches}")
            print(f"  匹配成功率: {match_rate:.1f}%")
            print(f"  生成时间戳: {len(timestamps)}")
            
            # 检查之前失败的台词现在是否匹配成功
            previously_failed = [
                "妈妈答应过你再也不让你受这种离别之苦了",
                "我魂穿到了这同名同姓的少女身上",
                "现在最核心的保险柜密码应该是 694Ω7",
            ]
            
            print(f"\n🔍 检查之前失败的台词:")
            improved_count = 0
            
            for target_text in previously_failed:
                # 查找这个台词在匹配结果中
                found_match = False
                for result in matcher.match_results:
                    if target_text in result.target_text and result.matched_sequences:
                        found_match = True
                        print(f"  ✅ '{target_text[:30]}...' 现在匹配到序号: {result.matched_sequences}")
                        improved_count += 1
                        break
                
                if not found_match:
                    print(f"  ❌ '{target_text[:30]}...' 仍然匹配失败")
            
            print(f"\n📊 改进效果: {improved_count}/{len(previously_failed)} 个之前失败的台词现在匹配成功")
            
            return match_rate > 89.0  # 期望匹配率有所提高
            
        else:
            print(f"❌ 匹配失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ 匹配过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 字幕解析修复验证")
    print("=" * 60)
    
    # 测试多行解析
    multiline_success = test_multiline_parsing()
    
    # 测试真实文件解析
    real_file_success = test_real_subtitle_files()
    
    # 测试匹配改进
    matching_success = test_matching_improvement()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 多行解析: {'通过' if multiline_success else '失败'}")
    print(f"✅ 真实文件解析: {'通过' if real_file_success else '失败'}")
    print(f"✅ 匹配改进: {'通过' if matching_success else '失败'}")
    
    if multiline_success and real_file_success and matching_success:
        print("\n🎉 字幕解析修复成功！")
        print("📋 修复效果:")
        print("1. 解决了多行台词只读取第一行的问题")
        print("2. 现在能正确合并多行台词内容")
        print("3. 提高了字幕匹配的成功率")
        print("4. 之前失败的台词现在能正确匹配")
        print("5. 完全解决了台词分割导致的匹配失败")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
