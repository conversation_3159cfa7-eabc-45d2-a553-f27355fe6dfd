#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试连续时间轴修复
"""

from video_track_writer import VideoTrackWriter, MatchResult

def test_continuous_timeline():
    """测试连续时间轴功能"""
    print("=" * 60)
    print("测试连续时间轴功能")
    print("=" * 60)
    
    # 创建不连续的时间戳（模拟实际导入的情况）
    discontinuous_matches = [
        MatchResult(
            main_segment_name="segment_001",
            main_start_time=2013.531,  # 原始时间戳（不连续）
            main_end_time=2026.567,
            second_segment_name="imported_segment",
            second_start_time=2013.531,  # 源时间戳
            second_end_time=2026.567,
            confidence=1.0,
            method="手动导入"
        ),
        MatchResult(
            main_segment_name="segment_002",
            main_start_time=2035.133,  # 原始时间戳（不连续）
            main_end_time=2066.132,
            second_segment_name="imported_segment",
            second_start_time=2035.133,  # 源时间戳
            second_end_time=2066.132,
            confidence=1.0,
            method="手动导入"
        ),
        MatchResult(
            main_segment_name="segment_003",
            main_start_time=140.900,   # 原始时间戳（不连续）
            main_end_time=171.000,
            second_segment_name="imported_segment",
            second_start_time=140.900,   # 源时间戳
            second_end_time=171.000,
            confidence=1.0,
            method="手动导入"
        )
    ]
    
    print("📊 不连续的时间戳测试数据:")
    for i, match in enumerate(discontinuous_matches, 1):
        duration = match.second_end_time - match.second_start_time
        print(f"  {i}. 原始时间: {match.main_start_time:.3f}s-{match.main_end_time:.3f}s")
        print(f"     源时间: {match.second_start_time:.3f}s-{match.second_end_time:.3f}s (时长: {duration:.3f}s)")
    
    # 生成第三轨道JSON
    test_material_id = "test_material_123"
    track_json = VideoTrackWriter._generate_third_video_track_json(discontinuous_matches, test_material_id)
    
    print(f"\n🎬 生成的轨道JSON片段:")
    
    # 分析生成的时间轴位置
    import re
    target_timeranges = re.findall(r'"target_timerange": \{\s*"duration": (\d+),\s*"start": (\d+)', track_json)
    
    print(f"\n📊 时间轴分析:")
    print(f"  找到 {len(target_timeranges)} 个片段")
    
    expected_start = 0
    all_continuous = True
    
    for i, (duration_str, start_str) in enumerate(target_timeranges):
        duration_us = int(duration_str)
        start_us = int(start_str)
        
        duration_s = duration_us / 1000000
        start_s = start_us / 1000000
        end_s = start_s + duration_s
        
        print(f"  片段 {i+1}: 时间轴 {start_s:.3f}s-{end_s:.3f}s (时长: {duration_s:.3f}s)")
        
        # 检查是否连续
        if abs(start_s - expected_start) > 0.001:  # 允许微小的浮点误差
            print(f"    ❌ 不连续！期望开始时间: {expected_start:.3f}s，实际: {start_s:.3f}s")
            all_continuous = False
        else:
            print(f"    ✅ 连续")
        
        expected_start = end_s
    
    print(f"\n🎯 连续性检查结果:")
    if all_continuous:
        print("✅ 所有片段都是连续的！")
        print(f"📊 总时长: {expected_start:.3f}秒")
        return True
    else:
        print("❌ 片段之间有间隔")
        return False

def test_original_vs_fixed():
    """对比修复前后的差异"""
    print("\n" + "=" * 60)
    print("对比修复前后的差异")
    print("=" * 60)
    
    # 模拟修复前的逻辑（使用原始时间戳）
    print("📋 修复前的逻辑（有间隔）:")
    original_timestamps = [2013.531, 2035.133, 140.900]
    for i, timestamp in enumerate(original_timestamps, 1):
        print(f"  片段 {i}: 时间轴位置 {timestamp:.3f}s")
    
    # 修复后的逻辑（连续时间轴）
    print("\n📋 修复后的逻辑（连续）:")
    durations = [13.036, 30.999, 30.100]  # 对应的时长
    current_pos = 0.0
    for i, duration in enumerate(durations, 1):
        print(f"  片段 {i}: 时间轴位置 {current_pos:.3f}s-{current_pos + duration:.3f}s")
        current_pos += duration
    
    print(f"\n🎯 对比结果:")
    print("  修复前: 片段之间有大量间隔，不连续")
    print("  修复后: 片段紧密连接，连续播放")
    print(f"  总时长: {current_pos:.3f}秒")

def test_with_real_timestamps():
    """使用真实的时间戳数据测试"""
    print("\n" + "=" * 60)
    print("使用真实时间戳数据测试")
    print("=" * 60)
    
    # 真实的时间戳数据（从用户提供的数据）
    real_timestamps = [
        (2013.531, 2026.567),  # 13.036s
        (2035.133, 2066.132),  # 30.999s
        (140.900, 171.000),    # 30.100s
        (187.599, 224.366),    # 36.767s
        (253.233, 272.567)     # 19.334s
    ]
    
    # 转换为MatchResult
    real_matches = []
    for i, (start, end) in enumerate(real_timestamps):
        match = MatchResult(
            main_segment_name=f"segment_{i+1:03d}",
            main_start_time=start,
            main_end_time=end,
            second_segment_name="imported_segment",
            second_start_time=start,
            second_end_time=end,
            confidence=1.0,
            method="手动导入"
        )
        real_matches.append(match)
    
    print("📊 真实时间戳数据:")
    total_duration = 0
    for i, (start, end) in enumerate(real_timestamps, 1):
        duration = end - start
        total_duration += duration
        print(f"  {i}. {start:.3f}s-{end:.3f}s (时长: {duration:.3f}s)")
    
    print(f"\n📊 原始数据统计:")
    print(f"  片段数量: {len(real_timestamps)}")
    print(f"  总时长: {total_duration:.3f}秒")
    print(f"  时间跨度: {min(start for start, _ in real_timestamps):.3f}s - {max(end for _, end in real_timestamps):.3f}s")
    
    # 生成连续时间轴
    print(f"\n🎬 生成连续时间轴:")
    current_pos = 0.0
    for i, (start, end) in enumerate(real_timestamps, 1):
        duration = end - start
        print(f"  片段 {i}: {current_pos:.3f}s-{current_pos + duration:.3f}s (源: {start:.3f}s-{end:.3f}s)")
        current_pos += duration
    
    print(f"\n✅ 连续时间轴总时长: {current_pos:.3f}秒")
    
    return real_matches

def main():
    """主测试函数"""
    print("🧪 连续时间轴修复验证")
    print("=" * 60)
    
    # 测试连续时间轴功能
    continuous_success = test_continuous_timeline()
    
    # 对比修复前后
    test_original_vs_fixed()
    
    # 使用真实数据测试
    real_matches = test_with_real_timestamps()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"✅ 连续时间轴: {'通过' if continuous_success else '失败'}")
    
    if continuous_success:
        print("\n🎉 连续时间轴修复成功！")
        print("📋 修复说明:")
        print("1. 使用连续的时间轴位置，而不是原始时间戳")
        print("2. 每个片段紧接着上一个片段")
        print("3. 时间轴位置从0开始，累加片段时长")
        print("4. 完全按照原项目VideoTrackWriter.java的逻辑")
        print("5. 现在导入的片段将连续播放，没有间隔")
    else:
        print("\n❌ 连续时间轴修复失败，需要进一步调试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
