#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试轨道左对齐功能集成
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_unified_gui_import():
    """测试统一GUI导入"""
    try:
        from unified_gui import UnifiedGUI
        print("✅ 成功导入 UnifiedGUI")
        return True
    except Exception as e:
        print(f"❌ 导入 UnifiedGUI 失败: {e}")
        return False

def test_track_align_methods():
    """测试轨道对齐方法是否存在"""
    try:
        from unified_gui import UnifiedGUI
        
        # 检查必要的方法是否存在
        required_methods = [
            'create_track_align_tab',
            'browse_align_project_folder',
            'refresh_align_projects',
            'analyze_align_tracks',
            'execute_track_align',
            '_calculate_track_gaps',
            '_align_single_track'
        ]
        
        for method_name in required_methods:
            if hasattr(UnifiedGUI, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                print(f"❌ 方法缺失: {method_name}")
                return False
        
        print("✅ 所有必要方法都存在")
        return True
        
    except Exception as e:
        print(f"❌ 测试方法失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建（不显示）"""
    try:
        import tkinter as tk
        from unified_gui import UnifiedGUI
        
        # 创建隐藏的根窗口进行测试
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 临时替换UnifiedGUI的root
        original_init = UnifiedGUI.__init__
        
        def test_init(self):
            self.root = root
            self.root.title("智能字幕处理工具集")
            self.root.geometry("900x800")
            
            # 设置中文字体
            self.default_font = ("Microsoft YaHei", 10)
            self.root.option_add("*Font", self.default_font)
            
            # 初始化变量
            self.setup_variables()
            
            # 设置日志
            self.setup_logger()
            
            # 检查是否有轨道对齐相关变量
            if hasattr(self, 'align_project_folder'):
                print("✅ 轨道对齐变量已初始化")
            else:
                print("❌ 轨道对齐变量未初始化")
        
        UnifiedGUI.__init__ = test_init
        
        # 创建GUI实例
        app = UnifiedGUI()
        
        # 恢复原始初始化方法
        UnifiedGUI.__init__ = original_init
        
        # 销毁测试窗口
        root.destroy()
        
        print("✅ GUI创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试轨道左对齐功能集成")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_unified_gui_import),
        ("方法存在性测试", test_track_align_methods),
        ("GUI创建测试", test_gui_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ 测试失败: {test_name}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！轨道左对齐功能已成功集成到字幕工具中")
    else:
        print("⚠️ 部分测试失败，请检查集成代码")

if __name__ == "__main__":
    main()
