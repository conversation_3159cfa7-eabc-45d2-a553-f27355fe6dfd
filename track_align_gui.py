#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剪映轨道左对齐GUI工具 - 集成到项目的GUI界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional

class TrackAlignGUI:
    """轨道左对齐GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("剪映轨道左对齐工具")
        self.root.geometry("800x600")
        
        self.draft_path = ""
        self.draft_data = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="剪映轨道左对齐工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 草稿选择区域
        ttk.Label(main_frame, text="剪映草稿:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.draft_path_var = tk.StringVar()
        draft_entry = ttk.Entry(main_frame, textvariable=self.draft_path_var, width=60)
        draft_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=5)
        
        browse_btn = ttk.Button(main_frame, text="浏览", command=self.browse_draft)
        browse_btn.grid(row=1, column=2, padx=(5, 0), pady=5)
        
        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=20, sticky=(tk.W, tk.E))
        
        analyze_btn = ttk.Button(button_frame, text="分析轨道", command=self.analyze_tracks)
        analyze_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        align_btn = ttk.Button(button_frame, text="执行左对齐", command=self.execute_align)
        align_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        clear_btn.pack(side=tk.LEFT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def log(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def browse_draft(self):
        """浏览选择草稿文件夹"""
        folder = filedialog.askdirectory(title="选择剪映草稿文件夹")
        if folder:
            # 检查是否包含draft_content.json
            draft_file = os.path.join(folder, "draft_content.json")
            if os.path.exists(draft_file):
                self.draft_path_var.set(folder)
                self.draft_path = folder
                self.log(f"✅ 选择草稿: {folder}")
            else:
                messagebox.showerror("错误", "所选文件夹不包含有效的剪映草稿文件")
                
    def load_draft(self) -> bool:
        """加载草稿数据"""
        if not self.draft_path:
            messagebox.showerror("错误", "请先选择草稿文件夹")
            return False
            
        try:
            draft_file = os.path.join(self.draft_path, "draft_content.json")
            with open(draft_file, 'r', encoding='utf-8') as f:
                self.draft_data = json.load(f)
            return True
        except Exception as e:
            self.log(f"❌ 加载草稿失败: {e}")
            return False
            
    def analyze_tracks(self):
        """分析轨道结构"""
        self.status_var.set("分析中...")
        
        if not self.load_draft():
            self.status_var.set("加载失败")
            return
            
        try:
            tracks = self.draft_data.get('tracks', [])
            video_tracks = [track for track in tracks if track.get('type') == 'video']
            
            self.log(f"📊 草稿分析结果:")
            self.log(f"   总轨道数: {len(tracks)}")
            self.log(f"   视频轨道数: {len(video_tracks)}")
            
            for i, track in enumerate(video_tracks):
                segments = track.get('segments', [])
                gaps = self._calculate_track_gaps(segments)
                
                self.log(f"\n🎬 轨道 {i + 1}:")
                self.log(f"   片段数量: {len(segments)}")
                self.log(f"   间隙数量: {len(gaps)}")
                self.log(f"   状态: {'需要对齐' if gaps else '已对齐'}")
                
                if gaps:
                    for j, gap in enumerate(gaps):
                        gap_duration = gap['gap_duration'] / 1000000.0
                        self.log(f"     间隙{j+1}: 时长 {gap_duration:.3f}s")
            
            self.status_var.set("分析完成")
            
        except Exception as e:
            self.log(f"❌ 分析失败: {e}")
            self.status_var.set("分析失败")
            
    def _calculate_track_gaps(self, segments: List[Dict]) -> List[Dict]:
        """计算轨道间隙"""
        gaps = []
        
        if len(segments) < 2:
            return gaps
            
        # 提取并排序片段
        segment_times = []
        for i, segment in enumerate(segments):
            target_timerange = segment.get('target_timerange', {})
            start_time = target_timerange.get('start', 0)
            duration = target_timerange.get('duration', 0)
            end_time = start_time + duration
            
            segment_times.append({
                'index': i,
                'start_time': start_time,
                'end_time': end_time
            })
        
        # 按开始时间排序
        segment_times.sort(key=lambda x: x['start_time'])
        
        # 计算间隙
        for i in range(len(segment_times) - 1):
            current_end = segment_times[i]['end_time']
            next_start = segment_times[i + 1]['start_time']
            
            if next_start > current_end:
                gaps.append({
                    'after_segment': i,
                    'before_segment': i + 1,
                    'gap_duration': next_start - current_end
                })
        
        return gaps
        
    def execute_align(self):
        """执行左对齐操作"""
        if not self.draft_data:
            if not self.load_draft():
                return
                
        # 确认操作
        result = messagebox.askyesno("确认操作", 
                                   "确定要执行左对齐操作吗？\n\n"
                                   "此操作会修改草稿文件，建议先备份。")
        if not result:
            return
            
        self.status_var.set("执行中...")
        
        try:
            # 备份原始文件
            self._backup_draft()
            
            # 执行左对齐
            tracks = self.draft_data.get('tracks', [])
            video_tracks = [track for track in tracks if track.get('type') == 'video']
            
            self.log(f"\n🔧 开始执行左对齐...")
            
            success_count = 0
            for i, track in enumerate(video_tracks):
                if self._align_track(track, i):
                    success_count += 1
            
            # 保存修改后的文件
            self._save_draft()
            
            self.log(f"\n🎉 左对齐完成!")
            self.log(f"📊 成功处理: {success_count}/{len(video_tracks)} 个轨道")
            self.log(f"💡 请在剪映中重新打开项目查看效果")
            
            self.status_var.set("完成")
            messagebox.showinfo("完成", "左对齐操作已完成！\n请在剪映中重新打开项目查看效果。")
            
        except Exception as e:
            self.log(f"❌ 执行失败: {e}")
            self.status_var.set("执行失败")
            messagebox.showerror("错误", f"执行失败: {e}")
            
    def _backup_draft(self):
        """备份草稿文件"""
        draft_file = os.path.join(self.draft_path, "draft_content.json")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(self.draft_path, f"draft_content_backup_{timestamp}.json")
        
        shutil.copy2(draft_file, backup_file)
        self.log(f"✅ 备份文件: {backup_file}")
        
    def _save_draft(self):
        """保存草稿文件"""
        draft_file = os.path.join(self.draft_path, "draft_content.json")
        with open(draft_file, 'w', encoding='utf-8') as f:
            json.dump(self.draft_data, f, ensure_ascii=False, separators=(',', ':'))
        self.log(f"✅ 保存草稿: {draft_file}")
        
    def _align_track(self, track: Dict, track_index: int) -> bool:
        """对单个轨道执行左对齐"""
        segments = track.get('segments', [])
        gaps = self._calculate_track_gaps(segments)
        
        if not gaps:
            self.log(f"   轨道 {track_index + 1}: 已对齐，跳过")
            return True
            
        self.log(f"   轨道 {track_index + 1}: 发现 {len(gaps)} 个间隙，开始处理...")
        
        # 按开始时间排序片段
        segment_data = []
        for i, segment in enumerate(segments):
            target_timerange = segment.get('target_timerange', {})
            start_time = target_timerange.get('start', 0)
            
            segment_data.append({
                'original_index': i,
                'segment': segment,
                'start_time': start_time
            })
        
        segment_data.sort(key=lambda x: x['start_time'])
        
        # 计算累积偏移并应用
        cumulative_offset = 0
        
        for i, seg_data in enumerate(segment_data):
            segment = seg_data['segment']
            
            # 应用偏移
            if cumulative_offset > 0:
                target_timerange = segment.get('target_timerange', {})
                original_start = target_timerange.get('start', 0)
                new_start = max(0, original_start - cumulative_offset)
                target_timerange['start'] = new_start
                
                self.log(f"     片段{i+1}: {original_start/1000000:.3f}s -> {new_start/1000000:.3f}s")
            
            # 检查是否有间隙在此片段后
            for gap in gaps:
                if gap['after_segment'] == i:
                    cumulative_offset += gap['gap_duration']
                    break
        
        return True

def main():
    """主函数"""
    root = tk.Tk()
    app = TrackAlignGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
