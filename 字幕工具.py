#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能字幕处理工具集 - 统一启动程序
包含字幕匹配和字幕合并两个功能

使用方法：
1. 双击 启动工具.bat
2. 或直接运行 python 字幕工具.py
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def main():
    """主函数"""
    try:
        # 设置当前工作目录为脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # 添加当前目录到Python路径
        sys.path.insert(0, script_dir)
        
        print("=" * 50)
        print("智能字幕处理工具集")
        print("=" * 50)
        print("正在启动GUI界面...")
        print(f"工作目录: {script_dir}")
        
        # 导入并启动统一GUI
        from unified_gui import UnifiedGUI
        
        app = UnifiedGUI()
        print("GUI界面启动成功！")
        app.run()
        
    except ImportError as e:
        error_msg = f"导入模块失败: {e}\n请确保所有必要的文件都在同一目录下"
        print(error_msg)
        
        # 尝试显示图形错误消息
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动失败", error_msg)
        except:
            pass
        
        input("\n按回车键退出...")
        
    except Exception as e:
        error_msg = f"程序启动失败: {e}"
        print(error_msg)
        
        # 尝试显示图形错误消息
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动失败", error_msg)
        except:
            pass
        
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
